# 发布笔记公开可见功能测试场景

## 测试场景1: 基础选择功能
### 测试步骤
1. 打开发布笔记页面
2. 点击"公开可见"选项
3. 在弹出的选择页面中选择"全部可见"
4. 点击"确定"按钮

### 预期结果
- 选择页面关闭
- 主页面"公开可见"显示为"全部可见"
- 数据正确保存为"1"

## 测试场景2: 单选逻辑验证
### 测试步骤
1. 打开公开情况选择页面
2. 依次点击不同的选项
3. 观察选中状态变化

### 预期结果
- 每次只能选中一个选项
- 点击新选项时，之前选中的选项自动取消选中
- 选中状态正确显示

## 测试场景3: 黑名单选择流程
### 测试步骤
1. 打开公开情况选择页面
2. 选择"不给谁看"
3. 自动跳转到好友选择页面
4. 选择几个好友
5. 点击确定

### 预期结果
- 正确跳转到好友选择页面
- 页面标题显示"不给谁看"
- 选择好友后底部显示选中数量
- 返回主页面后显示"不给谁看：用户1, 用户2"

## 测试场景4: 白名单选择流程
### 测试步骤
1. 打开公开情况选择页面
2. 选择"只给谁看"
3. 自动跳转到好友选择页面
4. 选择几个好友
5. 点击确定

### 预期结果
- 正确跳转到好友选择页面
- 页面标题显示"只给谁看"
- 选择好友后底部显示选中数量
- 返回主页面后显示"只给谁看：用户1, 用户2"

## 测试场景5: 错误处理验证
### 测试步骤1: 未选择任何选项
1. 打开公开情况选择页面
2. 不选择任何选项，直接点击"确定"

### 预期结果
- 显示提示"请选择公开情况"
- 页面不关闭

### 测试步骤2: 选择黑白名单但未选择用户
1. 选择"不给谁看"或"只给谁看"
2. 跳转到好友选择页面
3. 不选择任何好友，返回选择页面
4. 点击"确定"

### 预期结果
- 显示提示"请先选择用户"
- 页面不关闭

## 测试场景6: 状态保持验证
### 测试步骤
1. 选择"不给谁看"并选择用户
2. 完成选择后再次打开公开情况选择页面
3. 观察选中状态

### 预期结果
- "不给谁看"选项保持选中状态
- 子标题正确显示之前选择的用户列表

## 关键验证点

### 数据一致性
- [ ] 选择的数据正确保存到`visibilitySelectItemConfig.data`
- [ ] 用户列表正确保存到`selectedUsersList`
- [ ] 主页面显示与实际选择一致

### UI交互
- [ ] 单选逻辑正确工作
- [ ] 页面跳转流畅
- [ ] 提示信息准确显示
- [ ] 选中状态正确更新

### 边界情况
- [ ] 未选择时的错误处理
- [ ] 空用户列表的处理
- [ ] 页面关闭和重新打开的状态保持

## 注意事项
1. 确保测试时网络正常，好友列表能正常加载
2. 测试不同的用户数量选择
3. 验证长用户名列表的显示效果
4. 测试快速点击的情况
