// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		472DB5FA2DF15B0400A047E5 /* Pods_OXYPLay.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 885E9F43039FB1E9A4CC3072 /* Pods_OXYPLay.framework */; };
		472DB5FE2DF15B1700A047E5 /* Network.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 472DB5FD2DF15B1700A047E5 /* Network.framework */; };
		5281AC0F6EAB42C152D180B8 /* Pods_OXYPLay_OXYPLayUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5A9E3AB773DC14BC297A624C /* Pods_OXYPLay_OXYPLayUITests.framework */; };
		EDA9FEE2A49AA478DEE6C080 /* Pods_OXYPLayTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 847B377B63CF6C04FD7FFB6A /* Pods_OXYPLayTests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		47331A032DDEDFF60072EE33 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 473319E42DDEDFF50072EE33 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 473319EB2DDEDFF50072EE33;
			remoteInfo = Skiing;
		};
		47331A0D2DDEDFF60072EE33 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 473319E42DDEDFF50072EE33 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 473319EB2DDEDFF50072EE33;
			remoteInfo = Skiing;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		472DB6032DF15C6F00A047E5 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		36BFCFDE238782D267F73196 /* Pods-SkiingTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SkiingTests.release.xcconfig"; path = "Target Support Files/Pods-SkiingTests/Pods-SkiingTests.release.xcconfig"; sourceTree = "<group>"; };
		3A2C0A5E66863CE9024C8B93 /* Pods-OXYPLay-OXYPLayUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OXYPLay-OXYPLayUITests.debug.xcconfig"; path = "Target Support Files/Pods-OXYPLay-OXYPLayUITests/Pods-OXYPLay-OXYPLayUITests.debug.xcconfig"; sourceTree = "<group>"; };
		41DD3D1FF88E1C40CBCDFE67 /* Pods-Skiing-SkiingUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Skiing-SkiingUITests.debug.xcconfig"; path = "Target Support Files/Pods-Skiing-SkiingUITests/Pods-Skiing-SkiingUITests.debug.xcconfig"; sourceTree = "<group>"; };
		4234538B8E0FBEBDD1F74EBE /* Pods-Skiing.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Skiing.release.xcconfig"; path = "Target Support Files/Pods-Skiing/Pods-Skiing.release.xcconfig"; sourceTree = "<group>"; };
		42CB68EF0D6DC02A3A76AEAF /* Pods-Skiing-SkiingUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Skiing-SkiingUITests.release.xcconfig"; path = "Target Support Files/Pods-Skiing-SkiingUITests/Pods-Skiing-SkiingUITests.release.xcconfig"; sourceTree = "<group>"; };
		472DB5FD2DF15B1700A047E5 /* Network.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Network.framework; path = System/Library/Frameworks/Network.framework; sourceTree = SDKROOT; };
		473319EC2DDEDFF50072EE33 /* OXYPLay.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = OXYPLay.app; sourceTree = BUILT_PRODUCTS_DIR; };
		47331A022DDEDFF60072EE33 /* OXYPLayTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = OXYPLayTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		47331A0C2DDEDFF60072EE33 /* OXYPLayUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = OXYPLayUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		5A9E3AB773DC14BC297A624C /* Pods_OXYPLay_OXYPLayUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_OXYPLay_OXYPLayUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		68B72C2C4B4C295E16315D35 /* Pods-SkiingTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SkiingTests.debug.xcconfig"; path = "Target Support Files/Pods-SkiingTests/Pods-SkiingTests.debug.xcconfig"; sourceTree = "<group>"; };
		847B377B63CF6C04FD7FFB6A /* Pods_OXYPLayTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_OXYPLayTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		885E9F43039FB1E9A4CC3072 /* Pods_OXYPLay.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_OXYPLay.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		8E01C0FAF2C443086EAFCB1A /* Pods-OXYPLay-OXYPLayUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OXYPLay-OXYPLayUITests.release.xcconfig"; path = "Target Support Files/Pods-OXYPLay-OXYPLayUITests/Pods-OXYPLay-OXYPLayUITests.release.xcconfig"; sourceTree = "<group>"; };
		A5890C6B67F4EFE29651D3AC /* Pods-OXYPLay.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OXYPLay.debug.xcconfig"; path = "Target Support Files/Pods-OXYPLay/Pods-OXYPLay.debug.xcconfig"; sourceTree = "<group>"; };
		AD2834B168552EDD3FEF976A /* Pods-OXYPLay.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OXYPLay.release.xcconfig"; path = "Target Support Files/Pods-OXYPLay/Pods-OXYPLay.release.xcconfig"; sourceTree = "<group>"; };
		C703EBD0BE6D543587FBD4C4 /* Pods-OXYPLayTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OXYPLayTests.debug.xcconfig"; path = "Target Support Files/Pods-OXYPLayTests/Pods-OXYPLayTests.debug.xcconfig"; sourceTree = "<group>"; };
		D203AC5B2469F72CB37756D5 /* Pods-Skiing.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Skiing.debug.xcconfig"; path = "Target Support Files/Pods-Skiing/Pods-Skiing.debug.xcconfig"; sourceTree = "<group>"; };
		F51C32D71B2B3E07C299DC07 /* Pods-OXYPLayTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OXYPLayTests.release.xcconfig"; path = "Target Support Files/Pods-OXYPLayTests/Pods-OXYPLayTests.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		47E6A4EA2E08F28F0067A9B9 /* Exceptions for "OXYPLay" folder in "OXYPLay" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				MainClass/Info.plist,
			);
			target = 473319EB2DDEDFF50072EE33 /* OXYPLay */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		47331A052DDEDFF60072EE33 /* OXYPLayTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = OXYPLayTests;
			sourceTree = "<group>";
		};
		47331A0F2DDEDFF60072EE33 /* OXYPLayUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = OXYPLayUITests;
			sourceTree = "<group>";
		};
		47E6A4B22E08F28E0067A9B9 /* OXYPLay */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				47E6A4EA2E08F28F0067A9B9 /* Exceptions for "OXYPLay" folder in "OXYPLay" target */,
			);
			path = OXYPLay;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		473319E92DDEDFF50072EE33 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				472DB5FA2DF15B0400A047E5 /* Pods_OXYPLay.framework in Frameworks */,
				472DB5FE2DF15B1700A047E5 /* Network.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		473319FF2DDEDFF60072EE33 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EDA9FEE2A49AA478DEE6C080 /* Pods_OXYPLayTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		47331A092DDEDFF60072EE33 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5281AC0F6EAB42C152D180B8 /* Pods_OXYPLay_OXYPLayUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		473319E32DDEDFF50072EE33 = {
			isa = PBXGroup;
			children = (
				47E6A4B22E08F28E0067A9B9 /* OXYPLay */,
				47331A052DDEDFF60072EE33 /* OXYPLayTests */,
				47331A0F2DDEDFF60072EE33 /* OXYPLayUITests */,
				478DDF852DED75A90033D89F /* Frameworks */,
				473319ED2DDEDFF50072EE33 /* Products */,
				79954C34E49A3A9CFE038777 /* Pods */,
			);
			sourceTree = "<group>";
		};
		473319ED2DDEDFF50072EE33 /* Products */ = {
			isa = PBXGroup;
			children = (
				473319EC2DDEDFF50072EE33 /* OXYPLay.app */,
				47331A022DDEDFF60072EE33 /* OXYPLayTests.xctest */,
				47331A0C2DDEDFF60072EE33 /* OXYPLayUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		478DDF852DED75A90033D89F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				472DB5FD2DF15B1700A047E5 /* Network.framework */,
				885E9F43039FB1E9A4CC3072 /* Pods_OXYPLay.framework */,
				5A9E3AB773DC14BC297A624C /* Pods_OXYPLay_OXYPLayUITests.framework */,
				847B377B63CF6C04FD7FFB6A /* Pods_OXYPLayTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		79954C34E49A3A9CFE038777 /* Pods */ = {
			isa = PBXGroup;
			children = (
				D203AC5B2469F72CB37756D5 /* Pods-Skiing.debug.xcconfig */,
				4234538B8E0FBEBDD1F74EBE /* Pods-Skiing.release.xcconfig */,
				41DD3D1FF88E1C40CBCDFE67 /* Pods-Skiing-SkiingUITests.debug.xcconfig */,
				42CB68EF0D6DC02A3A76AEAF /* Pods-Skiing-SkiingUITests.release.xcconfig */,
				68B72C2C4B4C295E16315D35 /* Pods-SkiingTests.debug.xcconfig */,
				36BFCFDE238782D267F73196 /* Pods-SkiingTests.release.xcconfig */,
				A5890C6B67F4EFE29651D3AC /* Pods-OXYPLay.debug.xcconfig */,
				AD2834B168552EDD3FEF976A /* Pods-OXYPLay.release.xcconfig */,
				3A2C0A5E66863CE9024C8B93 /* Pods-OXYPLay-OXYPLayUITests.debug.xcconfig */,
				8E01C0FAF2C443086EAFCB1A /* Pods-OXYPLay-OXYPLayUITests.release.xcconfig */,
				C703EBD0BE6D543587FBD4C4 /* Pods-OXYPLayTests.debug.xcconfig */,
				F51C32D71B2B3E07C299DC07 /* Pods-OXYPLayTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		473319EB2DDEDFF50072EE33 /* OXYPLay */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 47331A152DDEDFF60072EE33 /* Build configuration list for PBXNativeTarget "OXYPLay" */;
			buildPhases = (
				E333F2CE76ADC55EA090FDE6 /* [CP] Check Pods Manifest.lock */,
				473319E82DDEDFF50072EE33 /* Sources */,
				473319E92DDEDFF50072EE33 /* Frameworks */,
				473319EA2DDEDFF50072EE33 /* Resources */,
				477FDC4E2DDF4F3D00E85ABC /* ShellScript */,
				40AE460EA8CD214528EEE94A /* [CP] Embed Pods Frameworks */,
				472DB6032DF15C6F00A047E5 /* Embed Frameworks */,
				84A477934B25AB2E61520E3B /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				47E6A4B22E08F28E0067A9B9 /* OXYPLay */,
			);
			name = OXYPLay;
			productName = Skiing;
			productReference = 473319EC2DDEDFF50072EE33 /* OXYPLay.app */;
			productType = "com.apple.product-type.application";
		};
		47331A012DDEDFF60072EE33 /* OXYPLayTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 47331A1A2DDEDFF60072EE33 /* Build configuration list for PBXNativeTarget "OXYPLayTests" */;
			buildPhases = (
				33D38FB7FAE9DF8A74E61CDA /* [CP] Check Pods Manifest.lock */,
				473319FE2DDEDFF60072EE33 /* Sources */,
				473319FF2DDEDFF60072EE33 /* Frameworks */,
				47331A002DDEDFF60072EE33 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				47331A042DDEDFF60072EE33 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				47331A052DDEDFF60072EE33 /* OXYPLayTests */,
			);
			name = OXYPLayTests;
			productName = SkiingTests;
			productReference = 47331A022DDEDFF60072EE33 /* OXYPLayTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		47331A0B2DDEDFF60072EE33 /* OXYPLayUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 47331A1D2DDEDFF60072EE33 /* Build configuration list for PBXNativeTarget "OXYPLayUITests" */;
			buildPhases = (
				00C531AB2D88E9D8E4F463DB /* [CP] Check Pods Manifest.lock */,
				47331A082DDEDFF60072EE33 /* Sources */,
				47331A092DDEDFF60072EE33 /* Frameworks */,
				47331A0A2DDEDFF60072EE33 /* Resources */,
				0D0929982E0843C798B65F7E /* [CP] Embed Pods Frameworks */,
				F8BA0EB8D7D48883AD708097 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				47331A0E2DDEDFF60072EE33 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				47331A0F2DDEDFF60072EE33 /* OXYPLayUITests */,
			);
			name = OXYPLayUITests;
			productName = SkiingUITests;
			productReference = 47331A0C2DDEDFF60072EE33 /* OXYPLayUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		473319E42DDEDFF50072EE33 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					473319EB2DDEDFF50072EE33 = {
						CreatedOnToolsVersion = 16.2;
						LastSwiftMigration = 1620;
					};
					47331A012DDEDFF60072EE33 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 473319EB2DDEDFF50072EE33;
					};
					47331A0B2DDEDFF60072EE33 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 473319EB2DDEDFF50072EE33;
					};
				};
			};
			buildConfigurationList = 473319E72DDEDFF50072EE33 /* Build configuration list for PBXProject "OXYPLay" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 473319E32DDEDFF50072EE33;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 473319ED2DDEDFF50072EE33 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				473319EB2DDEDFF50072EE33 /* OXYPLay */,
				47331A012DDEDFF60072EE33 /* OXYPLayTests */,
				47331A0B2DDEDFF60072EE33 /* OXYPLayUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		473319EA2DDEDFF50072EE33 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		47331A002DDEDFF60072EE33 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		47331A0A2DDEDFF60072EE33 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00C531AB2D88E9D8E4F463DB /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-OXYPLay-OXYPLayUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		0D0929982E0843C798B65F7E /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-OXYPLay-OXYPLayUITests/Pods-OXYPLay-OXYPLayUITests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-OXYPLay-OXYPLayUITests/Pods-OXYPLay-OXYPLayUITests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-OXYPLay-OXYPLayUITests/Pods-OXYPLay-OXYPLayUITests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		33D38FB7FAE9DF8A74E61CDA /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-OXYPLayTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		40AE460EA8CD214528EEE94A /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-OXYPLay/Pods-OXYPLay-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-OXYPLay/Pods-OXYPLay-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-OXYPLay/Pods-OXYPLay-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		477FDC4E2DDF4F3D00E85ABC /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "
";
		};
		84A477934B25AB2E61520E3B /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-OXYPLay/Pods-OXYPLay-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-OXYPLay/Pods-OXYPLay-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-OXYPLay/Pods-OXYPLay-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E333F2CE76ADC55EA090FDE6 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-OXYPLay-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		F8BA0EB8D7D48883AD708097 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-OXYPLay-OXYPLayUITests/Pods-OXYPLay-OXYPLayUITests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-OXYPLay-OXYPLayUITests/Pods-OXYPLay-OXYPLayUITests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-OXYPLay-OXYPLayUITests/Pods-OXYPLay-OXYPLayUITests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		473319E82DDEDFF50072EE33 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		473319FE2DDEDFF60072EE33 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		47331A082DDEDFF60072EE33 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		47331A042DDEDFF60072EE33 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 473319EB2DDEDFF50072EE33 /* OXYPLay */;
			targetProxy = 47331A032DDEDFF60072EE33 /* PBXContainerItemProxy */;
		};
		47331A0E2DDEDFF60072EE33 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 473319EB2DDEDFF50072EE33 /* OXYPLay */;
			targetProxy = 47331A0D2DDEDFF60072EE33 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		47331A162DDEDFF60072EE33 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A5890C6B67F4EFE29651D3AC /* Pods-OXYPLay.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 98MZSQWW48;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/OXYPLay/MainClass",
					"$(PROJECT_DIR)/OXYPLay/MainClass/frameworks",
					"$(PROJECT_DIR)/New\\ Folder/OXYPLay/MainClass/frameworks",
					"$(PROJECT_DIR)/New\\ Folder/OXYPLay/MainClass/frameworks",
					"$(PROJECT_DIR)/New\\ Folder/OXYPLay/MainClass/frameworks",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "$(SRCROOT)/OXYPlay/MainClass/Info.plist";
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问您的相机以扫描二维码";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "需要持续定位以记录滑雪轨迹";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "需要定位权限以查找附近的滑雪场";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要访问您的相册以保存生成的二维码";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的相册以识别图片中的二维码";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = "Launch Screen.storyboard";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-l\"swiftCoreGraphics\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"Alamofire\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"Combine\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"JXSegmentedView\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"MJRefresh\"",
					"-framework",
					"\"Moya\"",
					"-framework",
					"\"SnapKit\"",
					"-framework",
					"\"SwiftDate\"",
					"-framework",
					"\"SwifterSwift\"",
					"-framework",
					"\"UIKit\"",
					"-weak_framework",
					"\"Combine\"",
					"-weak_framework",
					"\"SwiftUI\"",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.kuangyang.oxyplay;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "OXYPlay/MainClass/OXYPlay-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		47331A172DDEDFF60072EE33 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AD2834B168552EDD3FEF976A /* Pods-OXYPLay.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 98MZSQWW48;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/OXYPLay/MainClass",
					"$(PROJECT_DIR)/OXYPLay/MainClass/frameworks",
					"$(PROJECT_DIR)/New\\ Folder/OXYPLay/MainClass/frameworks",
					"$(PROJECT_DIR)/New\\ Folder/OXYPLay/MainClass/frameworks",
					"$(PROJECT_DIR)/New\\ Folder/OXYPLay/MainClass/frameworks",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "$(SRCROOT)/OXYPlay/MainClass/Info.plist";
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问您的相机以扫描二维码";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "需要持续定位以记录滑雪轨迹";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "需要定位权限以查找附近的滑雪场";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要访问您的相册以保存生成的二维码";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的相册以识别图片中的二维码";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = "Launch Screen.storyboard";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-l\"swiftCoreGraphics\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"Alamofire\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"Combine\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"JXSegmentedView\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"MJRefresh\"",
					"-framework",
					"\"Moya\"",
					"-framework",
					"\"SnapKit\"",
					"-framework",
					"\"SwiftDate\"",
					"-framework",
					"\"SwifterSwift\"",
					"-framework",
					"\"UIKit\"",
					"-weak_framework",
					"\"Combine\"",
					"-weak_framework",
					"\"SwiftUI\"",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.kuangyang.oxyplay;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "OXYPlay/MainClass/OXYPlay-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		47331A182DDEDFF60072EE33 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		47331A192DDEDFF60072EE33 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		47331A1B2DDEDFF60072EE33 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C703EBD0BE6D543587FBD4C4 /* Pods-OXYPLayTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 485ZAMUAEL;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sliing.SkiingTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/OXYPLay.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/OXYPLay";
			};
			name = Debug;
		};
		47331A1C2DDEDFF60072EE33 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F51C32D71B2B3E07C299DC07 /* Pods-OXYPLayTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 485ZAMUAEL;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sliing.SkiingTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/OXYPLay.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/OXYPLay";
			};
			name = Release;
		};
		47331A1E2DDEDFF60072EE33 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3A2C0A5E66863CE9024C8B93 /* Pods-OXYPLay-OXYPLayUITests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 485ZAMUAEL;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sliing.SkiingUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Skiing;
			};
			name = Debug;
		};
		47331A1F2DDEDFF60072EE33 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8E01C0FAF2C443086EAFCB1A /* Pods-OXYPLay-OXYPLayUITests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 485ZAMUAEL;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sliing.SkiingUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Skiing;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		473319E72DDEDFF50072EE33 /* Build configuration list for PBXProject "OXYPLay" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				47331A182DDEDFF60072EE33 /* Debug */,
				47331A192DDEDFF60072EE33 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		47331A152DDEDFF60072EE33 /* Build configuration list for PBXNativeTarget "OXYPLay" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				47331A162DDEDFF60072EE33 /* Debug */,
				47331A172DDEDFF60072EE33 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		47331A1A2DDEDFF60072EE33 /* Build configuration list for PBXNativeTarget "OXYPLayTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				47331A1B2DDEDFF60072EE33 /* Debug */,
				47331A1C2DDEDFF60072EE33 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		47331A1D2DDEDFF60072EE33 /* Build configuration list for PBXNativeTarget "OXYPLayUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				47331A1E2DDEDFF60072EE33 /* Debug */,
				47331A1F2DDEDFF60072EE33 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 473319E42DDEDFF50072EE33 /* Project object */;
}
