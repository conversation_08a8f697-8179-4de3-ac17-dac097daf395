{"originHash": "f3e022878aa623307110e295b4af5ada79f4bceaa260af2af5ec228f8189fb4a", "pins": [{"identity": "alamofire", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/Alamofire.git", "state": {"revision": "513364f870f6bfc468f9d2ff0a95caccc10044c5", "version": "5.10.2"}}, {"identity": "estabbarcontroller", "kind": "remoteSourceControl", "location": "https://github.com/eggswift/ESTabBarController.git", "state": {"revision": "93a30b833a05fd916c6d4c5d6e94a270cf3b6636", "version": "2.9.0"}}, {"identity": "<PERSON><PERSON><PERSON>", "kind": "remoteSourceControl", "location": "https://github.com/alibaba/HandyJSON", "state": {"revision": "dcf39cf062680ffa4fd0acb7f9117a97755c113f", "version": "5.0.2"}}, {"identity": "jxsegmentedview", "kind": "remoteSourceControl", "location": "https://github.com/pujiaxin33/JXSegmentedView.git", "state": {"revision": "fbbf965d7396bd99ac1ce88a80b6c950b85313f6", "version": "1.4.1"}}, {"identity": "kingfisher", "kind": "remoteSourceControl", "location": "https://github.com/onevcat/Kingfisher.git", "state": {"revision": "7deda23bbdca612076c5c315003d8638a08ed0f1", "version": "8.3.2"}}, {"identity": "moya", "kind": "remoteSourceControl", "location": "https://github.com/Moya/Moya.git", "state": {"revision": "c263811c1f3dbf002be9bd83107f7cdc38992b26", "version": "15.0.3"}}, {"identity": "reactiveswift", "kind": "remoteSourceControl", "location": "https://github.com/ReactiveCocoa/ReactiveSwift.git", "state": {"revision": "c43bae3dac73fdd3cb906bd5a1914686ca71ed3c", "version": "6.7.0"}}, {"identity": "rxswift", "kind": "remoteSourceControl", "location": "https://github.com/ReactiveX/RxSwift.git", "state": {"revision": "5dd1907d64f0d36f158f61a466bab75067224893", "version": "6.9.0"}}, {"identity": "snapkit", "kind": "remoteSourceControl", "location": "https://github.com/SnapKit/SnapKit.git", "state": {"revision": "2842e6e84e82eb9a8dac0100ca90d9444b0307f4", "version": "5.7.1"}}, {"identity": "swifterswift", "kind": "remoteSourceControl", "location": "https://github.com/SwifterSwift/SwifterSwift.git", "state": {"revision": "5a6de915bb80234e1e31b3e3e5f7a7995fc1f4db", "version": "7.0.0"}}], "version": 3}