# OXYPLay - iOS社交应用

OXYPLay是一款基于iOS平台开发的社交应用，专注于提供用户分享、交流和互动的平台。应用采用模块化设计和MVVM架构，具有良好的可扩展性和可维护性。

## 项目概述

OXYPLay应用旨在打造一个全方位的社交平台，用户可以在应用中分享内容、浏览推荐、关注感兴趣的人和话题，以及参与各种社交活动。应用主要功能包括：

- 用户认证：支持手机号验证码登录、密码登录和微信第三方登录
- 内容浏览：个性化推荐、地区筛选和关注内容流
- 社交互动：点赞、评论、分享和私信
- 集结功能：用户可以发布和参与各类活动，如找人、找房、找车等
- 个人中心：用户资料管理、历史记录和设置

## 系统架构

### 架构设计

项目采用MVVM（Model-View-ViewModel）架构模式，结合Combine框架实现响应式编程。主要架构组件包括：

- **Model**：数据模型层，负责定义数据结构和业务逻辑
- **View**：视图层，负责UI展示和用户交互
- **ViewModel**：视图模型层，连接Model和View，处理业务逻辑和数据转换
- **Service**：服务层，负责网络请求和数据处理

### 模块划分

项目按功能模块进行划分，每个模块包含Model、View、ViewModel和ViewController四个主要部分：

1. **LoginModule**：用户登录注册模块
2. **HomeModule**：首页内容展示模块
3. **AssemblyModule**：集结社交活动模块
4. **MessageModule**：消息通知和私信模块
5. **MineModule**：个人中心模块
6. **TabbarModule**：底部标签栏模块
7. **APIService**：网络服务模块
8. **BaseClass**：基础组件模块
9. **Extension**：扩展工具模块
10. **Macros**：常量和宏定义模块

## 技术栈

### 开发环境

- 开发语言：Swift 5.0+
- 最低iOS版本：iOS 13.0+
- 开发工具：Xcode 12.0+

### 核心技术

- **UI框架**：UIKit
- **网络层**：基于Moya/Alamofire封装的网络框架
- **响应式编程**：Combine
- **数据解析**：SmartCodable
- **布局方案**：SnapKit (Auto Layout)
- **第三方登录**：微信SDK
- **即时通讯**：环信SDK (HyphenateChat)
- **依赖管理**：CocoaPods

### 第三方库

主要使用的第三方库包括：

- **SnapKit**：简化Auto Layout约束编写
- **Moya**：网络抽象层
- **SmartCodable**：智能JSON解析
- **MJRefresh**：下拉刷新和上拉加载
- **JXSegmentedView**：分段控制器
- **MBProgressHUD**：加载提示框
- **Then**：简化对象初始化和配置
- **SwiftyJSON**：JSON数据处理

## 项目结构

```
OXYPLay/
├── APIService/              # API服务层
│   ├── NetWork/             # 网络基础组件
│   ├── HomeService.swift    # 首页API服务
│   └── LoginService.swift   # 登录API服务
├── AssemblyModule/          # 集结模块
│   ├── Model/               # 数据模型
│   ├── View/                # 视图组件
│   ├── ViewController/      # 视图控制器
│   └── ViewModel/           # 视图模型
├── BaseClass/               # 基础类
│   ├── BaseView.swift       # 基础视图
│   ├── BaseViewController.swift # 基础视图控制器
│   ├── BaseViewModel.swift  # 基础视图模型
│   └── Refresh/             # 刷新组件
├── Extension/               # 扩展工具
├── HomeModule/              # 首页模块
│   ├── Model/               # 数据模型
│   ├── View/                # 视图组件
│   ├── ViewController/      # 视图控制器
│   └── ViewModel/           # 视图模型
├── LoginModule/             # 登录模块
│   ├── Model/               # 数据模型
│   ├── View/                # 视图组件
│   ├── ViewController/      # 视图控制器
│   └── ViewModel/           # 视图模型
├── Macros/                  # 常量和宏定义
├── MainClass/               # 应用主类
│   ├── AppDelegate.swift    # 应用代理
│   └── Assets.xcassets/     # 资源文件
├── MessageModule/           # 消息模块
├── MineModule/              # 个人中心模块
└── TabbarModule/            # 底部标签栏模块
```

## 核心功能实现

### 1. 网络层设计

网络层基于Moya和Combine框架实现，主要组件包括：

- **CombineNetworkManager**：基于Combine的网络请求管理器
- **GeneralAPIService**：API服务协议
- **TokenManager**：Token管理器
- **CombineTokenRefreshManager**：Token刷新管理器

网络请求流程：
1. ViewModel调用BaseViewModel中的网络请求方法
2. 请求通过CombineNetworkManager发送
3. 响应数据通过SmartCodable解析为模型
4. 结果通过Combine的Publisher返回给ViewModel

### 2. 用户认证

用户认证由LoginModule模块负责，支持多种登录方式：

- 手机号+验证码登录
- 手机号+密码登录
- 微信第三方登录
- 一键登录

认证流程由LoginManager统一管理，包括登录状态检查、Token管理和界面切换。

### 3. 首页功能

首页由HomeModule模块实现，主要包括：

- 分段式内容展示（地区、推荐、关注）
- 轮播广告展示
- 推荐内容流
- 下拉刷新和上拉加载更多

### 4. 集结功能

集结功能由AssemblyModule模块实现，提供多种社交活动类型：

- 找人
- 找房
- 找车
- 找闲置
- 有话说
- 随便问

支持按地区和场地进行筛选，实现精准内容匹配。

### 5. 底部标签栏

应用使用自定义TabBar实现底部导航，包括：

- 首页
- 集结
- 发布（中间按钮）
- 消息
- 我的

中间的发布按钮采用特殊设计，支持动画效果和点击交互。

## 安装指南

### 环境要求

- macOS 10.15+
- Xcode 12.0+
- iOS 13.0+
- CocoaPods 1.10.0+

### 安装步骤

1. 克隆项目代码
```bash
git clone https://github.com/yourusername/OXYPLay.git
cd OXYPLay
```

2. 安装依赖
```bash
pod install
```

3. 打开工程
```bash
open OXYPLay.xcworkspace
```

4. 编译运行
在Xcode中选择目标设备或模拟器，点击运行按钮。

## 使用说明

### 登录注册

1. 启动应用后，可选择跳过登录直接浏览内容
2. 支持手机号+验证码登录
3. 支持手机号+密码登录
4. 支持微信登录（需配置微信开发者账号）

### 内容浏览

1. 首页支持切换地区、推荐和关注三种内容流
2. 下拉刷新获取最新内容
3. 上拉加载更多历史内容

### 集结功能

1. 点击底部"集结"标签进入集结页面
2. 可按类型、地区和场地筛选内容
3. 点击内容可查看详情
4. 支持点赞和评论互动

### 发布内容

点击底部中间的"+"按钮，可发布新内容。

### 消息中心

点击底部"消息"标签，查看系统通知和私信。

### 个人中心

点击底部"我的"标签，管理个人资料和设置。

## 开发规范

### 命名规范

- 类名：使用大驼峰命名法（如BaseViewController）
- 变量和方法：使用小驼峰命名法（如setupUI()）
- 常量：使用k前缀（如kScreenWidth）

### 代码组织

- 每个模块的文件应放在对应的目录中
- 公共组件放在BaseClass目录
- 扩展方法放在Extension目录

### UI开发

- 使用Auto Layout进行界面布局
- 颜色和尺寸常量在Macro文件中定义
- 复用UI组件，继承自BaseView或BaseViewController

## 贡献指南

欢迎贡献代码或提出建议，请遵循以下步骤：

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

[MIT License](LICENSE)

## 联系方式

- 项目维护者：[Your Name](mailto:<EMAIL>)
- 项目仓库：[GitHub](https://github.com/yourusername/OXYPLay) 