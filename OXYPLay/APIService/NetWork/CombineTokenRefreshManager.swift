//
//  CombineTokenRefreshManager.swift
//  OXYPLay
//
//  Created by Renhw on 2023/6/10.
//

import Foundation
import Combine
import Moya

/// 基于Combine的Token刷新管理器
class CombineTokenRefreshManager {
    static let shared = CombineTokenRefreshManager()
    
    private init() {}
    
    private let tokenLock = NSLock()
    private var isRefreshing = false
    private var refreshSubject = PassthroughSubject<Void, Never>()
    var cancellables = Set<AnyCancellable>()
    
    // 专用于刷新Token的Provider，避免循环依赖
    private lazy var refreshProvider = MoyaProvider<MultiTarget>(plugins: [DebugPlugin()])
    
    /// 获取Token刷新事件的Publisher
    var tokenRefreshPublisher: AnyPublisher<Void, Never> {
        return refreshSubject.eraseToAnyPublisher()
    }
    
    /// 如果需要，刷新Token
    func refreshTokenIfNeeded() {
        tokenLock.lock()
        defer { tokenLock.unlock() }
        
        // 避免重复刷新Token
        if isRefreshing {
            return
        }
        
        // 检查是否有刷新Token
        guard let refreshToken = TokenManager.shared.refreshToken else {
            TokenManager.shared.clearTokens()
            NotificationCenter.default.post(name: AppNotifications.userTokenExpired, object: nil)
            return
        }
        
        isRefreshing = true
        
        // 创建刷新Token的请求
        let target = LoginService.refreshToken(refreshToken: refreshToken)
        
        refreshProvider.requestPublisher(MultiTarget(target))
            .tryMap { response -> AuthResponse in
                let jsonData = try response.mapJSON() as? [String: Any]
                let data = jsonData?[APIConstants.responseDataKey] as? [String: Any]
                
                guard let accessToken = data?["access_token"] as? String,
                      let refreshToken = data?["refresh_token"] as? String else {
                    throw NSError(domain: "TokenRefresh", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的Token响应"])
                }
                
                let authResponse = AuthResponse()
                authResponse.access_token = accessToken
                authResponse.refresh_token = refreshToken
                return authResponse
            }
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }
                    
                    self.isRefreshing = false
                    
                    if case .failure(_) = completion {
                        // Token刷新失败，清除所有Token
                        TokenManager.shared.clearTokens()
                        NotificationCenter.default.post(name: AppNotifications.userTokenExpired, object: nil)
                    }
                },
                receiveValue: { [weak self] newTokens in
                    guard let self = self else { return }
                    
                    // 更新Token
                    TokenManager.shared.updateTokens(
                        accessToken: newTokens.access_token ?? "",
                        refreshToken: newTokens.refresh_token ?? ""
                    )
                    
                    self.isRefreshing = false
                    
                    // 通知订阅者Token已刷新
                    self.refreshSubject.send()
                }
            )
            .store(in: &cancellables)
    }
    
    /// 清除所有Token并取消刷新操作
    func clearTokensAndCancelRefresh() {
        tokenLock.lock()
        defer { tokenLock.unlock() }
        
        isRefreshing = false
        TokenManager.shared.clearTokens()
        cancellables.removeAll()
    }
} 
