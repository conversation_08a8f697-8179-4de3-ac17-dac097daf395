//
//  GeneralAPIService.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/6.
//

import Foundation
import Moya
import Combine

/// 通用API服务协议
public protocol GeneralAPIService: TargetType, AccessTokenAuthorizable {}

public extension GeneralAPIService {
    var baseURL: URL {
        URL(string: APIConstants.baseURL)!
    }

    var method: Moya.Method {
        .post
    }

    var sampleData: Data {
        "".data(using: .utf8)!
    }

    var headers: [String: String]? {
        ["Content-Type": "application/x-www-form-urlencoded"]
    }

    /// 认证类型配置（Moya AccessTokenAuthorizable协议）
    var authorizationType: AuthorizationType? {
        return .bearer
    }
}
