//
//  DebugPlugin.swift
//  NewLiHeCRM
//
//  Created by Renhw on 2023/5/26.
//

public struct DebugPlugin {
    var openDebugRequest = false
    var openDebugResponse = true
}

// MARK: - PluginType

extension DebugPlugin: PluginType {
    public func willSend(_: RequestType, target: TargetType) {
        #if DEBUG
            printRequest(target)
        #endif
    }

    public func didReceive(_ result: Result<Moya.Response, MoyaError>, target: TargetType) {
        #if DEBUG
            ansysisResult(target, result, local: false)
        #endif
    }
}

extension DebugPlugin {
    private func printRequest(_ target: TargetType) {
        guard openDebugRequest else { return }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSSSSSZ"
        formatter.locale = Locale.current
        let date = formatter.string(from: Date())
        var parameters: [String: Any]?
        if case let .requestParameters(parame, _) = target.task {
            parameters = parame
        }
        
        // 确保URL参数中的中文正确显示
        let requestURL = requestFullLink(with: target)
        
        if let param = parameters, !param.isEmpty {
            // 格式化参数输出，避免中文显示为UTF-8编码
            let jsonData = try? JSONSerialization.data(withJSONObject: param, options: .prettyPrinted)
            let prettyParams = jsonData != nil ? String(data: jsonData!, encoding: .utf8) ?? "\(param)" : "\(param)"
            
            print("""
            ╔═══════════🚀🚀🚀 请求 🚀🚀🚀═══════════
            ║ 时间：\(date)
            ║ URL：{{\(requestURL)}}
            ║ 参数：\(prettyParams)
            ╚═══════════════════════════════════════
            """)
        } else {
            print("""
            ╔═══════════🚀🚀🚀 请求 🚀🚀🚀═══════════
            ║ 时间：\(date)
            ║ URL：\(requestURL)
            ╚═══════════════════════════════════════
            """)
        }
    }

    private func requestFullLink(with target: TargetType) -> String {
        var parameters: [String: Any]?
        if case let .requestParameters(parame, _) = target.task {
            parameters = parame
        }
        guard let parameters = parameters, !parameters.isEmpty else {
            return target.baseURL.absoluteString + target.path
        }
        
        // 对参数进行处理，确保中文能正确显示
        let sortedParameters = parameters.sorted(by: { $0.key > $1.key })
        var paramString = "?"
        for index in sortedParameters.indices {
            let key = sortedParameters[index].key
            let value = String(describing: sortedParameters[index].value)
            
            // 使用百分比编码但在打印时解码以显示中文
            if let encodedValue = value.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
                paramString.append("\(key)=\(encodedValue)")
            } else {
                paramString.append("\(key)=\(value)")
            }
            
            if index != sortedParameters.count - 1 { paramString.append("&") }
        }
        
        return target.baseURL.absoluteString + target.path + "\(paramString)"
    }
}

extension DebugPlugin {
    private func ansysisResult(_ target: TargetType, _ result: Result<Moya.Response, MoyaError>, local: Bool) {
        switch result {
        case let .success(response):
            do {
                let response = try response.filterSuccessfulStatusCodes()
                let json = try response.mapJSON()
                printResponse(target, json, local, true)
            } catch let MoyaError.jsonMapping(response) {
                let error = MoyaError.jsonMapping(response)
                printResponse(target, error.localizedDescription, local, false)
            } catch let MoyaError.statusCode(response) {
                let error = MoyaError.statusCode(response)
                printResponse(target, error.localizedDescription, local, false)
            } catch {
                printResponse(target, error.localizedDescription, local, false)
            }
        case let .failure(error):
            printResponse(target, error.localizedDescription, local, false)
        }
    }

    private func printResponse(_ target: TargetType, _ json: Any, _ local: Bool, _ success: Bool) {
        guard openDebugResponse else { return }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSSSSSZ"
        formatter.locale = Locale.current
        let date = formatter.string(from: Date())
        var parameters: [String: Any]?
        if case let .requestParameters(parame, _) = target.task {
            parameters = parame
        }
        
        // 格式化JSON输出，避免中文显示为UTF-8编码
        let prettyJson: String
        if let jsonDict = json as? [String: Any] {
            let jsonData = try? JSONSerialization.data(withJSONObject: jsonDict, options: .prettyPrinted)
            prettyJson = jsonData != nil ? String(data: jsonData!, encoding: .utf8) ?? "\(json)" : "\(json)"
        } else if let jsonArray = json as? [Any] {
            let jsonData = try? JSONSerialization.data(withJSONObject: jsonArray, options: .prettyPrinted)
            prettyJson = jsonData != nil ? String(data: jsonData!, encoding: .utf8) ?? "\(json)" : "\(json)"
        } else {
            prettyJson = "\(json)"
        }
        
        // 格式化参数输出，避免中文显示为UTF-8编码
        let prettyParams: String
        if let param = parameters, !param.isEmpty {
            let jsonData = try? JSONSerialization.data(withJSONObject: param, options: .prettyPrinted)
            prettyParams = jsonData != nil ? String(data: jsonData!, encoding: .utf8) ?? "\(param)" : "\(param)"
            
            print("""
            ╔═══════════🚀🚀🚀 请求开始 🚀🚀🚀═══════════
            ║ 时间: \(date)
            ║ URL: {{\(requestFullLink(with: target))}}
            ║-------------------------------------
            ║ 方法: \(target.method.rawValue)
            ║ Host: \(target.baseURL.absoluteString)
            ║ Path: \(target.path)
            ║ Headers: \(String(describing: target.headers))
            ║ 参数: \(prettyParams)
            ║----------🚀🚀🚀 响应 🚀🚀🚀----------
            ║ 结果: \(success ? "成功" : "失败")
            ║ 数据类型: \(local ? "本地数据" : "远程数据")
            ║ 响应:\n\n\(prettyJson)\n
            ╚═══════════🚀🚀🚀 请求结束 🚀🚀🚀═══════════
            """)
        } else {
            print("""
            ╔═══════════🚀🚀🚀 请求开始 🚀🚀🚀═══════════
            ║ 时间: \(date)
            ║ URL: {{\(requestFullLink(with: target))}}
            ║-------------------------------------
            ║ 方法: \(target.method.rawValue)
            ║ Host: \(target.baseURL.absoluteString)
            ║ Path: \(target.path)
            ║ Headers: \(String(describing: target.headers))
            ║----------🚀🚀🚀 响应 🚀🚀🚀----------
            ║ 结果: \(success ? "成功" : "失败")
            ║ 数据类型: \(local ? "本地数据" : "远程数据")
            ║ 响应:\n\n\(prettyJson)\n
            ╚═══════════🚀🚀🚀 请求结束 🚀🚀🚀═══════════
            """)
        }
    }
}
