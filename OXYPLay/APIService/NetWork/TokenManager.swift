import Foundation

/// 令牌管理工具
final class TokenManager {
    static let shared = TokenManager()

    var accessToken: String? {
        get { UserDefaults.standard.string(forKey: "accessToken") }
        set { UserDefaults.standard.set(newValue, forKey: "accessToken") }
    }

    var refreshToken: String? {
        get { UserDefaults.standard.string(forKey: "refreshToken") }
        set { UserDefaults.standard.set(newValue, forKey: "refreshToken") }
    }

    /// 更新双令牌
    func updateTokens(accessToken: String, refreshToken: String) {
        self.accessToken = accessToken
        self.refreshToken = refreshToken
    }

    /// 清除所有令牌
    func clearTokens() {
        accessToken = nil
        refreshToken = nil
    }
}
