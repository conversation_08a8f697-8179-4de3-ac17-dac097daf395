//
//  CombineNetworkManager.swift
//  OXYPLay
//
//  Created by Renhw on 2023/6/10.
//

import Foundation
import Combine
import Alamofire
import SmartCodable
import Moya
import SwiftyJSON
import UIKit



/// 基于Combine的网络管理器
class CombineNetworkManager {
    static let shared = CombineNetworkManager()
    
    private init() {}
    
    // 超时时长
    private var requestTimeOut: Double = APIConstants.requestTimeOut
    
    // 网络请求提供者
    private lazy var provider = MoyaProvider<MultiTarget>(
        requestClosure: requestClosure,
        plugins: [
            AccessTokenPlugin { _ in TokenManager.shared.accessToken ?? "" },
            DebugPlugin()
        ],
        trackInflights: false
    )
    
    /// 网络请求的设置
    private let requestClosure = { (endpoint: Endpoint, done: MoyaProvider.RequestResultClosure) in
        do {
            var request = try endpoint.urlRequest()
            // 设置请求时长
            request.timeoutInterval = APIConstants.requestTimeOut
            done(.success(request))
        } catch {
            done(.failure(MoyaError.underlying(error, nil)))
        }
    }
    
    // 在CombineNetworkManager类中添加一个私有属性用于存储待重试的请求
    private var pendingRequests = [UUID: (TargetType) -> AnyPublisher<ResponseModel, NetworkError>]()
    // 添加一个标志，避免重复订阅token刷新事件
    private var isListeningForTokenRefresh = false
    private var cancellables = Set<AnyCancellable>()

    /// 发送网络请求并返回Publisher
    /// - Parameter target: 接口
    /// - Returns: 返回一个Publisher，发出ResponseModel或错误
    func request(_ target: TargetType) -> AnyPublisher<ResponseModel, NetworkError> {
        let requestId = UUID()
        
        // 创建一个可以重试的请求函数
        let requestPublisher: (TargetType) -> AnyPublisher<ResponseModel, NetworkError> = { [weak self] target in
            guard let self = self else {
                return Fail(error: NetworkError.networkError(ResponseModel(code: -1, message: "网络管理器已释放"))).eraseToAnyPublisher()
            }
            
            return self.provider.requestPublisher(MultiTarget(target))
                .tryCatch { [weak self] error -> MoyaPublisher<Response> in
                    // 处理网络连接错误
                    if case .underlying(let underlyingError as NSError, _) = error, 
                        underlyingError.domain == NSURLErrorDomain {
                        self?.showErrorIfNeeded("网络连接失败")
                        throw NetworkError.noConnection
                    }
                    throw error
                }
                .tryMap { [weak self] response -> ResponseModel in
                    guard let self = self else {
                        throw NetworkError.networkError(ResponseModel(code: -1, message: "网络管理器已释放"))
                    }
                    
                    // 解析响应数据
                    return try self.parseResponse(response)
                }
                .mapError { [weak self] error -> NetworkError in
                    guard let self = self else {
                        return NetworkError.networkError(ResponseModel(code: -1, message: "网络管理器已释放"))
                    }
                    
                    // 处理错误
                    return self.handleError(error)
                }
                .eraseToAnyPublisher()
        }
        
        // 存储请求函数，以便在token刷新后重试
        pendingRequests[requestId] = requestPublisher
        
        return requestPublisher(target)
            .handleEvents(receiveCompletion: { [weak self] completion in
                guard let self = self else { return }
                
                if case .failure(let error) = completion {
                    self.handleCompletionError(error)
                }
            }, receiveCancel: { [weak self] in
                // 请求被取消时，移除存储的请求函数
                self?.pendingRequests.removeValue(forKey: requestId)
            })
            .catch { [weak self] (error: NetworkError) -> AnyPublisher<ResponseModel, NetworkError> in
                guard let self = self else {
                    return Fail(error: error).eraseToAnyPublisher()
                }
                
                // 处理错误并决定是否重试
                return self.handleRetryForError(error, requestId: requestId, target: target)
            }
            .eraseToAnyPublisher()
    }
    
    /// 解析响应数据
    /// - Parameter response: Moya响应
    /// - Returns: 解析后的ResponseModel
    /// - Throws: 解析过程中的错误
    private func parseResponse(_ response: Response) throws -> ResponseModel {
        let jsonData = try JSON(data: response.data)
        let respModel = ResponseModel()
        respModel.code = jsonData[APIConstants.responseCodeKey].int ?? -999
        respModel.message = jsonData[APIConstants.responseMessageKey].stringValue
        respModel.dataString = jsonData[APIConstants.responseDataKey].rawString() ?? ""
        
        if respModel.code == APIConstants.successCode {
            return respModel
        } else if respModel.code == APIConstants.tokenExpiredCode {
            throw NetworkError.tokenExpired
        } else if respModel.code == APIConstants.tokenErrorCode {
            throw NetworkError.tokenError
        } else {
            // 其他错误
            showErrorIfNeeded(respModel.message)
            throw NetworkError.networkError(respModel)
        }
    }
    
    /// 处理错误
    /// - Parameter error: 原始错误
    /// - Returns: 处理后的NetworkError
    private func handleError(_ error: Error) -> NetworkError {
        if let networkError = error as? NetworkError {
            return networkError
        }
        
        if let moyaError = error as? MoyaError, case .jsonMapping = moyaError {
            let message = "JSON解析失败"
            let model = ResponseModel(code: 1_000_000, message: message)
            showErrorIfNeeded(message)
            return .networkError(model)
        }
        
        let message = error.localizedDescription
        let model = ResponseModel(code: -1, message: message)
        showErrorIfNeeded(message)
        return .networkError(model)
    }
    
    /// 处理完成时的错误
    /// - Parameter error: 网络错误
    private func handleCompletionError(_ error: NetworkError) {
        if case .tokenExpired = error {
            // 确保只在第一次遇到token过期时启动刷新流程
            listenForTokenRefreshIfNeeded()
            CombineTokenRefreshManager.shared.refreshTokenIfNeeded()
        }
    }
    
    /// 处理错误并决定是否重试
    /// - Parameters:
    ///   - error: 网络错误
    ///   - requestId: 请求ID
    ///   - target: 请求目标
    /// - Returns: 可能重试的Publisher
    private func handleRetryForError(_ error: NetworkError, requestId: UUID, target: TargetType) -> AnyPublisher<ResponseModel, NetworkError> {
        // 如果是token过期错误，等待token刷新后重试请求
        if case .tokenExpired = error {
            return retryAfterTokenRefresh(requestId: requestId, target: target)
        } else if case .tokenError = error {
            // 发送token错误通知
            NotificationCenter.default.post(name: AppNotifications.userTokenExpired, object: nil)
            // 直接返回错误
            pendingRequests.removeValue(forKey: requestId)
            return Fail(error: error).eraseToAnyPublisher()
        } else {
            // 其他错误，直接返回错误
            pendingRequests.removeValue(forKey: requestId)
            return Fail(error: error).eraseToAnyPublisher()
        }
    }
    
    /// Token刷新后重试请求
    /// - Parameters:
    ///   - requestId: 请求ID
    ///   - target: 请求目标
    /// - Returns: 重试的Publisher
    private func retryAfterTokenRefresh(requestId: UUID, target: TargetType) -> AnyPublisher<ResponseModel, NetworkError> {
        // 创建一个Publisher，表示成功或失败的token刷新结果
        let refreshResult = CombineTokenRefreshManager.shared.tokenRefreshPublisher
            .map { _ in true } // token刷新成功
            .timeout(.seconds(10), scheduler: RunLoop.main) // 10秒超时
            .catch { _ -> AnyPublisher<Bool, Never> in
                // 超时或其他错误都视为token刷新失败
                return Just(false).eraseToAnyPublisher()
            }
            .eraseToAnyPublisher()
        
        return refreshResult
            .first() // 只取第一个结果
            .flatMap { [weak self] success -> AnyPublisher<ResponseModel, NetworkError> in
                guard let self = self else {
                    return Fail(error: NetworkError.tokenExpired).eraseToAnyPublisher()
                }
                
                // 移除存储的请求函数
                defer { self.pendingRequests.removeValue(forKey: requestId) }
                
                // 如果token刷新成功，重新发起原请求；否则返回错误
                if success, let requestFunc = self.pendingRequests[requestId] {
                    return requestFunc(target).eraseToAnyPublisher()
                } else {
                    return Fail(error: NetworkError.tokenExpired).eraseToAnyPublisher()
                }
            }
            .eraseToAnyPublisher()
    }
    
    /// 显示错误提示（如果需要）
    /// - Parameter message: 错误信息
    private func showErrorIfNeeded(_ message: String) {
        if let view = ScreenInfo.activeWindow {
            MBProgressHUD.showError(message, in: view)
        }
    }
    
    /// 请求单个模型
    /// - Parameters:
    ///   - target: 接口
    ///   - type: 模型类型
    /// - Returns: 返回一个Publisher，发出模型或错误
    func requestModel<T: SmartCodable>(_ target: TargetType, type: T.Type) -> AnyPublisher<T, NetworkError> {
        return request(target)
            .tryMap { responseModel -> T in
                if let result = T.deserialize(from: responseModel.dataString) {
                    return result
                } else {
                    throw NetworkError.decodingError("SmartCodable解析失败")
                }
            }
            .mapError { error -> NetworkError in
                if let networkError = error as? NetworkError {
                    return networkError
                }
                return NetworkError.decodingError(error.localizedDescription)
            }
            .eraseToAnyPublisher()
    }
    
    /// 请求模型数组
    /// - Parameters:
    ///   - target: 接口
    ///   - type: 模型数组类型
    /// - Returns: 返回一个Publisher，发出模型数组或错误
    func requestModels<T: SmartCodable>(_ target: TargetType, type: [T].Type) -> AnyPublisher<[T], NetworkError> {
        return request(target)
            .tryMap { responseModel -> [T] in
                if let result = [T].deserialize(from: responseModel.dataString) {
                    return result
                } else {
                    throw NetworkError.decodingError("SmartCodable数组解析失败")
                }
            }
            .mapError { error -> NetworkError in
                if let networkError = error as? NetworkError {
                    return networkError
                }
                return NetworkError.decodingError(error.localizedDescription)
            }
            .eraseToAnyPublisher()
    }
    
    /// 上传图片
    /// - Parameters:
    ///   - image: 要上传的图片
    ///   - fileName: 文件名
    ///   - mimeType: MIME类型
    /// - Returns: ResponseModel的Publisher
    func uploadImage(_ image: UIImage, fileName: String = "image.jpg", mimeType: String = "image/jpeg") -> AnyPublisher<ResponseModel, NetworkError> {
        guard let _ = image.jpegData(compressionQuality: 0.7) else {
            return Fail(error: NetworkError.decodingError("图片转换失败")).eraseToAnyPublisher()
        }
        
        // 使用Moya上传
        let uploadTarget = UploadService.uploadImage(image, fileName: fileName, mimeType: mimeType)
        return request(uploadTarget)
    }
    
    /// 上传文件
    /// - Parameter fileURL: 文件URL
    /// - Returns: ResponseModel的Publisher
    func uploadFile(_ fileURL: URL) -> AnyPublisher<ResponseModel, NetworkError> {
        // 使用Moya上传
        let uploadTarget = UploadService.uploadFile(fileURL)
        return request(uploadTarget)
    }

    /// 确保只在需要时监听token刷新事件
    private func listenForTokenRefreshIfNeeded() {
        if isListeningForTokenRefresh {
            return
        }
        
        isListeningForTokenRefresh = true
        
        // 监听token刷新完成事件
        CombineTokenRefreshManager.shared.tokenRefreshPublisher
            .sink { [weak self] _ in
                // token刷新完成后，重置监听状态，以便下次需要时可以再次监听
                self?.isListeningForTokenRefresh = false
            }
            .store(in: &cancellables)
    }

    /// 清除所有待处理的请求和刷新状态
    func clearPendingRequests() {
        pendingRequests.removeAll()
        isListeningForTokenRefresh = false
        cancellables.removeAll()
    }
}
