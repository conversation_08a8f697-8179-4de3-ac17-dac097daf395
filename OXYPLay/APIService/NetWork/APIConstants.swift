//
//  APIConstants.swift
//  OXYPLay
//
//  Created by Renhw on 2023/6/10.
//

import Foundation

/// API常量 - 集中管理API相关的常量
enum APIConstants {
    // 基础URL
    static let baseURL = "http://47.95.11.63"
    static let HomeApi = "/api/v1/post"
    static let ServiceApi = "/api/v1/service"
    static let ServiceDetailApi = "/api/v1/service-detail"
    static let UserApi = "/api/v1/user"
    static let CommentApi = "/api/v1/post-comment"
    static let MyApi = "/api/v1/my"
    static let CartApi = "/api/v1/cart"
    static let OrderApi = "/api/v1/order"
    static let PayApi = "/api/v1/pay"
    static let WalletApi = "/api/v1/wallet-cron"


    // 响应键
    static let responseDataKey = "data"
    static let responseMessageKey = "message"
    static let responseCodeKey = "code"

    // 状态码
    static let successCode: Int = 200
    static let tokenExpiredCode: Int = 401
    static let tokenErrorCode: Int = 402
    // 超时设置
    static let requestTimeOut: Double = 30
}
