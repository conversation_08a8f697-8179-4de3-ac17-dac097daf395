//
//  APITypes.swift
//  OXYPLay
//
//  Created by Renhw on 2023/6/10.
//

import Foundation
import SmartCodable
import Combine
import Moya

/// API类型定义 - 集中管理网络层使用的类型别名

// 单个模型的成功回调 包括： 模型，网络请求的模型(code,message,data等)
typealias RequestModelSuccessCallback<T: SmartCodable> = (T, ResponseModel) -> Void

// 数组模型的成功回调 包括： 模型数组， 网络请求的模型(code,message,data等)
typealias RequestModelsSuccessCallback<T: SmartCodable> = ([T], ResponseModel) -> Void

// 网络请求的回调 包括：网络请求的模型(code,message,data等)
typealias RequestCallback = (ResponseModel) -> Void

// 网络错误的回调
typealias ErrorCallback = () -> Void

// 可取消的网络请求
typealias NetworkCancellable = Moya.Cancellable

// MARK: - Combine类型定义

/// API结果类型 - 用于Combine的Publisher
enum APIResult<T> {
    case success(T, ResponseModel)
    case failure(ResponseModel)
}

/// 网络错误类型
enum NetworkError: Error {
    case networkError(ResponseModel)
    case decodingError(String)
    case noConnection
    case tokenExpired//token过期
    case tokenError//token错误需要重新登陆
}

// MARK: - Moya/Combine扩展类型

/// Moya响应结果类型
typealias MoyaPublisher<T> = AnyPublisher<T, MoyaError>

/// 自定义响应结果类型
typealias APIPublisher<T> = AnyPublisher<T, NetworkError>

/// 请求参数协议
protocol RequestParametersConvertible {
    func asParameters() -> [String: Any]
}

/// 请求参数类
struct RequestParameters: RequestParametersConvertible {
    private var parameters: [String: Any]
    
    init(_ parameters: [String: Any]) {
        self.parameters = parameters
    }
    
    func asParameters() -> [String: Any] {
        return parameters
    }
}

// 默认实现
extension Dictionary: RequestParametersConvertible where Key == String, Value == Any {
    func asParameters() -> [String: Any] {
        return self
    }
}
