//
//  ResponseModel.swift
//  OXYPLay
//
//  Created by Renhw on 2023/6/10.
//

import Foundation
import SmartCodable

/// 响应模型 - 统一的网络响应数据结构
class ResponseModel {
    /// 响应码
    var code: Int = -999

    /// 响应消息
    var message: String = ""

    /// 响应数据字符串
    var dataString: String = ""

    /// 初始化方法
    init() {}

    /// 使用参数初始化
    /// - Parameters:
    ///   - code: 响应码
    ///   - message: 响应消息
    ///   - dataString: 响应数据字符串
    init(code: Int = -999, message: String = "", dataString: String = "") {
        self.code = code
        self.message = message
        self.dataString = dataString
    }

    /// 判断响应是否成功
    var isSuccess: Bool {
        return code == APIConstants.successCode
    }

    /// 判断是否是Token过期错误
    var isTokenExpired: Bool {
        return code == APIConstants.tokenExpiredCode
    }
}

/// 认证响应模型
class AuthResponse: SmartCodable {
    var access_token: String?
    var refresh_token: String?
    var uid: Int?
    
    required init() {}
}

/// 分页响应模型
struct PageResponse<T: SmartCodable>: SmartCodable {
    /// 数据列表
    var list: [T] = []
    
    /// 当前页码
    var page: Int = 1
    
    /// 每页数量
    var size: Int = 20
    
    /// 总数量
    var total: Int = 0
    
}

/// 空响应模型 - 用于不需要返回数据的接口
class EmptyResponse: SmartCodable {
    required init() {}
}
