import Foundation
import Moya

/// 登录服务API
enum LoginService {
    case login(params: RequestParametersConvertible) // 登录
    case sendCode(params: RequestParametersConvertible) // 发送验证码
    case refreshToken(refreshToken: String) // 刷新令牌
    case getUserInfo(params: RequestParametersConvertible) // 获取用户信息
    case forgetPassword(params: RequestParametersConvertible) // 忘记密码
}

extension LoginService: GeneralAPIService {
    var path: String {
        switch self {
        case .login: return APIConstants.UserApi + "/login"
        case .sendCode: return APIConstants.UserApi + "/send-code"
        case .refreshToken: return APIConstants.UserApi + "/refresh"
        case .getUserInfo: return APIConstants.UserApi + "/userinfo"
        case .forgetPassword: return APIConstants.UserApi + "/forget-password"
        }
    }

    var task: Task {
        switch self {
        case let .login(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .sendCode(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .refreshToken(refreshToken):
            return .requestParameters(
                parameters: ["refresh_token": refreshToken],
                encoding: URLEncoding.default
            )
        case let .getUserInfo(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .forgetPassword(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        }
    }

    /// 认证类型配置（Moya AccessTokenAuthorizable协议）
    var authorizationType: AuthorizationType? {
        switch self {
        case .login, .sendCode, .refreshToken, .forgetPassword: return nil // 登录相关接口不需要认证头
        default: return .bearer // 其他接口需要认证
        }
    }
}
