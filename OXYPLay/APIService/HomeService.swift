import Foundation
import Moya

/// 首页服务API
enum HomeService {
    case adlist // 广告列表
    case detail(params: RequestParametersConvertible) // 广告详情
    case offdetail(params: RequestParametersConvertible) // 官方商品详情
    case commentList(params: RequestParametersConvertible) // 商品评价列表
    case commenCreate(params: RequestParametersConvertible) // 商品评价创建
    case delete(params: RequestParametersConvertible) // 删除评论
    case recommendGoods(params: RequestParametersConvertible) // 推荐好物
}

extension HomeService: GeneralAPIService {
    var path: String {
        switch self {
        case .adlist: return APIConstants.HomeApi + "/adlist"
        case .detail: return APIConstants.HomeApi + "/detail"
        case .offdetail: return APIConstants.HomeApi + "/offdetail"
        case .commentList: return APIConstants.HomeApi + "/comment-list"
        case .commenCreate: return APIConstants.HomeApi + "/comment-create"
        case .delete: return APIConstants.HomeApi + "/delete"
        case .recommendGoods: return APIConstants.HomeApi + "/recommend-goods"

        }
    }

    var task: Task {
        switch self {
        case .adlist:
            return .requestPlain
        case let .detail(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .offdetail(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .commentList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .commenCreate(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .delete(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .recommendGoods(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        }
        
    }
}
