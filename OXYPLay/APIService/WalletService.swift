//
//  WalletService.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/24.
//

import Foundation
import Moya

/// 钱包API服务
/// 提供钱包相关的所有接口操作，包括余额查询、账单明细、提现记录和提现申请
enum WalletService {
    /// 钱包概览接口（余额 + 优惠券）
    /// - Parameter params: 请求参数（通常为空，需要用户登录态）
    case summary(params: RequestParametersConvertible)
    
    /// 钱包账单明细接口
    /// - Parameter params: 包含month（可选）、type（可选）的请求参数
    case transactionList(params: RequestParametersConvertible)
    
    /// 提现明细接口
    /// - Parameter params: 包含month（可选）、page（可选）、limit（可选）的请求参数
    case withdrawList(params: RequestParametersConvertible)
    
    /// 卖家提现申请接口
    /// - Parameter params: 包含amount（提现金额）的请求参数
    case withdrawRequest(params: RequestParametersConvertible)

    /// 充值申请接口
    /// - Parameter params: 包含amount（充值金额）、payment_method（支付方式）的请求参数
    case rechargeRequest(params: RequestParametersConvertible)
}

extension WalletService: GeneralAPIService {
    var path: String {
        switch self {
        case .summary:
            return APIConstants.WalletApi + "/summary"
        case .transactionList:
            return APIConstants.WalletApi + "/transaction-list"
        case .withdrawList:
            return APIConstants.WalletApi + "/withdraw-list"
        case .withdrawRequest:
            return APIConstants.OrderApi + "/withdraw-request"
        case .rechargeRequest:
            return APIConstants.WalletApi + "/recharge-request"
        }
    }
    
    var task: Task {
        switch self {
        case let .summary(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
            
        case let .transactionList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
            
        case let .withdrawList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
            
        case let .withdrawRequest(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )

        case let .rechargeRequest(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        }
    }
}
