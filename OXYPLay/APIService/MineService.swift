//
//  MineService.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/18.
//


enum MineService {
    /// 获取互相关注列表接口
    /// - Parameters:
    ///   - page: 页码，默认 1
    ///   - limit: 每页条数，默认 10
    case friendsList(params: RequestParametersConvertible)
    /// 获取关注列表接口
    /// - Parameters:
    ///   - keyword: 搜索关键词（匹配昵称/uid）
    ///   - page: 页码，默认 1
    ///   - limit: 每页条数，默认 10
    case followList(params: RequestParametersConvertible)
    
    /// 粉丝列表接口
    /// - Parameters:
    ///   - keyword: 模糊搜索关键字（昵称或 uid）
    ///   - page: 页码，默认 1
    ///   - limit: 每页数量，默认 10
    case fansList(params: RequestParametersConvertible)
    
    /// 关注用户接口
    /// - Parameters:
    ///   - followee_id: 要关注的用户 ID
    case follow(params: RequestParametersConvertible)
    
    /// 取消关注用户接口
    /// - Parameters:
    ///   - followee_id: 要取消关注的用户 ID
    case unfollow(params: RequestParametersConvertible)
    
    /// 用户收货地址列表接口
    /// - Parameters:
    /// - type ： 1.收货地址 2退货地址
    case addressList(params: RequestParametersConvertible)
    
    /// 删除收货地址接口（逻辑删除）
    /// - Parameters:
    ///   - address_id: 要删除的地址ID
    case deleteAddress(params: RequestParametersConvertible)
    
    /// 设置默认收货地址接口
    /// - Parameters:
    ///   - address_id: 要设为默认的地址ID
    case setDefaultAddress(params: RequestParametersConvertible)
    
    /// 添加收货地址接口
    /// - Parameters:
    ///   - recipient_name: 收件人姓名
    ///   - phone: 收件人手机号
    ///   - province: 省份
    ///   - city: 城市
    ///   - district: 区/县
    ///   - detail: 详细地址（如门牌号等）
    ///   - postal_code: 邮政编码
    ///   - set_default: 是否设置为默认地址（默认否）
    case createAddress(params: RequestParametersConvertible)
    
    /// 修改收货地址接口
    /// - Parameters:
    ///   - address_id: 要修改的地址 ID
    ///   - recipient_name: 收件人姓名
    ///   - phone: 收件人手机号
    ///   - province: 省份
    ///   - city: 城市
    ///   - district: 区/县
    ///   - detail: 详细地址（如门牌号等）
    ///   - postal_code: 邮政编码
    ///   - set_default: 是否设置为默认地址（true/false）
    case updateAddress(params: RequestParametersConvertible)
    
    /// 用户优惠券列表接口
    /// - Parameters:
    ///   - advertiser_id: 广告主 ID，用于匹配专属券
    ///   - order_amount: 当前订单金额，用于满减过滤
    case couponList(params: RequestParametersConvertible)
    
    /// 校验单张优惠券是否可用接口
    /// - Parameters:
    ///   - coupon_id: 用户优惠券 ID
    ///   - order_amount: 当前订单金额
    ///   - advertiser_id: 商品所属广告主 ID（专属券时必传）
    case checkCoupon(params: RequestParametersConvertible)
    
    /// 标记优惠券为已使用
    /// - Parameters:
    ///   - coupon_id: 用户优惠券 ID
    case useCoupon(params: RequestParametersConvertible)
    
    /// 推荐关注用户接口）
    case recommendFollow(params: RequestParametersConvertible)
    

    /// 获取我卖出的订单列表（卖家视角）
    /// - Parameters:
    ///   - page: 页码，默认 1
    ///   - page_size: 每页数量，默认 10
    case soldOrders(params: RequestParametersConvertible)

    /// 获取我买到的订单列表（买家视角）
    /// - Parameters:
    ///   - page: 页码，默认 1
    ///   - page_size: 每页数量，默认 10
    case boughtOrders(params: RequestParametersConvertible)

}

extension MineService: GeneralAPIService {
    var method: Moya.Method {
        return .post
    }
    var path: String {
        switch self {
        case .friendsList: return APIConstants.MyApi + "/friends-list"
        case .followList: return APIConstants.UserApi + "/follow-list"
        case .fansList: return APIConstants.UserApi + "/fans-list"
        case .follow: return APIConstants.UserApi + "/follow"
        case .unfollow: return APIConstants.UserApi + "/unfollow"
        case .addressList: return APIConstants.UserApi + "/address"
        case .deleteAddress: return APIConstants.UserApi + "/delete"
        case .setDefaultAddress: return APIConstants.UserApi + "/set-default"
        case .createAddress: return APIConstants.UserApi + "/address-create"
        case .updateAddress: return APIConstants.UserApi + "/address-update"
        case .couponList: return APIConstants.UserApi + "/coupon-list"
        case .checkCoupon: return APIConstants.UserApi + "/check"
        case .useCoupon: return APIConstants.UserApi + "/use"
        case .recommendFollow: return APIConstants.ServiceApi + "/recommend-follow"
        case .soldOrders: return APIConstants.MyApi + "/sold-orders"
        case .boughtOrders: return APIConstants.MyApi + "/bought-orders"
        }
    }

    var task: Task {
        switch self {
        case let .friendsList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .followList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .fansList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .follow(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .unfollow(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .addressList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .deleteAddress(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .setDefaultAddress(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .createAddress(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .updateAddress(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .couponList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .checkCoupon(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .useCoupon(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .recommendFollow(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .soldOrders(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .boughtOrders(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        }
    }
}
