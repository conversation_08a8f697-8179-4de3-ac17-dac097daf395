import Foundation
import Moya
import UIKit

/// 上传服务API
enum UploadService {
    case uploadImage(UIImage, fileName: String, mimeType: String)
    case uploadFile(URL)
}

// MARK: - TargetType
extension UploadService: TargetType {
    var baseURL: URL {
        return URL(string: APIConstants.baseURL)!
    }
    
    var path: String {
        switch self {
        case .uploadImage:
            return "/api/v1/my/upload"
        case .uploadFile:
            return "/api/v1/my/upload"
        }
    }
    
    var method: Moya.Method {
        return .post
    }
    
    var task: Moya.Task {
        switch self {
        case .uploadImage(let image, let fileName, let mimeType):
            guard let imageData = image.jpegData(compressionQuality: 0.7) else {
                return .requestPlain
            }
            
            let formData = MultipartFormData(provider: .data(imageData), name: "file", fileName: fileName, mimeType: mimeType)
            return .uploadMultipart([formData])
            
        case .uploadFile(let fileURL):
            let formData = MultipartFormData(provider: .file(fileURL), name: "file")
            return .uploadMultipart([formData])
        }
    }
    
    var headers: [String : String]? {
        return ["Content-Type": "multipart/form-data"]
    }
    
    var sampleData: Data {
        return Data()
    }
} 
