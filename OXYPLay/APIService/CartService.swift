//
//  CartService.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/19.
//

import Foundation
import Moya

/// 购物车API服务
/// 提供购物车相关的所有接口操作，包括添加、获取、更新、删除和清空购物车
enum CartService {
    /// 添加商品到购物车
    /// - Parameter params: 包含product_id、sku_id、quantity的请求参数
    case add(params: RequestParametersConvertible)

    /// 获取购物车列表
    /// - Parameter params: 请求参数（通常为空，需要用户登录态）
    case list(params: RequestParametersConvertible)

    /// 更新购物车商品数量
    /// - Parameter params: 包含id（购物车记录ID）、quantity（新数量）的请求参数
    case updateQuantity(params: RequestParametersConvertible)

    /// 删除购物车商品
    /// - Parameter params: 包含ids（购物车记录ID数组）的请求参数
    case remove(params: RequestParametersConvertible)

    /// 清空购物车
    /// - Parameter params: 请求参数（通常为空，需要用户登录态）
    case clear(params: RequestParametersConvertible)
}

extension CartService: GeneralAPIService {
    var path: String {
        switch self {
        case .add:
            return APIConstants.CartApi + "/add"
        case .list:
            return APIConstants.CartApi + "/cart-list"
        case .updateQuantity:
            return APIConstants.CartApi + "/update-quantity"
        case .remove:
            return APIConstants.CartApi + "/remove"
        case .clear:
            return APIConstants.CartApi + "/clear"
        }
    }

    var task: Task {
        switch self {
        case let .add(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )

        case let .list(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )

        case let .updateQuantity(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )

        case let .remove(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )

        case let .clear(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        }
    }
}
