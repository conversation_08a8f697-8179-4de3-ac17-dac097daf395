//
//  CommentService.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/8.
//


/// 评价API
enum CommentService {
    case create(params: RequestParametersConvertible)
    case list(params: RequestParametersConvertible)
    case delete(params: RequestParametersConvertible)
    case like(params: RequestParametersConvertible)
    case likeToggle(params: RequestParametersConvertible)
    case favoriteToggle(params: RequestParametersConvertible)
    case regionList(params: RequestParametersConvertible)
}

extension CommentService: GeneralAPIService {
    var path: String {
        switch self {
        case .create: return APIConstants.CommentApi + "/create"
        case .list: return APIConstants.CommentApi + "/list"
        case .delete: return APIConstants.CommentApi + "/delete"
        case .like: return APIConstants.CommentApi + "/like"
        case .likeToggle: return APIConstants.CommentApi + "/like-toggle"
        case .favoriteToggle: return APIConstants.CommentApi + "/favorite-toggle"
        case .regionList: return "/api/v1/common/region-list"
        }
    }

    var task: Task {
        switch self {
        case let .create(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .list(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .delete(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .like(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .likeToggle(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .favoriteToggle(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .regionList(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        }
        
    }
}
