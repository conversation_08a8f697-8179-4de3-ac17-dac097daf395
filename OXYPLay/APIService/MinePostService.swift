//
//  MinePostService.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import Foundation
import Moya

/// 我的帖子相关API服务
enum MinePostService {
    /// 我发布的帖子列表接口（含草稿）
    case myPosts(params: RequestParametersConvertible)
    
    /// 我收藏的帖子接口
    case favoritePosts(params: RequestParametersConvertible)
    
    /// 我点赞的帖子接口
    case likedPosts(params: RequestParametersConvertible)
    
    /// 我发布的已下架服务帖接口
    case offlinePosts(params: RequestParametersConvertible)
    
    /// 用户浏览记录列表接口
    case browseHistory(params: RequestParametersConvertible)
    
    /// 删除草稿接口
    case deleteDraft(params: RequestParametersConvertible)
}

extension MinePostService: GeneralAPIService {
    var path: String {
        switch self {
        case .myPosts:
            return APIConstants.MyApi + "/my-posts"
        case .favoritePosts:
            return APIConstants.MyApi + "/favorites"
        case .likedPosts:
            return APIConstants.MyApi + "/likes"
        case .offlinePosts:
            return APIConstants.MyApi + "/my-offline-posts"
        case .browseHistory:
            return APIConstants.ServiceDetailApi + "/history-list"
        case .deleteDraft:
            return APIConstants.MyApi + "/delete-draft"
        }
    }
    
    var task: Task {
        switch self {
        case let .myPosts(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
            
        case let .favoritePosts(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
            
        case let .likedPosts(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
            
        case let .offlinePosts(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
            
        case let .browseHistory(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
            
        case let .deleteDraft(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        }
    }
}
