//
//  MineFollowTableCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/18.
//

import UIKit
import SnapKit
import Combine
import Kingfisher

class MineFollowTableCell: UITableViewCell {

    // MARK: - 属性

    var userModel: MineAddFriendItemModel? {
        didSet {
            updateUI()
        }
    }

    // 关注按钮点击回调
    var followButtonTapped: ((MineAddFriendItemModel?) -> Void)?
    // 关闭按钮点击回调
    var closeButtonTapped: ((MineAddFriendItemModel?) -> Void)?

    // MARK: - UI组件

    // 头像
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 18
        imageView.layer.masksToBounds = true
        imageView.backgroundColor = .random
        return imageView
    }()

    // 用户名
    private lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.text = "hahhahaha"
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textColor = UIColor(hexString: "2B2C2F")
        label.numberOfLines = 1
        return label
    }()

    // 简介
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "hahhahaha"
        label.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        label.textColor = UIColor(hexString: "2B2C2F",transparency: 0.64)
        label.numberOfLines = 1
        return label
    }()

    private lazy var stackView = UIStackView().then{
        $0.axis = .horizontal
        $0.spacing = 20
        $0.alignment = .center
    }
    // 关注按钮
    private lazy var followButton: BaseButton = {
        let button = BaseButton()
        button.setTitle("关注", for: .normal)
        button.setTitle("已关注", for: .selected)
        button.setTitleColor(.white, for: .normal)
        button.setTitleColor(color_2B2C2F64, for: .selected)
        button.setImage(UIImage(named: "addfriend_add"), for: .normal)
        button.backgroundColor = color_blue
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        button.isRounded = true
        return button
    }()

    // 关闭按钮
    lazy var closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.isHidden = true
        button.setImage(UIImage(named: "addfriend_close"), for: .normal)
        return button
    }()

    // MARK: - 初始化

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupBindings()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - UI设置

    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .white

        contentView.addSubview(avatarImageView)
        contentView.addSubview(usernameLabel)
        contentView.addSubview(descriptionLabel)
        contentView.addSubview(stackView)
        stackView.addArrangedSubview(followButton)
        stackView.addArrangedSubview(closeButton)

        setupConstraints()
    }

    private func setupConstraints() {
        // 头像约束
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalTo(12)
            make.bottom.equalTo(-12)
            make.width.height.equalTo(36)
        }

        stackView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalTo(-12)
        }
        // 关闭按钮约束
        closeButton.snp.makeConstraints { make in
            make.width.height.equalTo(18)
        }

        // 关注按钮约束
        followButton.snp.makeConstraints { make in
            make.width.equalTo(60)
            make.height.equalTo(28)
        }

        // 用户名约束
        usernameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.right.lessThanOrEqualTo(stackView.snp.left).offset(-12)
            make.top.equalTo(avatarImageView.snp.top).offset(2)
        }

        // 简介约束
        descriptionLabel.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.right.lessThanOrEqualTo(stackView.snp.left).offset(-12)
            make.bottom.equalTo(avatarImageView.snp.bottom).offset(-2)
        }
    }

    func setupBindings() {
        // 关注按钮点击
        followButton.addTarget(self, action: #selector(followButtonAction), for: .touchUpInside)

        // 关闭按钮点击
        closeButton.addTarget(self, action: #selector(closeButtonAction), for: .touchUpInside)
    }

    // MARK: - 按钮事件

    @objc private func followButtonAction() {
        followButtonTapped?(userModel)
    }

    @objc private func closeButtonAction() {
        closeButtonTapped?(userModel)
    }

    // MARK: - 数据更新

    private func updateUI() {
        guard let userModel = userModel else { return }

        // 设置用户名
        usernameLabel.text = userModel.nickname

        // 设置头像
        if let avatarUrl = URL(string: userModel.avatar) {
            avatarImageView.kf.setImage(with: avatarUrl, placeholder: UIImage(named: "placeholder_avatar"))
        } else {
            avatarImageView.image = UIImage(named: "placeholder_avatar")
        }

        // 设置简介
        descriptionLabel.text = userModel.description.isEmpty ? "这个人很懒，什么都没有留下" : userModel.description

        // 更新按钮状态
        updateButtonState()
    }

    /// 根据列表类型更新按钮状态
    private func updateButtonState() {
        guard let state = userModel?.is_mutual_follow else { return }
        switch state {
        case 0:
            followButton.setTitle("关注", for: .normal)
            followButton.setTitleColor(.white, for: .normal)
            followButton.backgroundColor = color_blue
            followButton.setImage(UIImage(named: "addfriend_add"), for: .normal)
        case 1:
            followButton.setTitle("已关注", for: .normal)
            followButton.setTitleColor(color_2B2C2F72, for: .normal)
            followButton.backgroundColor = UIColor(hexString: "788092", transparency: 0.08)
            followButton.setImage(nil, for: .normal)
        case 2:
            followButton.setTitle("互相关注", for: .normal)
            followButton.setTitleColor(color_blue, for: .normal)
            followButton.backgroundColor = .white
            followButton.borderColor = color_blue
            followButton.borderWidth = 1
            followButton.setImage(nil, for: .normal)
        default:
            print("非法数据")
        }
    }

}

