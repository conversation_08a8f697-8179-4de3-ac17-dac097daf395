//
//  MineRecommendCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine

protocol MineRecommendCellDelegate: AnyObject {
    /// 点赞按钮点击事件
    func recommendCellDidTapLike(_ model: RecommendModel)
    /// 删除草稿按钮点击事件
    func draftDidTapDelete(_ model: RecommendModel)
}

/// 我的推荐Cell
class MineRecommendCell: UICollectionViewCell {
    
    // MARK: - Properties
    
    weak var delegate: MineRecommendCellDelegate?
    private var model: RecommendModel?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - UI Components
    
    /// 主图片视图
    lazy var mainImageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.backgroundColor = .secondarySystemBackground
        iv.layer.cornerRadius = 8
        iv.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        iv.masksToBounds = true
        return iv
    }()
    
    /// 类型按钮
    lazy var typeButton: BaseButton = {
        let button = BaseButton()
        button.backgroundColor = UIColor(hexString: "000000", transparency: 0.32)
        button.layer.cornerRadius = 8
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 11, weight: .medium)
        button.horizontalPadding = 4
        return button
    }()
   
    /// 删除按钮
    private lazy var deleteButton: BaseButton = {
        let button = BaseButton()
        button.setImage(UIImage(named: "mine_delete"), for: .normal)
        button.backgroundColor = UIColor(hexString: "000000", transparency: 0.32)
        button.layer.cornerRadius = 12
        button.layer.masksToBounds = true
        return button
    }()
    
    /// 已下架蒙版（覆盖整个cell）
    private lazy var offlineMaskView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hexString: "000000", transparency: 0.56) // 降低透明度到30%
        view.layer.cornerRadius = 12 // 与cell的圆角保持一致
        view.masksToBounds = true
        view.isHidden = true
        return view
    }()
    
    /// 已下架标签
    private lazy var offlineLabel: UILabel = {
        let label = UILabel()
        label.text = "已下架"
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textAlignment = .center
        return label
    }()
    
    /// 内容容器
    private lazy var contentStack: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 8
        stack.alignment = .fill
        stack.distribution = .fill
        return stack
    }()
    
    /// 标题标签
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = color_3D3E40
        label.numberOfLines = 2
        return label
    }()
    
    /// 价格标签
    lazy var priceLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = color_FF8C00
        return label
    }()
    
    /// 底部容器
    private lazy var bottomStack: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.spacing = 8
        stack.alignment = .fill
        stack.distribution = .fill
        return stack
    }()
    
    /// 头像
    lazy var avatarImageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.backgroundColor = .secondarySystemBackground
        iv.layer.cornerRadius = 12
        iv.masksToBounds = true
        return iv
    }()
    
    /// 用户名标签
    lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textColor = color_3D3E40
        return label
    }()
    
    /// 点赞按钮
    private lazy var likeButton = BaseButton().then {
        $0.setImage(UIImage(named: "home_like_select")?.resize(to: CGSize(width: 12, height: 11)), for: .selected)
        $0.setImage(UIImage(named: "home_like")?.resize(to: CGSize(width: 12, height: 11)), for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        $0.setTitleColor(color_2B2C2F72, for: .normal)
        $0.imagePosition = .left
        $0.spacing = 4
    }

    /// 弹性占位视图
    private lazy var spacerView = UIView().then {
        $0.setContentHuggingPriority(UILayoutPriority(1), for: .horizontal)
        $0.setContentCompressionResistancePriority(UILayoutPriority(1), for: .horizontal)
    }

    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupBindings()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        contentView.layer.cornerRadius = 12
        contentView.layer.masksToBounds = true
        contentView.backgroundColor = .white
        
        contentView.addSubview(mainImageView)
        contentView.addSubview(contentStack)

        mainImageView.addSubview(typeButton)

        // 将蒙版添加到contentView上，覆盖整个cell
        contentView.addSubview(offlineMaskView)
        offlineMaskView.addSubview(offlineLabel)
        offlineMaskView.addSubview(deleteButton)

        contentStack.addArrangedSubview(titleLabel)
        contentStack.addArrangedSubview(priceLabel)
        contentStack.addArrangedSubview(bottomStack)
        
        bottomStack.addArrangedSubview(avatarImageView)
        bottomStack.addArrangedSubview(usernameLabel)
        bottomStack.addArrangedSubview(spacerView)
        bottomStack.addArrangedSubview(likeButton)
    }
    
    private func setupConstraints() {
        mainImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
        }
        
        typeButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(8)
            make.height.equalTo(16)
            make.width.greaterThanOrEqualTo(30)
        }
        
        offlineMaskView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        offlineLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        deleteButton.snp.makeConstraints { make in
            make.bottom.right.equalTo(-12)
        }
        
        contentStack.snp.makeConstraints { make in
            make.top.equalTo(mainImageView.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(8)
            make.right.equalToSuperview().offset(-8)
            make.bottom.equalToSuperview().offset(-8)
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.width.height.equalTo(24)
        }

        // 设置likeButton的约束，确保有合适的点击区域
        likeButton.snp.makeConstraints { make in
            make.height.equalTo(24)
        }
    }
    
    private func setupBindings() {
        likeButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let model = self.model else { return }
                self.delegate?.recommendCellDidTapLike(model)
            }
            .store(in: &cancellables)
        deleteButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let model = self.model else { return }
                self.delegate?.draftDidTapDelete(model)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Configuration
    
    /// 配置推荐数据
    func configure(with model: RecommendModel, pageType: MinePageType) {
        self.model = model
        
        // 设置图片
        if let imageUrl = URL(string: model.img) {
            mainImageView.kf.setImage(with: imageUrl, placeholder: UIImage(named: "placeholder"))
        }
        
        // 设置标题
        titleLabel.text = model.title
        
        // 设置价格
        if !model.price.isEmpty {
            priceLabel.text = model.price.formattedPrice
            priceLabel.isHidden = false
        } else {
            priceLabel.isHidden = true
        }
        
        // 设置类型
        typeButton.setTitle(model.recommendType.displayName, for: .normal)
        
        // 设置用户信息
        usernameLabel.text = model.user?.nickname ?? ""
        if let avatarUrl = URL(string: model.user?.avatar ?? "") {
            avatarImageView.kf.setImage(with: avatarUrl, placeholder: UIImage(named: "default_avatar"))
        }
        
        // 设置点赞状态
        likeButton.setTitle("\(model.like_count)", for: .normal)
        likeButton.isSelected = model.is_liked
        
        // 已下架/草稿页面显示蒙版
        offlineMaskView.isHidden = !(pageType == .draft || pageType == .offline)
        deleteButton.isHidden = pageType != .draft
        offlineLabel.text = pageType == .offline ? "已下架" : "本地草稿"
        
    }
    
    // MARK: - 静态高度计算方法
    
    /// 计算Cell高度
    static func calculateHeight(for model: RecommendModel, width: CGFloat) -> CGFloat {
        // 图片高度比例
        let imageHeight = width / model.imageSize
        var bottomHeight = 80.0
        
        // 如果有价格，增加高度
        if !model.price.isEmpty {
            bottomHeight += 20
        }
        
        // 如果标题不需要换行，减少高度
        if !isLabelWrappingRequired(text: model.title, font: UIFont.systemFont(ofSize: 14, weight: .regular), labelWidth: width - 16) {
            bottomHeight -= 18
        }
        
        let totalHeight = imageHeight + bottomHeight
        
        return ceil(totalHeight)
    }

    /// 判断标签是否需要换行
    static func isLabelWrappingRequired(text: String, font: UIFont, labelWidth: CGFloat) -> Bool {
        // 1. 计算单行文本所需宽度
        let attributes: [NSAttributedString.Key: Any] = [.font: font]
        let singleLineWidth = (text as NSString).size(withAttributes: attributes).width

        // 2. 考虑布局边距（系统自动添加约4pt内边距）
        let systemPadding: CGFloat = 4.0
        let effectiveLabelWidth = labelWidth - systemPadding

        // 3. 判断是否需要换行
        return singleLineWidth > effectiveLabelWidth
    }
}
