//
//  MineNavView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/11.
//
protocol MineNavViewDelegate: AnyObject {
    /// 菜单按钮点击
    func mineNavViewDidTapMenuButton(_ navView: MineNavView)
    /// 分享按钮点击
    func mineNavViewDidTapShareButton(_ navView: MineNavView)
    /// 设置按钮点击
    func mineNavViewDidTapSettingButton(_ navView: MineNavView)
}

class MineNavView: BaseView {
    // MARK: - 属性
    
    /// 代理
    weak var delegate: MineNavViewDelegate?
    
    /// 菜单按钮
    private lazy var menuButton = UIButton(type: .system).then {
        $0.setImage(UIImage(named: "home_leftmenu"), for: .normal)
        $0.tintColor = .white
        $0.addTarget(self, action: #selector(menuButtonTapped), for: .touchUpInside)
    }
    
    private lazy var shareButton = UIButton().then {
        $0.setImage(UIImage(named: "mine_share"), for: .normal)
        $0.addTarget(self, action: #selector(shareButtonTapped), for: .touchUpInside)
    }
    private lazy var settingButton = UIButton().then {
        $0.setImage(UIImage(named: "mine_setting"), for: .normal)
        $0.addTarget(self, action: #selector(settingButtonTapped), for: .touchUpInside)
    }
    // MARK: - 配置方法
    
    override func configUI() {
        super.configUI()
        
        // 添加子视图
        addSubview(menuButton)
        addSubview(shareButton)
        addSubview(settingButton)
      
    }
    
    override func configLayout() {
        super.configLayout()
        
        // 主按钮约束
        menuButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(18.5)
            make.width.height.equalTo(24)
            make.top.equalTo(ScreenInfo.statusBarHeight + 13)
        }
        
        
        settingButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalTo(menuButton)
        }
    
        shareButton.snp.makeConstraints { make in
            make.right.equalTo(settingButton.snp.left).offset(-12)
            make.centerY.equalTo(menuButton)
        }
    }
    
    // MARK: - 按钮事件
    
    @objc private func menuButtonTapped() {
        delegate?.mineNavViewDidTapMenuButton(self)
    }
    
    @objc private func shareButtonTapped() {
        delegate?.mineNavViewDidTapShareButton(self)
    }
    
    @objc private func settingButtonTapped() {
        delegate?.mineNavViewDidTapSettingButton(self)
    }
}
