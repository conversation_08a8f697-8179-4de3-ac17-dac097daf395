//
//  MineTableHeaderView.swift

import UIKit
import SnapKit
import Then

// MARK: - 代理协议
protocol MineTableHeaderViewDelegate: AnyObject {
    /// 点击编辑资料按钮
    func mineTableHeaderViewDidTapEditProfile(_ headerView: MineTableHeaderView)
    /// 点击邀请朋友按钮
    func mineTableHeaderViewDidTapInviteFriends(_ headerView: MineTableHeaderView)
    /// 点击查看详情按钮
    func mineTableHeaderViewDidTapViewDetails(_ headerView: MineTableHeaderView)
    /// 点击交易相关按钮
    func mineTableHeaderViewDidTapTransaction(_ headerView: MineTableHeaderView, type: MineTableHeaderView.TransactionType)
    /// 点击关注数
    func mineTableHeaderViewDidTapFollowing(_ headerView: MineTableHeaderView)
    /// 点击粉丝数
    func mineTableHeaderViewDidTapFollowers(_ headerView: MineTableHeaderView)
}

class MineTableHeaderView: BaseView {
    
    // MARK: - 枚举
    enum TransactionType {
        case wallet      // 钱包
        case selling     // 我卖出的
        case purchased   // 我买到的
        case cart        // 购物车
    }
    
    // MARK: - 属性
    weak var delegate: MineTableHeaderViewDelegate?
    
    // MARK: - UI组件
    
    // 头像
    private lazy var avatarImageView = UIImageView().then {
        $0.layer.cornerRadius = 35
        $0.layer.masksToBounds = true
        $0.contentMode = .scaleAspectFill
        $0.backgroundColor = UIColor(hexString: "2A72FF", transparency: 0.5)!
    }
    
    // 性别图标
    private lazy var genderImageView = UIImageView().then {
        $0.layer.cornerRadius = 10
        $0.layer.masksToBounds = true
        $0.contentMode = .center
    }
    
    // 用户名
    private lazy var usernameLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        $0.textColor = .white
    }
    
    // 用户ID
    private lazy var userIdLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textColor = .white.withAlphaComponent(0.48)
    }
    
    // 个人简介
    private lazy var bioLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        $0.textColor = .white.withAlphaComponent(0.8)
        $0.numberOfLines = 1
    }
    
    // 关注
    private lazy var followingLabel = UILabel().then {
        $0.text = "关注"
        $0.font = UIFont.systemFont(ofSize: 12,weight: .regular)
        $0.textColor = .white.withAlphaComponent(0.8)
    }
    private lazy var followingCountLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .medium)
        $0.textColor = .white
    }
    // 关注点击区域
    private lazy var followingTapView = UIView().then {
        $0.backgroundColor = .clear
        $0.isUserInteractionEnabled = true
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(followingTapped))
        $0.addGestureRecognizer(tapGesture)
    }
    
    // 粉丝
    private lazy var followersLabel = UILabel().then {
        $0.text = "粉丝"
        $0.font = UIFont.systemFont(ofSize: 12,weight: .regular)
        $0.textColor = .white.withAlphaComponent(0.8)
    }
    // 粉丝数
    private lazy var followersCountLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .medium)
        $0.textColor = .white
    }
    // 粉丝点击区域
    private lazy var followersTapView = UIView().then {
        $0.backgroundColor = .clear
        $0.isUserInteractionEnabled = true
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(followersTapped))
        $0.addGestureRecognizer(tapGesture)
    }
    
    // 地区标签
    private lazy var locationLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textColor = .white
        $0.backgroundColor = UIColor.black.withAlphaComponent(0.08)
        $0.textAlignment = .center
        $0.cornerRadius = 19/2
        $0.setHorizontalPadding(5)
    }
    
    // 雪龄标签
    private lazy var snowAgeLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textColor = .white
        $0.backgroundColor = UIColor.black.withAlphaComponent(0.08)
        $0.cornerRadius = 19/2
        $0.setHorizontalPadding(5)
        $0.textAlignment = .center
    }
    
    // 编辑资料按钮
    private lazy var editProfileButton = BaseButton().then {
        $0.setTitle("编辑资料", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.backgroundColor = UIColor.black.withAlphaComponent(0.08)
        $0.borderColor = UIColor.white.withAlphaComponent(0.8)
        $0.borderWidth = 0.5
        $0.isRounded = true
        $0.addTarget(self, action: #selector(editProfileTapped), for: .touchUpInside)
    }
    
    // 邀请朋友领取优惠券背景
    private lazy var inviteBackgroundView = UIView().then {
        $0.layer.cornerRadius = 12
    }
    
    // 礼物图标
    private lazy var giftImageView = UIImageView().then {
        $0.image = UIImage(named: "mine_gift")
        $0.contentMode = .scaleToFill
    }
    
    // 邀请文字
    private lazy var inviteLabel = UILabel().then {
        $0.text = "邀请朋友领取专属优惠券礼包"
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = .white
    }
    
    // 查看详情按钮
    private lazy var viewDetailsButton = BaseButton().then {
        $0.setTitle("查看详情", for: .normal)
        $0.setImage(UIImage(named: "mine_arrow"), for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.imagePosition = .right
        $0.spacing = 4
        $0.addTarget(self, action: #selector(viewDetailsTapped), for: .touchUpInside)
    }
    
    
    // 我的交易标题
    private lazy var transactionTitleLabel = UILabel().then {
        $0.text = "我的交易"
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textColor = UIColor(hexString: "3D3E40")
    }
    
    // 交易选项容器
    private lazy var transactionContainer = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 12
    }
    
    // 交易按钮栈视图
    private lazy var transactionStackView = UIStackView().then {
        $0.axis = .horizontal
        $0.distribution = .fillEqually
        $0.alignment = .fill
        $0.spacing = 0
    }
    
    // 交易按钮
    private lazy var walletButton = createTransactionButton(
        image: UIImage(named: "mine_wallet")!,
        title: "钱包",
        type: .wallet
    )
    
    private lazy var sellingButton = createTransactionButton(
        image: UIImage(named: "mine_selling")! ,
        title: "我卖出的",
        type: .selling
    )
    
    private lazy var purchasedButton = createTransactionButton(
        image: UIImage(named: "mine_purchased")!,
        title: "我买到的",
        type: .purchased
    )
    
    private lazy var cartButton = createTransactionButton(
        image: UIImage(named: "mine_cart")!,
        title: "购物车",
        type: .cart
    )
    
    // MARK: - 初始化方法
    
    override func configUI() {
        addGradientBackground(colors: [UIColor(hexString: "#3D4962",transparency: 0.63)!, UIColor(hexString: "#8399C4",transparency: 0.72)!])

        // 添加子视图
        addSubview(avatarImageView)
        addSubview(genderImageView)
        addSubview(usernameLabel)
        addSubview(userIdLabel)
        addSubview(bioLabel)
        addSubview(followersLabel)
        addSubview(followingLabel)
        addSubview(followersCountLabel)
        addSubview(followingCountLabel)
        addSubview(followingTapView)
        addSubview(followersTapView)
        addSubview(locationLabel)
        addSubview(snowAgeLabel)
        addSubview(editProfileButton)
        
        // 邀请朋友视图
        addSubview(giftImageView)
        addSubview(inviteBackgroundView)
        inviteBackgroundView.addSubview(inviteLabel)
        inviteBackgroundView.addSubview(viewDetailsButton)
        
        // 交易视图
        addSubview(transactionContainer)
        
        // 添加交易按钮到栈视图
        transactionStackView.addArrangedSubview(walletButton)
        transactionStackView.addArrangedSubview(sellingButton)
        transactionStackView.addArrangedSubview(purchasedButton)
        transactionStackView.addArrangedSubview(cartButton)
        
        // 添加栈视图到容器
        transactionContainer.addSubview(transactionStackView)
        transactionContainer.addSubview(transactionTitleLabel)
        
        // 添加点击事件
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(inviteViewTapped))
        inviteBackgroundView.addGestureRecognizer(tapGesture)
        inviteBackgroundView.isUserInteractionEnabled = true
    }
    override func layoutSubviews() {
        super.layoutSubviews()
        // 头像布局
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.top.equalToSuperview().offset(58+ScreenInfo.statusBarHeight)
            make.width.height.equalTo(70)
        }
        
    
        // 用户名布局
        usernameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.top.equalTo(avatarImageView)
        }
        
        // 用户ID布局
        userIdLabel.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel.snp.right).offset(12)
            make.bottom.equalTo(usernameLabel)
        }
        
        // 个人简介布局
        bioLabel.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.top.equalTo(usernameLabel.snp.bottom).offset(12)
            make.right.equalToSuperview().offset(-20)
        }
        
        // 关注数布局
        followingLabel.snp.makeConstraints { make in
            make.left.equalTo(usernameLabel)
            make.top.equalTo(bioLabel.snp.bottom).offset(12)
        }
        
        followingCountLabel.snp.makeConstraints { make in
            make.left.equalTo(followingLabel.snp.right).offset(5)
            make.centerY.equalTo(followingLabel)
        }
        
        // 关注点击区域布局
        followingTapView.snp.makeConstraints { make in
            make.left.equalTo(followingLabel)
            make.right.equalTo(followingCountLabel)
            make.top.equalTo(followingLabel).offset(-5)
            make.bottom.equalTo(followingLabel).offset(5)
        }
        
        // 粉丝数布局
        followersLabel.snp.makeConstraints { make in
            make.left.equalTo(followingCountLabel.snp.right).offset(15)
            make.centerY.equalTo(followingLabel)
        }
        followersCountLabel.snp.makeConstraints { make in
            make.left.equalTo(followersLabel.snp.right).offset(5)
            make.centerY.equalTo(followingLabel)
        }
        
        // 粉丝点击区域布局
        followersTapView.snp.makeConstraints { make in
            make.left.equalTo(followersLabel)
            make.right.equalTo(followersCountLabel)
            make.top.equalTo(followersLabel).offset(-5)
            make.bottom.equalTo(followersLabel).offset(5)
        }
        // 性别图标布局
        genderImageView.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView)
            make.top.equalTo(avatarImageView.snp.bottom).offset(15)
            make.width.height.equalTo(20)
        }
        
        // 地区标签布局
        locationLabel.snp.makeConstraints { make in
            make.left.equalTo(genderImageView.snp.right).offset(10)
            make.centerY.equalTo(genderImageView)
            make.height.equalTo(19)
        }
        
        // 雪龄标签布局
        snowAgeLabel.snp.makeConstraints { make in
            make.left.equalTo(locationLabel.snp.right).offset(10)
            make.centerY.equalTo(genderImageView)
            make.height.equalTo(19)
        }
        
        // 编辑资料按钮布局
        editProfileButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-20)
            make.centerY.equalTo(locationLabel)
            make.width.equalTo(64)
            make.height.equalTo(23)
        }
        
        // 邀请朋友背景布局
        inviteBackgroundView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(24)
            make.right.equalToSuperview().offset(-24)
            make.top.equalTo(locationLabel.snp.bottom).offset(20)
            make.height.equalTo(50)
        }
        
        // 礼物图标布局
        giftImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.top.equalTo(locationLabel.snp.bottom).offset(20)
            make.height.equalTo(50)
        }
        
        // 邀请文字布局
        inviteLabel.snp.makeConstraints { make in
            make.left.equalTo(80)
            make.top.equalTo(viewDetailsButton.snp.centerY)
        }
        
        // 查看详情按钮布局
        viewDetailsButton.snp.makeConstraints { make in
            make.right.equalTo(-10)
            make.top.equalTo(10)
        
        }
        // 交易容器布局
        transactionContainer.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.top.equalTo(inviteBackgroundView.snp.bottom).offset(0)
            make.height.equalTo(111)
        }
        
        // 交易标题布局
        transactionTitleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalTo(12)
            make.height.equalTo(14)
        }
        
       
        // 交易栈视图布局
        transactionStackView.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.right.equalTo(0)
            make.top.equalTo(transactionTitleLabel.snp.bottom).offset(12)
            make.bottom.equalTo(-12)
        }
    }

    
    // MARK: - 辅助方法
    
    private func createTransactionButton(image: UIImage, title: String, type: TransactionType) -> BaseButton {
        let button = BaseButton()
        button.imagePosition = .top
        button.spacing = 8
        button.setImage(image, for: .normal)
        button.setTitle(title, for: .normal)
        button.setTitleColor(UIColor(hexString: "2B2C2F", transparency: 0.8), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        button.tintColor = .black
        
        // 使用tag存储类型
        switch type {
        case .wallet:
            button.tag = 0
        case .selling:
            button.tag = 1
        case .purchased:
            button.tag = 2
        case .cart:
            button.tag = 3
        }
        
        button.addTarget(self, action: #selector(transactionButtonTapped(_:)), for: .touchUpInside)
        
        return button
    }
    
    // MARK: - 事件处理
    
    @objc private func editProfileTapped() {
        delegate?.mineTableHeaderViewDidTapEditProfile(self)
    }
    
    @objc private func inviteViewTapped() {
        delegate?.mineTableHeaderViewDidTapInviteFriends(self)
    }
    
    @objc private func viewDetailsTapped() {
        delegate?.mineTableHeaderViewDidTapViewDetails(self)
    }
    
    @objc private func followingTapped() {
        delegate?.mineTableHeaderViewDidTapFollowing(self)
    }
    
    @objc private func followersTapped() {
        delegate?.mineTableHeaderViewDidTapFollowers(self)
    }
    
    @objc private func transactionButtonTapped(_ sender: UIButton) {
        let type: TransactionType
        
        switch sender.tag {
        case 0:
            type = .wallet
        case 1:
            type = .selling
        case 2:
            type = .purchased
        case 3:
            type = .cart
        default:
            type = .wallet
        }
        
        delegate?.mineTableHeaderViewDidTapTransaction(self, type: type)
    }
    
    // MARK: - 公共方法
    
    /// 更新用户信息
    func updateUserInfo() {
        guard let model = UserManager.shared.getCurrentUser() else { return }
        if let imageUrl = URL(string: model.avatar) {
            avatarImageView.kf.setImage(with: imageUrl, placeholder: UIImage(named: "有话说"))
        }
        genderImageView.image = UIImage(named: model.gender == 1 ? "mine_man":"mine_woman")
        usernameLabel.text = model.nickname
        userIdLabel.text = "ID:\(model.uid)"
        bioLabel.text = model.description
        followingCountLabel.text = model.follow_count
        followersCountLabel.text = model.fans_count
        locationLabel.text = model.location
        snowAgeLabel.text = model.ski_age

    }
}

