//
//  MineFollowTableHeaderView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/19.
//

class MineFollowTableHeaderView:BaseView{
    // 简介
    private lazy var interestLabel: UILabel = {
        let label = UILabel()
        label.text = "您可能感兴趣的人"
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textColor = UIColor(hexString: "2B2C2F",transparency: 0.8)
        label.numberOfLines = 1
        return label
    }()

    // 关注按钮
    private lazy var addressButton: BaseButton = {
        let button = BaseButton()
        button.setTitle("通讯录", for: .normal)
        button.setTitleColor(color_2B2C2F, for: .normal)
        button.setImage(UIImage(named: "addfriend_adressbook"), for: .normal)
        button.backgroundColor = .white
        button.titleLabel?.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        button.cornerRadius = 12
        button.masksToBounds = true
        return button
    }()
    private lazy var qrCodeButton: BaseButton = {
        let button = BaseButton()
        button.setTitle("二维码", for: .normal)
        button.setTitleColor(color_2B2C2F, for: .normal)
        button.setImage(UIImage(named: "addfriend_qrcode"), for: .normal)
        button.backgroundColor = .white
        button.titleLabel?.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        button.cornerRadius = 12
        button.masksToBounds = true
        return button
    }()
    override func configUI() {
        addSubview(addressButton)
        addSubview(qrCodeButton)
        addSubview(interestLabel)

    }
    override func configLayout() {
        addressButton.snp.makeConstraints { make in
            make.left.top.equalTo(12)
            make.height.equalTo(67)
            make.right.equalTo(self.snp.centerX).offset(-6)
        }
        qrCodeButton.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(67)
            make.left.equalTo(self.snp.centerX).offset(6)
        }
        interestLabel.snp.makeConstraints { make in
            make.left.equalTo(addressButton)
            make.top.equalTo(addressButton.snp.bottom).offset(24)
            make.bottom.equalTo(-12)
        }
    }
}
class MineNoFollowTableHeaderView:BaseView{
    // 简介
    private lazy var interestLabel: UILabel = {
        let label = UILabel()
        label.text = "您可能感兴趣的人"
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textColor = UIColor(hexString: "2B2C2F",transparency: 0.8)
        label.numberOfLines = 1
        return label
    }()

    // 关注按钮
    private lazy var noFollowButton: BaseButton = {
        let button = BaseButton()
        button.setTitle("还没有关注的人", for: .normal)
        button.setTitleColor(color_2B2C2F64, for: .normal)
        button.setImage(UIImage(named: "addfriend_adressbook"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        button.imagePosition = .top
        button.spacing = 10
        return button
    }()
   
    override func configUI() {
        addSubview(noFollowButton)
        addSubview(interestLabel)

    }
    override func configLayout() {
        noFollowButton.snp.makeConstraints { make in
            make.top.equalTo(16)
            make.centerX.equalToSuperview()
        }
     
        interestLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(noFollowButton.snp.bottom).offset(24)
            make.bottom.equalTo(-12)
        }
    }
}
