//
//  MineAddFriendViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/19.
//

class MineAddFriendViewModel: BaseViewModel {
    @Published var recommendUserList:[MineAddFriendItemModel] = []
    /// 关注结果发布者
    let followResultPublisher = PassthroughSubject<Bool, Never>()
    // MARK: - 初始化

    override init() {
        super.init()
        self.fetchRecommendUserListData(refresh: true)
    }

    // MARK: - 绑定

    override func setupBindings() {
        super.setupBindings()
    }

    // MARK: - 数据加载方法

    /// 重写刷新数据方法
    override func refreshData() {
        fetchRecommendUserListData(refresh: true)
    }

    /// 重写加载更多数据方法
    override func loadMoreData() {
        fetchRecommendUserListData(refresh: false)
    }

    /// 获取推荐用户列表数据
    func fetchRecommendUserListData(refresh: Bool = false) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

        // 构建请求参数
        let params: [String: Any] = [
            "page": currentPage,
            "limit": pageSize
        ]

        let service = MineService.recommendFollow(params: RequestParameters(params))

        // 使用便捷的分页数据请求方法
        requestPageData(service, type: MineAddFriendItemModel.self, isRefresh: refresh) { [weak self] newData, isRefresh in
            guard let self = self else { return }

            if isRefresh {
                self.recommendUserList = newData
            } else {
                self.recommendUserList.append(contentsOf: newData)
            }
        }
        .sink(
            receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    print("加载推荐用户失败: \(error.localizedDescription)")
                }
            },
            receiveValue: { _ in
                print("推荐用户加载成功")
            }
        )
        .store(in: &cancellables)
    }
    /// 关注用户
    /// - Parameters:
    ///   - userId: 用户ID
    func addFollow(userId: String) {
        // 构建请求参数
        let params = RequestParameters([
            "followee_id": userId
        ])
        
        // 发起网络请求
        requestModel(MineService.follow(params: params), type: EmptyResponse.self)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    print("关注操作失败: \(error.localizedDescription)")
                    self?.followResultPublisher.send(false)
                }
            } receiveValue: { [weak self] _ in
                // 操作成功
                self?.followResultPublisher.send(true)
                // 关注成功后，重新获取用户信息以获取最新的关注数
                UserManager.shared.refreshUserInfo()
            }
            .store(in: &cancellables)
    }

    // MARK: - 私有方法

   

}
