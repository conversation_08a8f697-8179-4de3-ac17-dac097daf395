//
//  MineFollowingViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import Foundation
import Combine


class MineFollowingViewModel: BaseViewModel {
    // MARK: - 属性
    
    /// 当前列表类型（关注/粉丝）
     var listType: MineFollowingListType = .following
    
    /// 用户列表数据
    @Published var userList: [MineAddFriendItemModel] = []

    /// 列表总数
    @Published var totalCount: Int = 0

    /// 搜索关键词
    @Published var keyword: String = ""
    
    /// 关注/取消关注结果发布者
    let followResultPublisher = PassthroughSubject<Bool, Never>()
        
    // MARK: - 数据加载
    
    /// 重写刷新数据方法
    override func refreshData() {
        fetchUserList(refresh: true)
    }

    /// 重写加载更多数据方法
    override func loadMoreData() {
        fetchUserList(refresh: false)
    }

    /// 加载用户列表数据
    /// - Parameter refresh: 是否刷新（重置页码）
    func fetchUserList(refresh: Bool = false) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

        // 构建请求参数
        let params = RequestParameters([
            "keyword": keyword,
            "page": currentPage,
            "limit": pageSize
        ])

        // 根据列表类型选择不同的API
        let service: MineService = listType == .following ? .followList(params: params) : .fansList(params: params)

        // 使用便捷的分页数据请求方法
        requestPageData(service, type: MineAddFriendItemModel.self, isRefresh: refresh) { [weak self] newData, isRefresh in
            guard let self = self else { return }

            if isRefresh {
                self.userList = newData
            } else {
                self.userList.append(contentsOf: newData)
            }
        }
        .sink(
            receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    print("获取列表失败: \(error.localizedDescription)")
                }
            },
            receiveValue: { [weak self] pageResponse in
                guard let self = self else { return }
                print("用户列表加载成功")
                // 更新总数
                self.totalCount = pageResponse.total
            }
        )
        .store(in: &cancellables)
    }
    
    /// 关注/取消关注用户
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - isFollow: 是否关注（true: 加关注, false: 取消关注）
    func toggleFollow(userId: String, isFollow: Bool) {
        // 构建请求参数
        let params = RequestParameters([
            "followee_id": userId
        ])
        
        // 选择API
        let service: MineService = isFollow ? .follow(params: params) : .unfollow(params: params)
        
        // 发起网络请求
        requestModel(service, type: EmptyResponse.self)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    print("关注操作失败: \(error.localizedDescription)")
                    self?.followResultPublisher.send(false)
                }
            } receiveValue: { [weak self] _ in
                // 操作成功
                self?.followResultPublisher.send(true)
                // 关注成功后，重新获取用户信息以获取最新的关注数
                UserManager.shared.refreshUserInfo()
                // 关注/取消关注成功后，重新获取列表以更新总数
                self?.fetchUserList(refresh: true)
            }
            .store(in: &cancellables)
    }
    
    /// 搜索用户
    /// - Parameter keyword: 搜索关键词
    func searchUsers(keyword: String) {
        self.keyword = keyword
        fetchUserList(refresh: true)
    }

   
}
