//
//  MineFollowingListController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import UIKit
import Combine
import SnapKit
/// 关注/粉丝列表类型
enum MineFollowingListType {
    /// 我关注的用户
    case following
    /// 关注我的用户（粉丝）
    case fans
}

class MineFollowingListController: JXBaseViewController {
    // MARK: - 属性

    var type:MineFollowingListType = .following
    let viewModel = MineFollowingViewModel()
    private var searchText: String = ""

    // 存储header中的标题label，用于更新总数显示
    private var headerTitleLabel: UILabel?

    // MARK: - UI组件

   
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.register(MineFollowTableCell.self, forCellReuseIdentifier: "MineFollowTableCell")
        tableView.delegate = self
        tableView.dataSource = self
        tableView.rowHeight = UITableView.automaticDimension
        tableView.estimatedRowHeight = 70
        tableView.backgroundColor = color_F6F8F9
        return tableView
    }()

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configLayout()
        setupBindings()
        loadData()
    }

    // MARK: - 配置UI

    override func configUI() {
        super.configUI()
        view.addSubview(tableView)
        viewModel.listType = type
    }

    override func configLayout() {
        super.configLayout()
        // 表格视图
        tableView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.left.right.bottom.equalToSuperview()
        }
    }

    override func setupBindings() {
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: tableView, with: viewModel)

        // 监听用户列表变化
        viewModel.$userList
            .receive(on: DispatchQueue.main)
            .sink { [weak self] userList in
                guard let self = self else { return }
                self.tableView.reloadData()
            }
            .store(in: &cancellables)


        // 监听关注操作结果
        viewModel.followResultPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    // 关注操作成功，刷新数据
                    self?.viewModel.fetchUserList(refresh: true)
                }
            }
            .store(in: &cancellables)

        // 监听总数变化
        viewModel.$totalCount
            .receive(on: DispatchQueue.main)
            .sink { [weak self] totalCount in
                self?.updateHeaderTitle(totalCount: totalCount)
            }
            .store(in: &cancellables)

    }

    // MARK: - 私有方法

    private func loadData() {
        viewModel.fetchUserList(refresh: true)
    }

    /// 更新header标题显示总数
    private func updateHeaderTitle(totalCount: Int) {
        headerTitleLabel?.text = type == .following ? "我的关注(\(totalCount))" : "我的粉丝(\(totalCount))"
    }

}

// MARK: - UITableViewDataSource

extension MineFollowingListController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.userList.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "MineFollowTableCell", for: indexPath) as! MineFollowTableCell
        let userModel = viewModel.userList[indexPath.row]
        cell.userModel = userModel
        // 设置关注按钮回调
        cell.followButtonTapped = { [weak self] userModel in
            guard let self = self, let userModel = userModel else { return }
            if type == .following || (type == .fans && userModel.is_mutual_follow == 2 ){
                self.viewModel.toggleFollow(userId: userModel.id, isFollow: false)
            }else {
                self.viewModel.toggleFollow(userId: userModel.id, isFollow: true)
            }
        }
        return cell
    }
}

// MARK: - UITableViewDelegate

extension MineFollowingListController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let view = UIView()
        let titleLabel = UILabel()

        // 初始显示，使用当前的总数
        let currentCount = viewModel.totalCount
        titleLabel.text = type == .following ? "我的关注(\(currentCount))" : "我的粉丝(\(currentCount))"
        titleLabel.textColor = UIColor(hexString: "#2B2C2F", transparency: 0.8)
        titleLabel.font = .systemFont(ofSize: 12, weight: .regular)

        // 保存titleLabel的引用
        headerTitleLabel = titleLabel

        view.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
        }
        return view
    }
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 36
    }
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        // 可以在这里添加点击用户头像跳转到用户详情页的逻辑
    }
}

// MARK: - UITextFieldDelegate

extension MineFollowingListController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
        if let text = textField.text {
            viewModel.searchUsers(keyword: text)
        }
        return true
    }
}
