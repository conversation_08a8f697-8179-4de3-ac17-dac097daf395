//
//  MineFollowingController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/22.
//

class MineFollowingController: JXBaseRootViewController {
    let followingVc = MineFollowingListController().then{
        $0.type = .following
    }
    let followerVc = MineFollowingListController().then{
        $0.type = .fans
    }

    override func viewDidLoad() {
        configSegement()
        super.viewDidLoad()
        configUI()
    }
    func configSegement(){
        shouldHideNavigationBar = false
        isNavSegementView = true
        // 配置属性
        titles = ["粉丝", "关注"]
        viewControllers = [followerVc,followingVc]
        // 配置UI
        segmentedViewTop = 0
        segmentedViewHeight = 36
        segmentedViewBackgroundColor = color_00000004
        segmentedViewCornerRadius = 18
        segmentedViewContentEdgeInsetLeft = 17
        segmentedViewContentEdgeInsetRight = 17
        segmentedViewMaskedCorners = [.layerMinXMinYCorner,.layerMinXMaxYCorner,.layerMaxXMaxYCorner,.layerMaxXMinYCorner]
        
        // 配置指示器
        indicatorType = .background
        indicatorColor = .white
        indicatorWidth = 28
        indicatorHeight = 30
        
        // 配置标题
        titleSelectedColor = color_2B2C2F
        titleNormalColor = color_2B2C2F64
        titleNormalFont = .systemFont(ofSize: 14, weight: .regular)
        titleSelectedFont = .systemFont(ofSize: 14, weight: .medium)
    }
    override func configUI(){
        followingVc.superJXVC = self
        followerVc.superJXVC = self
        segmentedView.frame = CGRectMake(0, 0, 120, segmentedViewHeight)
    }
}

