//
//  MineSettingController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import UIKit

class MineSettingController: BaseViewController {

    // MARK: - Menu Item 枚举
    enum MenuItem: CaseIterable {
        case accountSecurity
        case generalSettings
        case notificationSettings
        case privacySettings
        case address
        case customerService
        
        var identifier: String {
            switch self {
            case .accountSecurity: return "setting_accountSecurity"
            case .generalSettings: return "setting_generalSettings"
            case .notificationSettings: return "setting_notificationSettings"
            case .privacySettings: return "setting_privacySettings"
            case .address: return "setting_address"
            case .customerService: return "setting_customerService"
            }
        }
        
        var title: String {
            switch self {
            case .accountSecurity: return "账户安全"
            case .generalSettings: return "通用设置"
            case .notificationSettings: return "通知设置"
            case .privacySettings: return "隐私设置"
            case .address: return "地址管理"
            case .customerService: return "客服帮助"
            }
        }
        
        var iconString: String {
            switch self {
            case .accountSecurity: return "setting_accountSecurity"
            case .generalSettings: return "setting_generalSettings"
            case .notificationSettings: return "setting_notificationSettings"
            case .privacySettings: return "setting_privacySettings"
            case .address: return "slide_address"
            case .customerService: return "slide_customerService"
            }
        }
    }
    // MARK: - UI Components
    private lazy var listView: BaseListView = {
        let view = BaseListView()
        view.delegate = self
        return view
    }()
    
    private lazy var toolBar = BaseTabToolBar().then {
        $0.delegate = self
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configureListItems()
    }
    
    override func configUI() {
        title = "设置"

        // 配置 toolBar
        toolBar.configureLeftMultipleRightFull(leftItems: [], rightButtonTitle: "退出登录")

        view.addSubview(toolBar)
        toolBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
        view.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.top.equalTo(self.view.safeAreaLayoutGuide.snp.top)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(toolBar.snp.top).offset(0)
        }
    }
    
    private func configureListItems() {
        // 定义菜单项分组
        let section1: [ListItemConfig] = [
            makeListItemConfig(for: .accountSecurity),
            makeListItemConfig(for: .generalSettings),
            makeListItemConfig(for: .notificationSettings),
            makeListItemConfig(for: .privacySettings),
        ]
        let section2: [ListItemConfig] = [
            makeListItemConfig(for: .address)
        ]
        let section3: [ListItemConfig] = [
            makeListItemConfig(for: .customerService)
        ]
        
        // 设置列表项
        listView.setItems([section1, section2, section3])
    }
    
    private func makeListItemConfig(for menuItem: MenuItem) -> ListItemConfig {
        return ListItemConfig(
            type: .select,
            identifier: menuItem.identifier,
            iconString: menuItem.iconString,
            title: menuItem.title
        )
    }
}

// MARK: - BaseListViewDelegate
extension MineSettingController: BaseListViewDelegate {
    
    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        // 通过配置的 identifier 查找对应的 MenuItem
        guard let menuItem = MenuItem.allCases.first(where: { $0.identifier == config.identifier }) else {
            return
        }
        var pushVc:BaseViewController?
        switch menuItem {
        case .accountSecurity:
            pushVc = MineAccountSecurityController()
        case .generalSettings:
            pushVc = MineGeneralSettingsController()
        case .notificationSettings:
            pushVc = MineNotificationSettingsController()
        case .privacySettings:
            pushVc = MinePrivacySettingsController()
        case .address:
            pushVc = SelectAdressController()
        case .customerService:
            pushVc = CustomerServiceController()
        }
        guard let pushVc = pushVc else { return }
        pushVcHiddenTabBar(pushVc, animated: true)
        
    }
   
}
extension MineSettingController: TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        self.navigationController?.popViewController(animated: true)
        self.logOut()
    }
}
