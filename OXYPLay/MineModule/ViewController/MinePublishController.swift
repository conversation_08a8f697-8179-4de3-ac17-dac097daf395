//
//  MinePublishController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//


import UIKit
import Combine

class MinePublishController: JXBaseViewController {
    // MARK: - Properties

    var viewModel: MineViewModel
    private let pageType: MinePageType

    // MARK: - UI Components

    lazy var contentView: MinePublishContentView = {
        let contentView = MinePublishContentView()
        contentView.delegate = self
        return contentView
    }()

    // MARK: - 初始化

    init(pageType: MinePageType = .myPosts) {
        self.pageType = pageType
        self.viewModel = MineViewModel(pageType: pageType)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        loadData()
    }

    // MARK: - Private Methods

    override func configUI() {
        view.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 设置页面类型
        contentView.setPageType(pageType)
    }
    
    override func setupBindings() {
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: contentView.collectionView, with: viewModel,enableHeader: false)

        // 绑定推荐列表
        viewModel.$recommendList
            .receive(on: DispatchQueue.main)
            .sink { [weak self] models in
                self?.contentView.updateRecommendList(models)
            }
            .store(in: &cancellables)

        // 绑定草稿数据（仅在我发布的页面有效）
        viewModel.$draftModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] draft in
                self?.contentView.draftModel = draft
            }
            .store(in: &cancellables)

        // 绑定删除草稿结果
        viewModel.deleteDraftResultPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    // 删除成功，显示提示
                    print("草稿删除成功")
                } else {
                    // 删除失败，显示错误提示
                    print("草稿删除失败")
                }
            }
            .store(in: &cancellables)

        // 绑定点赞结果
        viewModel.likeResultPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    print("点赞操作成功")
                    // 可以在这里添加成功提示，比如轻微的震动反馈
                    self?.provideLikeHapticFeedback()
                } else {
                    print("点赞操作失败")
                    // 可以在这里显示错误提示
                }
            }
            .store(in: &cancellables)
    }
    
    private func loadData() {
        // 加载数据
        viewModel.fetchData(refresh: true)
    }

    /// 提供点赞操作的触觉反馈
    private func provideLikeHapticFeedback() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
}

// MARK: - MineContentViewDelegate

extension MinePublishController: MinePublishContentViewDelegate {
    func draftDidTapDelete(_ model: RecommendModel) {
        // 删除草稿
        viewModel.deleteDraft(draftId: model.id)
    }
    
    func pushToRecommendDetail(item: RecommendModel) {
     
        let vc = HomeDetailController(postId: item.id, pageType: item.type)
        pushVcHiddenTabBar(vc, animated: true)
    }

    func likeItem(item: RecommendModel) {
        // 调用ViewModel的点赞方法
        viewModel.toggleLike(type: item.type, postId: item.id)
    }

    func refreshData() {
        // 下拉刷新
        viewModel.fetchData(refresh: true)
    }

    func loadMoreData() {
        // 上拉加载更多
        viewModel.fetchData(refresh: false)
    }
}
// MARK: - JXPagingViewListViewDelegate

extension MinePublishController: JXPagingViewListViewDelegate {
    func listScrollView() -> UIScrollView {
        return contentView.collectionView
    }

    func listViewDidScrollCallback(callback: @escaping (UIScrollView) -> ()) {
        contentView.listViewDidScrollCallback = callback
    }
}
