import UIKit
import JXSegmentedView
import Combine

extension JXPagingListContainerView: JXSegmentedViewListContainer {}

class MineViewController: BaseViewController {
    lazy var TableHeaderViewHeight: Int = Int(ScreenInfo.statusBarHeight + 352)
    lazy var FeightForHeaderInSection: Int = 40

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if LoginManager.shared.isLogin(){
            mineTableHeaderView.updateUserInfo()
        }else{
            LoginManager.shared.checkLoginStatus()
        }
    }
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
    }
    
    override func configUI(){
        fd_prefersNavigationBarHidden = true
        view.addSubview(pagingView)
        view.addSubview(navView)
        segmentedView.listContainer = pagingView.listContainerView
        pagingView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }
        navView.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalNavBarHeight)
        }
    }
    override func setupBindings() {
        setupRefresh(for: pagingView.mainTableView, enableHeader: true, enableFooter: false, onHeaderRefresh:  { [weak self] in
            self?.refreshCurrentPage()
        })
        // 监听用户关注数和粉丝数更新通知
        NotificationCenter.default.publisher(for: AppNotifications.userFollowCountUpdated)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                // 更新用户信息显示
                self?.mineTableHeaderView.updateUserInfo()
            }
            .store(in: &cancellables)
    }
    // MARK: - 刷新相关方法

    /// 刷新当前显示的页面
    private func refreshCurrentPage() {
        // 获取当前选中的页面索引
        let currentIndex = segmentedView.selectedIndex

        // 确保索引有效
        guard currentIndex < vcs.count else {
            endRefreshing(for: pagingView.mainTableView)
            return
        }

        // 获取当前显示的控制器
        let currentController = vcs[currentIndex]

        // 监听刷新状态，当刷新完成时结束刷新动画
        currentController.viewModel.$refreshState
            .receive(on: DispatchQueue.main)
            .first { state in
                // 只监听第一个完成状态
                switch state {
                case .refreshSuccess, .refreshFailure:
                    return true
                default:
                    return false
                }
            }
            .sink { [weak self] state in
                // 刷新完成（成功或失败），结束刷新动画
                self?.endRefreshing(for: self?.pagingView.mainTableView ?? UIScrollView())
            }
            .store(in: &cancellables)

        // 调用当前控制器的刷新方法
        currentController.viewModel.refreshData()
    }
    
    lazy var vcs:[MinePublishController] = {
        var array = [MinePublishController]()
        for (index, _) in titles.enumerated() {
            let pageType = MinePageType(rawValue: index) ?? .myPosts
            let controller = MinePublishController(pageType: pageType)
            array.append(controller)
        }
        return array
    }()
    
    lazy var mineTableHeaderView = MineTableHeaderView().then{
        $0.delegate = self
    }
    lazy var titles = ["我发布的","我的收藏","我的点赞","已下架","浏览历史"]

    lazy var segmentedViewDataSource : JXSegmentedTitleDataSource = {
        let dataSource = JXSegmentedTitleDataSource()
        dataSource.titles = titles
        dataSource.titleSelectedColor = UIColor(hexString: "2A72FF", transparency: 1)!
        dataSource.titleNormalColor = UIColor(hexString: "2B2C2F", transparency: 0.8)!
        dataSource.titleNormalFont = .systemFont(ofSize: 14, weight: .regular)
        dataSource.titleSelectedFont = .systemFont(ofSize: 14, weight: .regular)
        dataSource.isTitleColorGradientEnabled = true
        dataSource.isTitleZoomEnabled = false
        return dataSource
    }()
    lazy var segmentedView = JXSegmentedView().then{
        $0.backgroundColor = UIColor.white
        $0.contentEdgeInsetLeft = 12
        $0.contentEdgeInsetRight = 12
        $0.layer.cornerRadius = 16
        $0.layer.maskedCorners = [.layerMinXMinYCorner,.layerMaxXMinYCorner]
        $0.masksToBounds = true
        $0.isContentScrollViewClickTransitionAnimationEnabled = false
        let lineView = JXSegmentedIndicatorLineView()
        lineView.indicatorColor = UIColor(hexString: "2A72FF", transparency: 1)!
        lineView.indicatorWidth = 10
        lineView.indicatorHeight = 3
        $0.indicators = [lineView]
        $0.dataSource = self.segmentedViewDataSource
    }
    lazy var navView = MineNavView().then{
        $0.delegate = self
    }
    lazy var pagingView = JXPagingView(delegate: self).then{
        $0.mainTableView.backgroundColor = UIColor(hexString: "#3D4962",transparency: 0.63)
    }
}

extension MineViewController: JXPagingViewDelegate {
    
    func tableHeaderViewHeight(in pagingView: JXPagingView) -> Int {
        return TableHeaderViewHeight
    }
    
    func tableHeaderView(in pagingView: JXPagingView) -> UIView {
        return mineTableHeaderView
    }
    
    func heightForPinSectionHeader(in pagingView: JXPagingView) -> Int {
        return FeightForHeaderInSection
    }
    
    func viewForPinSectionHeader(in pagingView: JXPagingView) -> UIView {
        return segmentedView
    }
    
    func numberOfLists(in pagingView: JXPagingView) -> Int {
        return titles.count
    }
    
    func pagingView(_ pagingView: JXPagingView, initListAtIndex index: Int) -> JXPagingViewListViewDelegate {
        return vcs[index]
    }
    
    func mainTableViewDidScroll(_ scrollView: UIScrollView) {
        let transparency = scrollView.contentOffset.y/(CGFloat(TableHeaderViewHeight) - ScreenInfo.totalNavBarHeight)
        navView.backgroundColor = UIColor(hexString: "#8399C4", transparency: transparency)
    }
}
extension MineViewController:MineNavViewDelegate{
    func mineNavViewDidTapMenuButton(_ navView: MineNavView) {
        showSlideMenu()
    }
    
    func mineNavViewDidTapShareButton(_ navView: MineNavView) {
        
    }
    
    func mineNavViewDidTapSettingButton(_ navView: MineNavView) {
        let vc = MineSettingController()
        pushVcHiddenTabBar(vc, animated: true)
    }
    
}
extension MineViewController:MineTableHeaderViewDelegate{
    func mineTableHeaderViewDidTapEditProfile(_ headerView: MineTableHeaderView) {
        let vc = MineEditProfileController()
        pushVcHiddenTabBar(vc, animated: true)
    }
    
    func mineTableHeaderViewDidTapInviteFriends(_ headerView: MineTableHeaderView) {
        
    }
    
    func mineTableHeaderViewDidTapViewDetails(_ headerView: MineTableHeaderView) {
        
    }
    
    func mineTableHeaderViewDidTapTransaction(_ headerView: MineTableHeaderView, type: MineTableHeaderView.TransactionType) {
        
        switch type {
        case .wallet:
            let vc = WalletController()
            pushVcHiddenTabBar(vc, animated: true)
        case .cart:
            let vc = ProductCartController()
            pushVcHiddenTabBar(vc, animated: true)
        case .selling:
            let vc = ProductSellingPurchasedController()
            vc.isSelling = true
            pushVcHiddenTabBar(vc, animated: true)
        case .purchased:
            let vc = ProductSellingPurchasedController()
            vc.isSelling = false
            pushVcHiddenTabBar(vc, animated: true)
        }
    }
    
    func mineTableHeaderViewDidTapFollowing(_ headerView: MineTableHeaderView) {
        let vc = MineFollowingController()
        vc.defaultSelectedIndex = 1
        pushVcHiddenTabBar(vc, animated: true)
    }

    func mineTableHeaderViewDidTapFollowers(_ headerView: MineTableHeaderView) {
        let vc = MineFollowingController()
        vc.defaultSelectedIndex = 0
        pushVcHiddenTabBar(vc, animated: true)
    }
}

