//
//  MineaAddFriendController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/19.
//

class MineaAddFriendController: BaseViewController {
    private lazy var tableView = UITableView(frame: .zero, style:.grouped).then{
        $0.separatorStyle = .none
        $0.showsVerticalScrollIndicator = false
        $0.register(MineFollowTableCell.self, forCellReuseIdentifier: "MineFollowTableCell")
        $0.delegate = self
        $0.dataSource = self
        $0.estimatedRowHeight = 350
        $0.rowHeight = UITableView.automaticDimension
        $0.sectionHeaderHeight = UITableView.automaticDimension
        $0.estimatedSectionHeaderHeight = 250
        $0.tableHeaderView = UIView(frame: CGRect(x: 0, y: 0, width: 0, height: 0.01))
    }
    lazy var tableHeaderView = MineFollowTableHeaderView()
    lazy var noFollowTableHeaderView = MineNoFollowTableHeaderView()
    lazy var viewModel = MineAddFriendViewModel()
    var isNoFollow:Bool?

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configLayout()
        setupBindings()
    }
    override func setupBindings() {
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: tableView, with: viewModel,enableHeader: false)

        // 监听评论列表变化
        viewModel.$recommendUserList
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)
        // 监听关注操作结果
        viewModel.followResultPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    // 关注操作成功，刷新数据
                    self?.viewModel.refreshData()
                }
            }
            .store(in: &cancellables)
    }
    override func configUI() {
        title = "添加好友"
        view.addSubview(tableView)

    }
    override func configLayout() {
        tableView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
        }
    }
    
}
extension MineaAddFriendController:UITableViewDelegate,UITableViewDataSource{
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.recommendUserList.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "MineFollowTableCell", for: indexPath) as! MineFollowTableCell
        cell.closeButton.isHidden = false
        cell.userModel = viewModel.recommendUserList[indexPath.row]
        // 设置关注按钮回调
        cell.followButtonTapped = { [weak self] userModel in
            guard let self = self, let userModel = userModel else { return }
            self.viewModel.addFollow(userId: userModel.id)
        }
        return cell
    }
    // 设置分区头视图
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        guard let _ = isNoFollow else {
            return tableHeaderView
        }
        return noFollowTableHeaderView
    }
}
