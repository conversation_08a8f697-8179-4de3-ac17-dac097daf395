//
//  MineRequestModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import Foundation

// MARK: - 我发布的帖子请求参数

/// 我发布的帖子请求参数
struct MyPostsRequest: RequestParametersConvertible {
    /// 页码，默认1
    var page: Int
    
    /// 每页条数，默认10
    var limit: Int
    
    init(page: Int = 1, limit: Int = 10) {
        self.page = page
        self.limit = limit
    }
    
    func asParameters() -> [String: Any] {
        return [
            "page": page,
            "limit": limit
        ]
    }
}

// MARK: - 收藏帖子请求参数

/// 收藏帖子请求参数
struct FavoritePostsRequest: RequestParametersConvertible {
    /// 页码，默认1
    var page: Int
    
    /// 每页数量，默认10
    var limit: Int
    
    /// 帖子类型编号（可选）
    var type: Int?
    
    init(page: Int = 1, limit: Int = 10, type: Int? = nil) {
        self.page = page
        self.limit = limit
        self.type = type
    }
    
    func asParameters() -> [String: Any] {
        var params: [String: Any] = [
            "page": page,
            "limit": limit
        ]
        
        if let type = type {
            params["type"] = type
        }
        
        return params
    }
}

// MARK: - 点赞帖子请求参数

/// 点赞帖子请求参数
struct LikedPostsRequest: RequestParametersConvertible {
    /// 页码，默认1
    var page: Int
    
    /// 每页数量，默认10
    var limit: Int
    
    /// 帖子类型编号（可选）
    var type: Int?
    
    init(page: Int = 1, limit: Int = 10, type: Int? = nil) {
        self.page = page
        self.limit = limit
        self.type = type
    }
    
    func asParameters() -> [String: Any] {
        var params: [String: Any] = [
            "page": page,
            "limit": limit
        ]
        
        if let type = type {
            params["type"] = type
        }
        
        return params
    }
}

// MARK: - 已下架帖子请求参数

/// 已下架帖子请求参数
struct OfflinePostsRequest: RequestParametersConvertible {
    /// 页码，默认1
    var page: Int
    
    /// 每页数量，默认10
    var limit: Int
    
    init(page: Int = 1, limit: Int = 10) {
        self.page = page
        self.limit = limit
    }
    
    func asParameters() -> [String: Any] {
        return [
            "page": page,
            "limit": limit
        ]
    }
}

// MARK: - 浏览历史请求参数

/// 浏览历史请求参数
struct BrowseHistoryRequest: RequestParametersConvertible {
    /// 页码，默认1
    var page: Int
    
    /// 每页数量，默认10，最大50
    var limit: Int
    
    init(page: Int = 1, limit: Int = 10) {
        self.page = page
        self.limit = min(limit, 50) // 限制最大50
    }
    
    func asParameters() -> [String: Any] {
        return [
            "page": page,
            "limit": limit
        ]
    }
}

// MARK: - 删除草稿请求参数

/// 删除草稿请求参数
struct DeleteDraftRequest: RequestParametersConvertible {
    /// 草稿ID
    var draftId: String
    
    init(draftId: String) {
        self.draftId = draftId
    }
    
    func asParameters() -> [String: Any] {
        return [
            "draft_id": draftId
        ]
    }
}

// MARK: - 便捷扩展

extension MyPostsRequest {
    /// 创建第一页请求
    static func firstPage(limit: Int = 10) -> MyPostsRequest {
        return MyPostsRequest(page: 1, limit: limit)
    }
    
    /// 创建指定页码请求
    static func page(_ page: Int, limit: Int = 10) -> MyPostsRequest {
        return MyPostsRequest(page: page, limit: limit)
    }
}

extension FavoritePostsRequest {
    /// 创建第一页请求
    static func firstPage(limit: Int = 10, type: Int? = nil) -> FavoritePostsRequest {
        return FavoritePostsRequest(page: 1, limit: limit, type: type)
    }
    
    /// 创建指定页码请求
    static func page(_ page: Int, limit: Int = 10, type: Int? = nil) -> FavoritePostsRequest {
        return FavoritePostsRequest(page: page, limit: limit, type: type)
    }
    
    /// 创建指定类型的第一页请求
    static func firstPageWithType(_ type: Int, limit: Int = 10) -> FavoritePostsRequest {
        return FavoritePostsRequest(page: 1, limit: limit, type: type)
    }
}

extension LikedPostsRequest {
    /// 创建第一页请求
    static func firstPage(limit: Int = 10, type: Int? = nil) -> LikedPostsRequest {
        return LikedPostsRequest(page: 1, limit: limit, type: type)
    }
    
    /// 创建指定页码请求
    static func page(_ page: Int, limit: Int = 10, type: Int? = nil) -> LikedPostsRequest {
        return LikedPostsRequest(page: page, limit: limit, type: type)
    }
    
    /// 创建指定类型的第一页请求
    static func firstPageWithType(_ type: Int, limit: Int = 10) -> LikedPostsRequest {
        return LikedPostsRequest(page: 1, limit: limit, type: type)
    }
}

extension OfflinePostsRequest {
    /// 创建第一页请求
    static func firstPage(limit: Int = 10) -> OfflinePostsRequest {
        return OfflinePostsRequest(page: 1, limit: limit)
    }
    
    /// 创建指定页码请求
    static func page(_ page: Int, limit: Int = 10) -> OfflinePostsRequest {
        return OfflinePostsRequest(page: page, limit: limit)
    }
}

extension BrowseHistoryRequest {
    /// 创建第一页请求
    static func firstPage(limit: Int = 10) -> BrowseHistoryRequest {
        return BrowseHistoryRequest(page: 1, limit: limit)
    }
    
    /// 创建指定页码请求
    static func page(_ page: Int, limit: Int = 10) -> BrowseHistoryRequest {
        return BrowseHistoryRequest(page: page, limit: limit)
    }
}
