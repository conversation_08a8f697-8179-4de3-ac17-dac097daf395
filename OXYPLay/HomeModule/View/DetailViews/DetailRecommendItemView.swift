//
//  DetailRecommendItemView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/10.
//

import UIKit

// MARK: - 推荐商品项视图

class DetailRecommendItemView: UIView {
    // MARK: - UI组件
    
    private lazy var containerView = UIView()
    
    private lazy var imageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.clipsToBounds = true
        $0.layer.cornerRadius = 4
        $0.backgroundColor = .random
    }
    
    private lazy var nameLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        $0.textColor = color_3D3E40
        $0.numberOfLines = 2
    }
    private lazy var priceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 18, weight: .regular)
        $0.textColor = color_2B2C2F
    }
    private lazy var likeLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.textColor = color_2B2C2F64
    }
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        containerView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(128) // 固定高度
        }
        
        containerView.addSubview(nameLabel)
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(8)
            make.left.right.equalToSuperview()
           
        }
        
        containerView.addSubview(priceLabel)
        priceLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(8)
            make.bottom.equalToSuperview()
            make.left.equalToSuperview()
            make.right.lessThanOrEqualToSuperview()
        }
        containerView.addSubview(likeLabel)
        likeLabel.snp.makeConstraints { make in
            make.centerY.equalTo(priceLabel)
            make.right.equalTo(0)
        }
    }
    
    // MARK: - 配置方法

    func configure(with sku: AdDetailSkuItem) {
        nameLabel.text = sku.name
        priceLabel.text = "¥\(sku.price)"
        likeLabel.text = sku.view
        if let imageUrl = URL(string: sku.cover_image) {
            imageView.kf.setImage(with: imageUrl, placeholder: UIImage(named: "placeholder"))
        }
    }

    /// 配置商品推荐项数据
    /// - Parameter item: 商品推荐项
    func configure(with item: ProductRecommendItem) {
        nameLabel.text = item.name
        priceLabel.text = item.formattedPrice
        likeLabel.text = item.formattedView
        if let imageUrl = URL(string: item.cover_image) {
            imageView.kf.setImage(with: imageUrl, placeholder: UIImage(named: "placeholder"))
        }
    }
}

