//
//  DetailInfoTableCell.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/6/2.
//

import UIKit
import SnapKit
import Then
/// 列表代理
protocol DetailInfoTableCellDelegate: AnyObject {
    // 列表项点击事件
    func buyButtonClick()
    
}
class DetailInfoTableCell: UITableViewCell {
    weak var delegate:DetailInfoTableCellDelegate?
    var cancellables = Set<AnyCancellable>()

    // MARK: - UI组件
    
    private lazy var containerView = UIView().then {
        $0.backgroundColor = .white
    }
    
    private lazy var titleView = DetailInfoTitleView()
    
    private lazy var timeLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.textColor = UIColor(hexString: "3D3E40", transparency: 0.48)
    }
    
    private lazy var descriptionLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = color_3D3E40
        $0.numberOfLines = 0
    }
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        containerView.addSubview(titleView)
        titleView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.right.equalToSuperview().inset(12)
        }
        
        containerView.addSubview(descriptionLabel)
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(titleView.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(12)
        }
        
        containerView.addSubview(timeLabel)
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.bottom.equalTo(-16)
        }
        titleView.buybutton.tapPublisher
            .sink { [weak self] _ in
            guard let self = self else { return }
                self.delegate?.buyButtonClick()
        }
        .store(in: &cancellables)
        
    }
    
    // MARK: - 配置方法
    
    func configure(with model: HomeDetailModel) {
        titleView.configure(with: model)
        timeLabel.text = "发布于：\(model.createdAt)"
        descriptionLabel.text = model.description
        
    }
} 
