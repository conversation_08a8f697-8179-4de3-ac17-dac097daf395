//
//  DetailRecommendTableCell.swift
//  OXYPLay
//
//  Created by <PERSON>h<PERSON> on 2025/6/2.
//

import UIKit
import SnapKit
import Then
import Kingfisher
protocol DetailRecommendTableCellDelegate: AnyObject {
    func moreButtonClick()
}

class DetailRecommendTableCell: UITableViewCell {
    weak var delegate: DetailRecommendTableCellDelegate?

    // MARK: - UI组件
    
    private lazy var containerView = UIView()
    private lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textColor = color_3D3E40
        $0.text = "推荐好物"
    }
    
    lazy var moreButton = BaseButton().then {
        $0.setTitle("查看全部", for: .normal)
        $0.setTitleColor(color_999999, for: .normal)
        $0.imagePosition = .right
        $0.spacing = 4
        $0.setImage(UIImage(named: "home_product_detail_right_arrow"), for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12)
    }
    
    private lazy var recommendItemsView = RecommendItemsView()
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 16, left: 12, bottom: 16, right: 12))
        }
        
        containerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview()
        }
        
        containerView.addSubview(moreButton)
        moreButton.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalToSuperview()
        }
        
        containerView.addSubview(recommendItemsView)
        recommendItemsView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        moreButton.addTarget(self, action: #selector(moreButtonTapped), for: .touchUpInside)
    }
    
    // MARK: - 配置方法
    
    func configure(with adSkuList: [AdDetailSkuItem],hideMoreButton:Bool){
        recommendItemsView.configure(with: adSkuList)
        moreButton.isHidden = hideMoreButton
    }
    
    // MARK: - 事件处理
    
    @objc private func moreButtonTapped() {
        // 处理查看更多按钮点击事件
        self.delegate?.moreButtonClick()
    }
    
}

// MARK: - 推荐商品视图

class RecommendItemsView: UIView {
    
    // MARK: - 属性
    
    private var skuList: [AdDetailSkuItem] = []
    private var itemViews: [DetailRecommendItemView] = []
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 配置方法
    
    func configure(with skuList: [AdDetailSkuItem]) {
        // 清除旧的视图
        itemViews.forEach { $0.removeFromSuperview() }
        itemViews.removeAll()
        
        // 最多显示4个商品
        let limitedSkuList = Array(skuList.prefix(4))
        self.skuList = limitedSkuList
        
        // 创建商品视图
        for sku in limitedSkuList {
            let itemView = DetailRecommendItemView()
            itemView.configure(with: sku)
            addSubview(itemView)
            itemViews.append(itemView)
        }
        if itemViews.count == 1 {
            itemViews.first?.snp.makeConstraints { make in
                make.left.top.bottom.equalToSuperview()
                make.height.equalTo(196)
                make.width.equalTo((kScreenWidth-24-9)/2)
            }
        }else{
            itemViews.snp.distributeSudokuViews(fixedItemWidth: (kScreenWidth-24-9)/2, fixedItemHeight: 196, warpCount: 2)
        }
    }
}

