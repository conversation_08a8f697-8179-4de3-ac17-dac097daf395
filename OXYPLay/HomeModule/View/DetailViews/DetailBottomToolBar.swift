import UIKit
import Then
import SnapKit
import Combine
import CombineCocoa

protocol DetailBottomToolBarDelegate: AnyObject {
    func toolBar(_ toolBar: DetailBottomToolBar, didClickComment text: String)
    func toolBar(_ toolBar: DetailBottomToolBar, didClickLike isLiked: Bool)
    func toolBar(_ toolBar: DetailBottomToolBar, didClickFavorite isFavorited: Bool)
    func toolBar(_ toolBar: DetailBottomToolBar, didClickShare sender: UIButton)
}

class DetailBottomToolBar: BaseView {
    
    // MARK: - 属性
    
    weak var delegate: DetailBottomToolBarDelegate?
    
    private var isLiked: Bool = false {
        didSet {
            likeButton.isSelected = isLiked
            updateLikeButtonAppearance()
        }
    }
    
    private var isFavorited: Bool = false {
        didSet {
            favoriteButton.isSelected = isFavorited
            updateFavoriteButtonAppearance()
        }
    }
    
    private var likeCount: Int = 0 {
        didSet {
            likeButton.setTitle("\(likeCount)", for: .normal)
            likeButton.setTitle("\(likeCount)", for: .selected)
        }
    }
    
    private var favoriteCount: Int = 0 {
        didSet {
            favoriteButton.setTitle("\(favoriteCount)", for: .normal)
            favoriteButton.setTitle("\(favoriteCount)", for: .selected)
        }
    }
    
    private var commentCount: Int = 0 {
        didSet {
            commentButton.setTitle("\(commentCount)", for: .normal)
        }
    }
    
    // MARK: - UI组件
    
    lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.shadowColor = UIColor(hexString: "6F6F77", transparency: 0.16)?.cgColor
        $0.layer.shadowOffset = CGSize(width: 0, height: -2)
        $0.layer.shadowRadius = 16
        $0.layer.shadowOpacity = 1
    }
    
    lazy var commentTextField = UITextField().then {
        $0.placeholder = "写评论..."
        $0.font = UIFont.systemFont(ofSize: 14)
        $0.backgroundColor = color_F3F6F7
        $0.layer.cornerRadius = 17
        $0.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 10, height: 10))
        $0.leftViewMode = .always
        $0.returnKeyType = .send
    }
    
    lazy var likeButton = UIButton().then {
        $0.setImage(UIImage(named: "home_like_select")?.resize(to: CGSize(width: 17, height: 15)), for: .selected)
        $0.setImage(UIImage(named: "home_like")?.resize(to: CGSize(width: 17, height: 15)), for: .normal)
        $0.setTitle("0", for: .normal)
        $0.setTitle("0", for: .selected)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }
    
    lazy var favoriteButton = UIButton().then {
        $0.setImage(UIImage(named: "assembly_collection"), for: .normal)
        $0.setImage(UIImage(named: "assembly_collection_select"), for: .selected)
        $0.setTitle("0", for: .normal)
        $0.setTitle("0", for: .selected)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }
    
    lazy var commentButton = UIButton().then {
        $0.setImage(UIImage(named: "assembly_comment"), for: .normal)
        $0.setTitle("0", for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }
    
    
    // MARK: - UI设置
    
    override func configUI() {
        backgroundColor = .clear
        
        addSubview(containerView)
        
        containerView.addSubview(commentTextField)
        containerView.addSubview(likeButton)
        containerView.addSubview(favoriteButton)
        containerView.addSubview(commentButton)
        
        setupBindings()
    }
    
    override func configLayout() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        commentTextField.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalTo(12)
            make.height.equalTo(34)
        }
                
        commentButton.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalTo(commentTextField)
        }
        
        favoriteButton.snp.makeConstraints { make in
            make.right.equalTo(commentButton.snp.left).offset(-20)
            make.centerY.equalTo(commentTextField)
        }
        
        likeButton.snp.makeConstraints { make in
            make.right.equalTo(favoriteButton.snp.left).offset(-20)
            make.centerY.equalTo(commentTextField)
            make.left.equalTo(commentTextField.snp.right).offset(25)
        }
    }
    
    // MARK: - 配置数据
    
    func configure(with model: HomeDetailModel) {
        isLiked = model.is_liked
        isFavorited = model.is_favorited
        likeCount = model.likeCount
        favoriteCount = model.favoriteCount
        commentCount = model.commentCount
    }
    
    // MARK: - 事件绑定
    
    override func setupBindings() {
        // 点赞按钮点击事件
        likeButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.isLiked = !self.isLiked
                self.likeCount += self.isLiked ? 1 : -1
                self.delegate?.toolBar(self, didClickLike: self.isLiked)
            }
            .store(in: &cancellables)
        
        // 收藏按钮点击事件
        favoriteButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.isFavorited = !self.isFavorited
                self.favoriteCount += self.isFavorited ? 1 : -1
                self.delegate?.toolBar(self, didClickFavorite: self.isFavorited)
            }
            .store(in: &cancellables)
        
        // 评论按钮点击事件
        commentButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.commentTextField.becomeFirstResponder()
            }
            .store(in: &cancellables)
        
        // 评论文本框回车发送事件
        commentTextField.controlEventPublisher(for: .editingDidEndOnExit)
            .sink { [weak self] _ in
                guard let self = self, let text = self.commentTextField.text, !text.isEmpty else { return }
                self.delegate?.toolBar(self, didClickComment: text)
                self.commentTextField.text = ""
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 辅助方法
    
    private func updateLikeButtonAppearance() {
        if isLiked {
            likeButton.tintColor = .systemPink
        } else {
            likeButton.tintColor = .lightGray
        }
    }
    
    private func updateFavoriteButtonAppearance() {
        if isFavorited {
            favoriteButton.tintColor = .systemYellow
        } else {
            favoriteButton.tintColor = .lightGray
        }
    }
}

