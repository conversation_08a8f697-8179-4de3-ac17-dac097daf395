//
//  DetailInfoTitleView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/10.
//

class DetailInfoTitleView:BaseView{
    private lazy var stackView = UIStackView().then{
        $0.spacing = 8
        $0.axis = .vertical
    }
    private lazy var topView = UIView()
    private lazy var bottomView = UIView()
    private lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        $0.textColor = color_3D3E40
        $0.numberOfLines = 0
    }
    private lazy var stateLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.backgroundColor = UIColor(hexString: "FF0000", transparency: 0.08)
        $0.textColor = color_red
        $0.layer.cornerRadius = 5
        $0.textAlignment = .center
        $0.masksToBounds = true
    }
    
    private lazy var remainingLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.backgroundColor = UIColor(hexString: "000000", transparency: 0.08)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.64)
        $0.layer.cornerRadius = 5
        $0.textAlignment = .center
        $0.masksToBounds = true
    }
    private let currentPriceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = UIColor(hexString: "F12D24", transparency: 1)
    }
    
    private let originalPriceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.48)
    }
    let buybutton = BaseButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.setTitle("立即购买", for: .normal)
        $0.setBackgroundColor(UIColor(hexString: "387BFF", transparency: 1)!, for: .normal)
        $0.setTitleColor(UIColor(hexString: "FFFFFF", transparency: 1)!, for: .normal)
        $0.setTitle("已下架", for: .disabled)
        $0.setBackgroundColor(UIColor(hexString: "788092", transparency: 0.08)!, for: .disabled)
        $0.setTitleColor(UIColor(hexString: "#2B2C2F", transparency: 0.24)!, for: .disabled)
        $0.cornerRadius = 5
        $0.isRounded = true
    }
    func configure(with model: HomeDetailModel) {
        titleLabel.text = model.title
        stateLabel.text = model.postage_type_text
        remainingLabel.text = "数量\(model.quantity)"
        currentPriceLabel.text = "￥\(model.price)"
        originalPriceLabel.setStrikethroughText(model.origin_price.formattedPrice)
        remainingLabel.snp.updateConstraints { make in
            make.width.equalTo(remainingLabel.intrinsicContentSize.width + 12)
           
        }
        stateLabel.snp.updateConstraints { make in
            make.width.equalTo(stateLabel.intrinsicContentSize.width + 12)
           
        }
        bottomView.isHidden = !model.has_price
        stateLabel.isHidden = !model.has_price
        buybutton.isEnabled = model.status == "1"
    }

    override func configUI() {
        topView.addSubview(titleLabel)
        topView.addSubview(stateLabel)
        bottomView.addSubview(remainingLabel)
        bottomView.addSubview(currentPriceLabel)
        bottomView.addSubview(originalPriceLabel)
        bottomView.addSubview(buybutton)
        stackView.addArrangedSubview(topView)
        stackView.addArrangedSubview(bottomView)
        addSubview(stackView)
    }
    override func configLayout() {
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        titleLabel.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
        }
        stateLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview()
            make.width.equalTo(42)
            make.height.equalTo(19)
            make.left.greaterThanOrEqualTo(titleLabel.snp.right).offset(15)
        }
       
        currentPriceLabel.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.centerY.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        originalPriceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(currentPriceLabel)
            make.left.equalTo(currentPriceLabel.snp.right).offset(8)
        }
        remainingLabel.snp.makeConstraints { make in
            make.width.equalTo(56)
            make.height.equalTo(20)
            make.centerY.equalTo(originalPriceLabel)
            make.left.equalTo(originalPriceLabel.snp.right).offset(8)
        }
        buybutton.snp.makeConstraints { make in
            make.top.equalTo(7)
            make.bottom.equalTo(-7)
            make.right.equalTo(0)
            make.width.equalTo(68)
            make.height.equalTo(28)
        }
    }
}
