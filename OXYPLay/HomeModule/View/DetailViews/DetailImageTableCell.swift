//
//  DetailImageTableCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/2.
//

import UIKit
import SnapKit
import Then
import Kingfisher

class DetailImageTableCell: UITableViewCell {
    
    
    // MARK: - UI组件
    
    private lazy var imagesView = DetailProductImagesView()
    
  
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = color_F3F6F7
        
        contentView.addSubview(imagesView)
        imagesView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - 配置方法
    
    func configure(with model: HomeDetailModel) {
        imagesView.configure(with: model)
    }
}

