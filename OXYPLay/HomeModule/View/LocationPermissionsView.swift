//
//  LocationPermissionsView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/18.
//

class LocationPermissionsView:BaseView{
    private lazy var titleLabel = UILabel().then {
        $0.text = "没有开启定位权限"
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textColor = color_2B2C2F
    }
   
    private lazy var contentLabel = UILabel().then {
        $0.text = "或者手动选择城市"
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.64)
    }
   
    private lazy var button = BaseButton().then {
        $0.setTitle("开启定位", for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.setTitleColor(color_blue, for: .normal)
        $0.borderColor = color_blue
        $0.borderWidth = 1
        $0.isRounded = true
    }
   
    override func configUI() {
        self.layer.cornerRadius = 16
        self.backgroundColor = .white
        self.masksToBounds = true
        addSubview(titleLabel)
        addSubview(contentLabel)
        addSubview(button)
        button.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                
            }
            .store(in: &cancellables)
        
    }
    override func configLayout() {
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(12)
        }
        button.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(6)
            make.right.equalTo(self.snp.centerX)
            make.width.equalTo(72)
            make.height.equalTo(24)
        }
        contentLabel.snp.makeConstraints { make in
            make.left.equalTo(button.snp.right).offset(6)
            make.centerY.equalTo(button)
        }
    }
    
}
