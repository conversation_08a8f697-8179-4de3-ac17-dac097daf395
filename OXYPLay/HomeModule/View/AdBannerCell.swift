import UIKit

protocol AdBannerCellDelegate: AnyObject {
    func adBannerCell(_ cell: AdBannerCell, didSelectItemAt index: Int, item: ContentItem)
}

class AdBannerCell: UICollectionViewCell, CycleBannerViewDelegate {
    private var datas: [ContentItem] = []
    weak var delegate: AdBannerCellDelegate?

    // MARK: - 属性

    private lazy var bannerView: CycleBannerView = {
        let view = CycleBannerView()
        view.delegate = self
        view.pageControlBackgroundIsShow = true
        return view
    }()

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func layoutSubviews() {
        super.layoutSubviews()
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        bannerView.stopAutoScroll()
    }

    // MARK: - UI设置

    private func setupUI() {
        contentView.addSubview(bannerView)
        bannerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    // MARK: - 配置数据

    func configure(with data: [ContentItem]) {
        datas = data
        bannerView.configure(with: data)
    }

    // MARK: - CycleBannerViewDelegate

    func cycleBannerView(_: CycleBannerView, didSelectItemAt index: Int, item: ContentItem) {
        delegate?.adBannerCell(self, didSelectItemAt: index, item: item)
    }
}
