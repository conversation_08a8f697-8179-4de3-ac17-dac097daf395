//
//  RecommendView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/2.
//

import JXSegmentedView
import MJRefresh
import UIKit

protocol RecommendViewDelegate: AnyObject {
    func pushToRecommendDetail(item:RecommendModel)
    func pushToAdDetail(item: ContentItem)
    func likeItem(item: RecommendModel)
    /// 下拉刷新时触发
    func refreshRecommendData()
    
    /// 上拉加载更多时触发
    func loadMoreRecommendData()
}

/// 推荐视图区域类型
enum RecommendSection {
    case banner
    case activity
    case waterfall
}

class RecommendView: BaseView {
    var listViewDidScrollCallback: ((UIScrollView) -> ())?
    weak var delegate: RecommendViewDelegate?
    var adModel: HomeAdlistModel? {
        didSet {
            if let adlist = adModel?.adList {
                isShowBanner = adlist.count > 0
            }
            if let offList = adModel?.offList.banner {
                isShowActivity = offList.count > 0
            }
            collectionView.reloadData()
        }
    }
    
    // 推荐数据列表
    var recommendList: [RecommendModel] = [] {
        didSet {
            // 不再在这里清除高度缓存，而是在需要时重新计算
            collectionView.reloadData()
        }
    }
    
    // MARK: - 控制显示隐藏的属性

    /// 是否显示广告
    var isShowBanner: Bool = false

    /// 是否显示热门活动
    var isShowActivity: Bool = false
    
    /// 是否还有更多数据
    var hasMoreData: Bool = true

    // MARK: - 懒加载控件

    lazy var collectionView: UICollectionView = {
        let layout = WaterfallMutiSectionFlowLayout()
        layout.delegate = self
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .systemBackground
        collectionView.register(AdBannerCell.self, forCellWithReuseIdentifier: "banner")
        collectionView.register(HotActivityCell.self, forCellWithReuseIdentifier: "activity")
        collectionView.register(RecommendCollectionCell.self, forCellWithReuseIdentifier: "waterfall")
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.backgroundColor = color_F6F8F9
        return collectionView
    }()
    deinit {
        listViewDidScrollCallback = nil
    }
    override func configUI() {
        addSubview(collectionView)
       
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    /// 更新推荐列表数据
    func updateRecommendList(_ models: [RecommendModel]) {
        self.recommendList = models
        collectionView.reloadData()
    }
    
    /// 添加更多推荐列表数据
    func appendRecommendList(_ models: [RecommendModel]) {
        self.recommendList.append(contentsOf: models)
        // 不需要清除缓存，新数据会在需要时计算高度
        collectionView.reloadData()
    }
    
    /// 清除高度缓存
    func clearHeightCache() {
        // 清除模型中的高度缓存
        for i in 0..<recommendList.count {
            recommendList[i].cellHeight = 0
        }
    }
    
    /// 刷新数据并清除缓存
    func reloadDataAndClearCache() {
        clearHeightCache()
        collectionView.reloadData()
    }
}

// MARK: - 集合视图数据源和代理

extension RecommendView: UICollectionViewDataSource, UICollectionViewDelegate, WaterfallMutiSectionDelegate {
    func numberOfSections(in _: UICollectionView) -> Int {
        var count = 0
        if isShowBanner {
            count += 1
        }
        if isShowActivity {
            count += 1
        }
        // 瀑布流内容区域始终显示
        count += 1
        return count
    }

    // 根据当前显示状态获取实际的Section类型
    private func getSectionType(for index: Int) -> RecommendSection? {
        var currentIndex = 0

        if isShowBanner {
            if index == currentIndex {
                return .banner
            }
            currentIndex += 1
        }

        if isShowActivity {
            if index == currentIndex {
                return .activity
            }
            currentIndex += 1
        }

        if index == currentIndex {
            return .waterfall
        }

        return nil
    }

    func collectionView(_: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        guard let sectionType = getSectionType(for: section) else { return 0 }
        switch sectionType {
        case .banner: return 1
        case .activity: return 1
        case .waterfall: return recommendList.count
        }
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let sectionType = getSectionType(for: indexPath.section) else { return UICollectionViewCell() }

        switch sectionType {
        case .banner:
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "banner", for: indexPath) as! AdBannerCell
            if let list = adModel?.adList {
                cell.configure(with: list)
                cell.delegate = self
            }
            return cell

        case .activity:
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "activity", for: indexPath) as! HotActivityCell
            if let offList = adModel?.offList {
                cell.configure(with: offList)
                cell.delegate = self
            }
            return cell

        case .waterfall:
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "waterfall", for: indexPath) as! RecommendCollectionCell
            cell.delegate = self
            if indexPath.item < recommendList.count {
                cell.configure(with: recommendList[indexPath.item])
            }
            return cell
        }
    }

    func heightForRowAtIndexPath(collectionView: UICollectionView, layout _: WaterfallMutiSectionFlowLayout, indexPath: IndexPath, itemWidth: CGFloat) -> CGFloat {
        guard let sectionType = getSectionType(for: indexPath.section) else { return 0 }
        switch sectionType {
        case .banner: return 150
        case .activity: return 191
        case .waterfall:
            if indexPath.item < recommendList.count {
                let model = recommendList[indexPath.item]
                
                // 先检查模型本身是否已缓存高度
                if model.cellHeight > 0 {
                    return model.cellHeight
                }
                
                let height = RecommendCollectionCell.calculateHeight(for: model, width: itemWidth)
                
               
                recommendList[indexPath.item].cellHeight = height
                
                return height
            }
            return 300
        }
    }

    func columnNumber(collectionView _: UICollectionView, layout _: WaterfallMutiSectionFlowLayout, section: Int) -> Int {
        guard let sectionType = getSectionType(for: section) else { return 0 }
        switch sectionType {
        case .banner: return 1
        case .activity: return 1
        case .waterfall: return 2
        }
    }

    // 每个section 边距（默认为0）
    func insetForSection(collectionView _: UICollectionView, layout _: WaterfallMutiSectionFlowLayout, section _: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 12, left: 12, bottom: 0, right: 12)
    }

    /// 每个section item上下间距（默认为0）
    func lineSpacing(collectionView _: UICollectionView, layout _: WaterfallMutiSectionFlowLayout, section _: Int) -> CGFloat {
        return 8.5
    }

    /// 每个section item左右间距（默认为0）
    func interitemSpacing(collectionView _: UICollectionView, layout _: WaterfallMutiSectionFlowLayout, section _: Int) -> CGFloat {
        return 9.5
    }

    func collectionView(_: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard let sectionType = getSectionType(for: indexPath.section) else { return }
        
        switch sectionType {
        case .waterfall:
            if indexPath.item < recommendList.count {
                let model = recommendList[indexPath.item]
                delegate?.pushToRecommendDetail(item:model)
            }
        default:
            break
        }
    }
    public func scrollViewDidScroll(_ scrollView: UIScrollView) {
        self.listViewDidScrollCallback?(scrollView)
    }
}

// MARK: - AdBannerCellDelegate

extension RecommendView: AdBannerCellDelegate {
    func adBannerCell(_: AdBannerCell, didSelectItemAt _: Int, item: ContentItem) {
        delegate?.pushToAdDetail(item: item)
    }
}

// MARK: - HotActivityCellDelegate

extension RecommendView: HotActivityCellDelegate {
    func hotActivityCell(_: HotActivityCell, didSelectBannerAt _: Int, item: ContentItem) {
        delegate?.pushToAdDetail(item: item)
    }
    
    func hotActivityCell(_: HotActivityCell, didSelectProductAt _: Int, item: ProductItem) {
        delegate?.pushToAdDetail(item: item)
    }
}

extension RecommendView:RecommendCollectionCellDelegate{
    func recommendCollectionCellDidTapLike(_ model: RecommendModel) {
        delegate?.likeItem(item: model)
    }
    
    
}
