import UIKit

protocol HotActivityCellDelegate: AnyObject {
    func hotActivityCell(_ cell: HotActivityCell, didSelectBannerAt index: Int, item: ContentItem)
    func hotActivityCell(_ cell: HotActivityCell, didSelectProductAt index: Int, item: ProductItem)
}

class HotActivityCell: UICollectionViewCell, CycleBannerViewDelegate {
    // MARK: - 属性

    weak var delegate: HotActivityCellDelegate?
    private var offListData: [ContentItem] = []
    private var productData: [ProductItem] = []

    // MARK: - 控件属性

    private let titleImageView = UIImageView().then {
        $0.image = UIImage(named: "home_hottitle")
    }
    
    private let titlebottomImageView = UIImageView().then {
        $0.image = UIImage(named: "home_recommendtitle")
    }
    private let updateImageView = UIImageView().then {
        $0.image = UIImage(named: "home_update")
    }
    
    private let updateLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.textColor = color_3D3E40
        $0.numberOfLines = 2
    }

    private lazy var productBannerView = CycleBannerView().then {
        $0.delegate = self
        $0.cornerRadius = 8
        $0.showPageControl = true
        $0.pageControlBottomOffset = 30
    }

    private lazy var bannerView = CycleBannerView().then {
        $0.delegate = self
        $0.cornerRadius = 8
        $0.showPageControl = true
    }

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 布局生命周期

    override func layoutSubviews() {
        super.layoutSubviews()
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        bannerView.stopAutoScroll()
        productBannerView.stopAutoScroll()
    }

    // MARK: - UI设置

    private func setupUI() {
        contentView.addSubview(titleImageView)
        contentView.addSubview(titlebottomImageView)
        contentView.addSubview(updateImageView)
        contentView.addSubview(updateLabel)
        contentView.addSubview(productBannerView)
        contentView.addSubview(bannerView)
    }

    // MARK: - 约束设置

    private func setupConstraints() {
        titleImageView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalTo(0)
        }
        productBannerView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.width.equalTo(150)
            make.top.equalTo(titleImageView.snp.bottom).offset(12)

        }
        updateImageView.snp.makeConstraints { make in
            make.top.equalTo(bannerView.snp.bottom).offset(6)
            make.left.equalTo(bannerView)
            make.bottom.equalTo(productBannerView)
        }
        updateLabel.snp.makeConstraints { make in
            make.centerY.equalTo(updateImageView)
            make.right.equalTo(bannerView)
            make.left.equalTo(updateImageView.snp.right).offset(4)
        }

        bannerView.snp.makeConstraints { make in
            make.top.equalTo(productBannerView)
            make.right.equalToSuperview().offset(0)
            make.height.equalTo(80)
            make.left.equalTo(productBannerView.snp.right).offset(10)
        }
        
        titlebottomImageView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalTo(productBannerView.snp.bottom).offset(24.5)
            make.bottom.equalToSuperview()
        }
    }

    // MARK: - 数据配置

    func configure(with offList: OffListContent) {
        // 配置banner区域
        offListData = offList.banner
        bannerView.configure(with: offListData)
        
        // 配置产品区域
        productData = offList.product
        productBannerView.configure(with: productData)
        
        // 配置更新提示
        updateLabel.text = offList.update.first?.description
    }

    // MARK: - CycleBannerViewDelegate

    func cycleBannerView(_: CycleBannerView, didSelectItemAt index: Int, item: ContentItem) {
        delegate?.hotActivityCell(self, didSelectBannerAt: index, item: item)
    }
       
}
