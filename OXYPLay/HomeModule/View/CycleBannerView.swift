import Kingfisher
import SnapKit
import UIKit

protocol CycleBannerViewDelegate: AnyObject {
    func cycleBannerView(_ bannerView: CycleBannerView, didSelectItemAt index: Int, item: ContentItem)
}

class CycleBannerView: BaseView, InfiniteBannerViewDataSource, InfiniteBannerViewDelegate {
    private var datas: [ContentItem] = []
    weak var delegate: CycleBannerViewDelegate?

    // 是否显示页面指示器
    var showPageControl: Bool = true {
        didSet {
            pageControl.isHidden = !showPageControl
        }
    }

    // 页面指示器位置偏移
    var pageControlBottomOffset: CGFloat = -4 {
        didSet {
            updatePageControlConstraints()
        }
    }

    // 圆角大小
    var cornerRadius: CGFloat = 8 {
        didSet {
            layer.cornerRadius = cornerRadius
            clipsToBounds = true
        }
    }
    
    // 页面指示器颜色
    var pageIndicatorTintColor: UIColor = .white {
        didSet {
            pageControl.pageIndicatorTintColor = pageIndicatorTintColor
        }
    }
    
    // 当前页面指示器颜色
    var currentPageIndicatorTintColor: UIColor = color_blue {
        didSet {
            pageControl.currentPageIndicatorTintColor = currentPageIndicatorTintColor
        }
    }
    
    // 页面指示器是否显示背景
    var pageControlBackgroundIsShow: Bool? {
        didSet {
            guard let pageControlBackgroundIsShow = pageControlBackgroundIsShow else {
                pageControl.containerBackgroundColor = .clear
                return
            }
            pageControl.containerBackgroundColor = pageControlBackgroundIsShow ? UIColor.black.withAlphaComponent(0.16):.clear

        }
    }

    // MARK: - 属性

    private(set) lazy var pagerView = InfiniteBannerView().then {
        $0.isInfiniteLoop = true
        $0.autoScrollInterval = 3.0
        $0.dataSource = self
        $0.delegate = self
    }

    private lazy var pageControl = CustomPageControl().then {
        $0.pageIndicatorSize = CGSize(width: 4, height: 4)
        $0.pageIndicatorTintColor = .white
        $0.currentPageIndicatorTintColor = color_blue
    }

    // MARK: - 配置UI

    override public func configUI() {
        layer.cornerRadius = cornerRadius
        clipsToBounds = true

        addSubview(pagerView)
        addSubview(pageControl)

        // 注册Cell
        pagerView.register(BannerItemCell.self, forCellWithReuseIdentifier: "BannerItemCell")
    }

    override public func configLayout() {
        pagerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        updatePageControlConstraints()
    }

    private func updatePageControlConstraints() {
        pageControl.snp.remakeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(pageControlBottomOffset)
            make.height.equalTo(20)
        }
    }

    // MARK: - 配置数据

    func configure(with data: [ContentItem]) {
        datas = data
        pageControl.numberOfPages = datas.count
        pagerView.reloadData()

        // 如果只有一个Banner，不显示分页控件
        pageControl.isHidden = datas.count <= 1 || !showPageControl

        // 如果有多个Banner，启动自动滚动
        if datas.count > 1 {
            startAutoScroll()
        } else {
            stopAutoScroll()
        }
    }

    // MARK: - 自动滚动控制

    func startAutoScroll() {
        pagerView.autoScrollInterval = 3.0
    }

    func stopAutoScroll() {
        pagerView.autoScrollInterval = 0
    }

    // MARK: - InfiniteBannerViewDataSource

    func numberOfItems(in bannerView: InfiniteBannerView) -> Int {
        return datas.count
    }

    func bannerView(_ bannerView: InfiniteBannerView, cellForItemAt index: Int) -> UICollectionViewCell {
        let cell = bannerView.dequeueReusableCell(withReuseIdentifier: "BannerItemCell", for: index) as! BannerItemCell
        let item = datas[index]
        cell.configure(with: item)
        return cell
    }

    // MARK: - InfiniteBannerViewDelegate

    func bannerView(_ bannerView: InfiniteBannerView, didScrollFrom fromIndex: Int, to toIndex: Int) {
        pageControl.currentPage = toIndex
    }

    func bannerView(_ bannerView: InfiniteBannerView, didSelectItemAt index: Int) {
        if index < datas.count {
            let item = datas[index]
            delegate?.cycleBannerView(self, didSelectItemAt: index, item: item)
        }
    }
}

// MARK: - BannerItemCell

class BannerItemCell: UICollectionViewCell {
    let imageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.clipsToBounds = true
    }

    let titleLabel = UILabel().then {
        $0.textColor = .white
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textAlignment = .center
    }
    let priceTagView = PriceTagView().then {
        $0.isHidden = true
    }
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        contentView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(40)
        }
        contentView.addSubview(priceTagView)
        priceTagView.snp.makeConstraints { make in
            make.left.equalTo(4)
            make.bottom.equalTo(-4)
            make.height.equalTo(24)
        }
    }

    func configure(with item: ContentItem) {
        // 处理图片URL
        if let imageUrl = URL(string: item.imageUrl) {
            imageView.kf.setImage(with: imageUrl, placeholder: UIImage(named: "placeholder_banner"))
        }
      
        // 添加价格标签
        if item.price.isEmpty == false {
            priceTagView.isHidden = false
            priceTagView.configure(currentPrice: item.price, originalPrice: item.origin_price)
        } else {
            priceTagView.isHidden = true
        }
    }
}


