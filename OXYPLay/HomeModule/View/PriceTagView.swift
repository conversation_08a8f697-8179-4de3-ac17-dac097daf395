import UIKit
import SnapKit

/// 价格标签视图 - 用于显示商品当前价格和原价
class PriceTagView: UIView {
    // MARK: - 属性
    
    private let gradientLayer = CAGradientLayer()
    
    // MARK: - 控件属性
    
    private let currentPriceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11, weight: .medium)
        $0.textColor = .white
    }
    
    private let originalPriceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 10, weight: .regular)
        $0.textColor = .white.withAlphaComponent(0.8)
    }
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期
    
    override func layoutSubviews() {
        super.layoutSubviews()
        gradientLayer.frame = bounds
        self.setCustomCorners(topLeft: 6, topRight: 10, bottomLeft: 10, bottomRight: 6)
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        // 设置渐变背景
        gradientLayer.colors = [UIColor(red: 1.0, green: 0.3, blue: 0.3, alpha: 1.0).cgColor, 
                               UIColor(red: 1.0, green: 0.1, blue: 0.1, alpha: 1.0).cgColor]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        layer.insertSublayer(gradientLayer, at: 0)
        
        addSubview(currentPriceLabel)
        addSubview(originalPriceLabel)
    }
    
    // MARK: - 约束设置
    
    private func setupConstraints() {
        currentPriceLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(10)
            make.centerY.equalToSuperview()
        }
        
        originalPriceLabel.snp.makeConstraints { make in
            make.left.equalTo(currentPriceLabel.snp.right).offset(5)
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-10)
        }
    }
    
    // MARK: - 数据配置
    
    /// 配置价格标签内容
    /// - Parameters:
    ///   - currentPrice: 当前价格
    ///   - originalPrice: 原价
    func configure(currentPrice: String, originalPrice: String) {
        currentPriceLabel.text = currentPrice.formattedPrice
        originalPriceLabel.setStrikethroughText(originalPrice.formattedPrice)
    }
} 
