//
//  HomeLocationHeaderView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/18.
//

/// 筛选视图代理协议
protocol HomeLocationHeaderViewDelegate: AnyObject {

    /// 地区选择事件
    /// - Parameters:
    ///   - filterView: 触发事件的筛选视图
    ///   - region: 选择的地区
    func filterView(_ filterView: BaseView, didSelectRegion region: RegionListItemModel)
   
}

/// 筛选视图组件
class HomeLocationHeaderView: BaseView {
    // MARK: - 属性

    /// 代理
    weak var delegate: HomeLocationHeaderViewDelegate?

    private var regions: [RegionListItemModel] = []
    private var selectedRegionIndex: Int = 0

    // MARK: - UI组件


    // 地区和场地区域
    private lazy var regionFilterView = FilterScrollView(type: .region)
    lazy var locationPermissionsView = LocationPermissionsView()
    private lazy var regionTitle = UILabel().then {
        $0.text = "选择地区"
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = color_3D3E40
    }
   
    private lazy var whiteBackgroundView = UIStackView().then{
        $0.spacing = 12
        $0.axis = .vertical
    }

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func layoutSubviews() {
        super.layoutSubviews()
    }

    // MARK: - 配置方法

    override func configUI() {
        locationPermissionsView.isHidden = true
        self.addSubview(whiteBackgroundView)
        whiteBackgroundView.addArrangedSubview(locationPermissionsView)
        whiteBackgroundView.addArrangedSubview(regionTitle)
        whiteBackgroundView.addArrangedSubview(regionFilterView)
        
        whiteBackgroundView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.equalTo(12)
            make.bottom.equalTo(0)
        }
        locationPermissionsView.snp.makeConstraints { make in
            make.height.equalTo(68)
        }
        regionFilterView.snp.makeConstraints { make in
            make.height.equalTo(24)
        }
    }
    
    
  

    /// 设置地区筛选器
    func setupRegionFilter(regionList:[RegionListItemModel]) {
        self.regions = regionList
        for (index, region) in regions.enumerated() {
            regionFilterView.addButton(
                title: region.name,
                isSelected: region.isSelected,
                delegate: self
            )
        }
    }

    

    // MARK: - 选择处理

    

    private func selectRegion(index: Int) {
        regionFilterView.selectButton(at: index)
        selectedRegionIndex = index
    }


    // MARK: - 公开方法

    /// 获取当前选中的地区
    func getSelectedRegion() -> RegionListItemModel {
        return regions[selectedRegionIndex]
    }
    
    /// 设置选中的地区
    func setSelectedRegion(index: Int) {
        selectRegion(index: index)
    }
    
   

    /// 获取当前选中的地区索引
    func getSelectedRegionIndex() -> Int {
        return selectedRegionIndex
    }
 
}

// MARK: - FilterButtonCellDelegate

extension HomeLocationHeaderView: FilterButtonCellDelegate {
    func filterButtonCellDidTap(_ buttonCell: FilterButtonCell) {
        handleButtonTap(buttonCell)
    }
    
    private func handleButtonTap(_ buttonCell: FilterButtonCell) {
        if let index = regionFilterView.getButtons().firstIndex(of: buttonCell) {
            handleRegionButtonTap(index)
        }
    }
    
    
    
    private func handleRegionButtonTap(_ index: Int) {
        selectRegion(index: index)
        delegate?.filterView(self, didSelectRegion: regions[index])
    }
 
}

