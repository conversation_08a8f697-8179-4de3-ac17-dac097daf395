//
//  HomeViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2023/6/10.
//

import Foundation
import Combine

/// 首页列表类型枚举
enum HomeListType {
    case recommend      // 推荐列表
    case location       // 位置筛选列表
    case follow         // 关注列表

    var description: String {
        switch self {
        case .recommend: return "推荐"
        case .location: return "位置筛选"
        case .follow: return "关注"
        }
    }
}

/// 首页ViewModel - 负责管理首页的广告列表、推荐列表、位置筛选列表和关注列表
class HomeViewModel: BaseViewModel {

    // MARK: - Published Properties

    /// 广告列表数据（包含banner和热门活动）
    @Published private(set) var adList: HomeAdlistModel?

    /// 推荐列表数据
    @Published private(set) var recommendList: [RecommendModel] = []

    /// 位置筛选列表数据
    @Published private(set) var locationList: [RecommendModel] = []

    /// 关注列表数据
    @Published private(set) var followList: [RecommendModel] = []
    /// 地区列表数据
    @Published var regionList: [RegionListItemModel] = []
    /// 操作结果发布者
    let likeResultPublisher = PassthroughSubject<Bool, Never>()

    // MARK: - Private Properties

    /// 当前位置筛选条件
    private var currentLocation: String = ""

    // MARK: - 初始化

    override init() {
        super.init()
        setupBindings()
    }

    // MARK: - 绑定设置

    override func setupBindings() {
        super.setupBindings()
        // 可以在这里添加其他数据绑定逻辑
    }

    // MARK: - 广告数据加载

    /// 获取广告列表（包含banner和热门活动）
    func fetchAdList() {
        requestModel(HomeService.adlist, type: HomeAdlistModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.handleError(error, operation: "获取广告列表")
                        self?.adList = nil
                    }
                },
                receiveValue: { [weak self] models in
                    self?.adList = models
                    print("广告列表加载成功，banner数量: \(models.adList.count), 活动数量: \(models.offList.banner.count)")
                }
            )
            .store(in: &cancellables)
    }

    // MARK: - 推荐列表数据加载

    /// 重写刷新数据方法（默认刷新推荐列表）
    override func refreshData() {
        fetchRecommendList(isRefresh: true)
    }

    /// 重写加载更多数据方法（默认加载推荐列表）
    override func loadMoreData() {
        fetchRecommendList(isRefresh: false)
    }

    /// 获取推荐列表
    /// - Parameter isRefresh: 是否为刷新操作（true: 下拉刷新, false: 上拉加载更多）
    func fetchRecommendList(isRefresh: Bool = true) {
        updateRefreshState(isRefresh: isRefresh, listType: .recommend)

        let params: [String: Any] = [
            "page": currentPage,
            "limit": pageSize,
            "noCache": 1
        ]

        requestPageData(AssemblyService.allList(params: params), type: RecommendModel.self, isRefresh: isRefresh) { [weak self] newData, isRefresh in
            guard let self = self else { return }

            if isRefresh {
                self.recommendList = newData
            } else {
                self.recommendList.append(contentsOf: newData)
            }
        }
        .sink(
            receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.handleError(error, operation: "获取推荐列表")
                }
            },
            receiveValue: { [weak self] _ in
                guard let self = self else { return }
                print("推荐列表加载成功，共\(self.recommendList.count)条数据")
            }
        )
        .store(in: &cancellables)
    }

    // MARK: - 位置筛选列表数据加载

    /// 获取位置筛选的推荐列表
    /// - Parameters:
    ///   - location: 位置筛选条件
    ///   - isRefresh: 是否为刷新操作
    func fetchLocationRecommendList(location: String, isRefresh: Bool = true) {
        // 更新当前位置筛选条件
        if isRefresh {
            currentLocation = location
        }

        updateRefreshState(isRefresh: isRefresh, listType: .location)

        let params: [String: Any] = [
            "page": currentPage,
            "limit": pageSize,
            "location": location,
            "noCache": 1
        ]

        requestPageData(AssemblyService.allList(params: params), type: RecommendModel.self, isRefresh: isRefresh) { [weak self] newData, isRefresh in
            guard let self = self else { return }

            if isRefresh {
                self.locationList = newData
            } else {
                self.locationList.append(contentsOf: newData)
            }
            // 根据数据数量更新空数据状态
            self.updateEmptyDataSetForData(count: self.locationList.count)
        }
        .sink(
            receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.handleError(error, operation: "获取位置筛选列表")
                }
            },
            receiveValue: { [weak self] _ in
                guard let self = self else { return }
                print("位置筛选列表加载成功，位置: \(location)，共\(self.locationList.count)条数据")
            }
        )
        .store(in: &cancellables)
    }
    // MARK: - Public Methods

    /// 获取地区列表
    /// - Parameter parent_code: 父级地区代码，nil表示获取省份列表
    func fetchRegionList(parent_code: String?) {
        var params = RequestParameters(["": ""])
        if let parent_code = parent_code {
            params = RequestParameters([
                "parent_code": parent_code,
            ])
        }

        requestModel(CommentService.regionList(params: params), type: [RegionListItemModel].self)
            .sink { [weak self] completion in
                switch completion {
                case .finished:
                    break
                case .failure(let error):
                    self?.regionList = []
                }
            } receiveValue: { [weak self] models in
                self?.regionList = models
            }
            .store(in: &cancellables)
    }
    // MARK: - 关注列表数据加载

    /// 获取关注列表
    /// - Parameter isRefresh: 是否为刷新操作
    func fetchFollowList(isRefresh: Bool = true) {
        updateRefreshState(isRefresh: isRefresh, listType: .follow)

        let params: [String: Any] = [
            "page": currentPage,
            "limit": pageSize,
            "noCache": 1
        ]

        requestPageData(AssemblyService.followList(params: params), type: RecommendModel.self, isRefresh: isRefresh) { [weak self] newData, isRefresh in
            guard let self = self else { return }

            if isRefresh {
                self.followList = newData
            } else {
                self.followList.append(contentsOf: newData)
            }
        }
        .sink(
            receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.handleError(error, operation: "获取关注列表")
                }
            },
            receiveValue: { [weak self] _ in
                guard let self = self else { return }
                print("关注列表加载成功，共\(self.followList.count)条数据")
            }
        )
        .store(in: &cancellables)
    }

    // MARK: - 帖子互动操作

    /// 帖子点赞/取消点赞
    /// - Parameters:
    ///   - type: 帖子类型
    ///   - postId: 帖子ID
    func toggleLike(type: Int, postId: String) {
        guard !postId.isEmpty else {
            print("帖子ID不能为空")
            return
        }

        let params = RequestParameters([
            "post_id": postId,
            "type": type
        ])

        request(CommentService.likeToggle(params: params))
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error, operation: "帖子点赞")
                        self?.likeResultPublisher.send(false)
                    }
                },
                receiveValue: { [weak self] _ in
                    self?.likeResultPublisher.send(true)
                    print("帖子点赞操作成功")
                }
            )
            .store(in: &cancellables)
    }

    // MARK: - 私有辅助方法

    /// 更新刷新状态
    /// - Parameters:
    ///   - isRefresh: 是否为刷新操作
    ///   - listType: 列表类型
    private func updateRefreshState(isRefresh: Bool, listType: HomeListType) {
        if isRefresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }
    }

    /// 统一的错误处理方法
    /// - Parameters:
    ///   - error: 网络错误
    ///   - operation: 操作描述
    override func handleError(_ error: NetworkError, operation: String) {
        // 调用父类方法处理空数据状态
        super.handleError(error, operation: operation)

        // 添加特定的错误处理逻辑
        switch error {
        case .networkError(let response):
            print("\(operation)失败: \(response.message)")
        case .decodingError(let message):
            print("\(operation)数据解析错误: \(message)")
        case .noConnection:
            print("\(operation)失败: 网络连接失败")
        case .tokenExpired:
            print("\(operation)失败: Token已过期")
        case .tokenError:
            print("\(operation)失败: Token错误")
        }
    }
}
