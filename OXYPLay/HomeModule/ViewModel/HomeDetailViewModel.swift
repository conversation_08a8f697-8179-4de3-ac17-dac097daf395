//
//  HomeDetailViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/3.
//

import Foundation
import Combine

/// 首页详情页ViewModel - 负责管理帖子详情、评论列表等数据
class HomeDetailViewModel: BaseViewModel {

    // MARK: - Published Properties

    /// 详情数据
    @Published var detailData: HomeDetailModel?

    /// 评论列表数据
    @Published var commentList: [CommentModel] = []

    /// 操作结果发布者
    let likeResultPublisher = PassthroughSubject<Bool, Never>()
    let favoriteResultPublisher = PassthroughSubject<Bool, Never>()
    let followResultPublisher = PassthroughSubject<Bool, Never>()
    let commentPostResultPublisher = PassthroughSubject<Bool, Never>()

    // MARK: - Private Properties

    /// 帖子ID
    private let postId: String

    /// 页面类型:1为广告
    private var pageType: Int = 1

    /// 商品所属广告主ID（仅广告类型使用）
    var advertiserId: String = ""

    // MARK: - 初始化

    /// 初始化详情页ViewModel
    /// - Parameters:
    ///   - postId: 帖子ID
    ///   - pageType: 页面类型（推荐帖子或广告帖子）
    init(postId: String, pageType: Int?) {
        self.postId = postId
        self.pageType = pageType ?? 1
        super.init()
    }

    // MARK: - 数据加载方法

    /// 加载页面数据（详情数据 + 评论列表）
    func loadData() {
        // 同时加载详情数据和评论列表
        fetchDetailData()
        fetchCommentList(refresh: true)
    }

    /// 获取详情数据（根据页面类型选择不同的API）
    private func fetchDetailData() {
        if pageType == 1 {
            fetchAdDetailData()

        }else{
            fetchRecommendDetailData()
        }
    }

    /// 获取广告详情数据
    private func fetchAdDetailData() {
        let params = RequestParameters([
            "pid": postId
        ])

        requestModel(HomeService.detail(params: params), type: HomeDetailModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error, operation: "获取广告详情")
                    }
                },
                receiveValue: { [weak self] model in
                    self?.detailData = model
                    self?.advertiserId = model.user_id
                }
            )
            .store(in: &cancellables)
    }

    /// 获取推荐详情数据
    private func fetchRecommendDetailData() {
        let serviceType = ServiceType(rawValue: pageType) ?? .all
        let params = RequestParameters([
            "id": postId
        ])

        requestModel(AssemblyService.detailServiceBy(type: serviceType, params: params), type: HomeDetailModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error, operation: "获取推荐详情")
                    }
                },
                receiveValue: { [weak self] model in
                    self?.detailData = model
                }
            )
            .store(in: &cancellables)
    }
    /// 获取评论列表
    /// - Parameter refresh: 是否为刷新操作（true: 下拉刷新, false: 上拉加载更多）
    func fetchCommentList(refresh: Bool = false) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

        // 构建请求参数
        let params = RequestParameters([
            "post_id": postId,
            "page": currentPage,
            "limit": pageSize,
            "type": pageType
        ])

        requestPageData(CommentService.list(params: params), type: CommentModel.self, isRefresh: refresh) { [weak self] newData, isRefresh in
            guard let self = self else { return }

            if isRefresh {
                self.commentList = newData
            } else {
                self.commentList.append(contentsOf: newData)
            }
        }
        .sink(
            receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.handleError(error, operation: "获取评论列表")
                }
            },
            receiveValue: { _ in
                print("评论列表加载成功，共\(self.commentList.count)条评论")
            }
        )
        .store(in: &cancellables)
    }

    // MARK: - 重写BaseViewModel方法

    /// 下拉刷新回调
    override func refreshData() {
        fetchCommentList(refresh: true)
    }

    /// 上拉加载更多回调
    override func loadMoreData() {
        fetchCommentList(refresh: false)
    }

    // MARK: - 评论相关操作

    /// 发表评论
    /// - Parameter content: 评论内容
    func postComment(content: String) {
        guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("评论内容不能为空")
            return
        }

        // 构建请求参数
        let params = RequestParameters([
            "post_id": postId,
            "content": content,
            "type": pageType
        ])

        request(CommentService.create(params: params))
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error, operation: "发表评论")
                        self?.commentPostResultPublisher.send(false)
                    }
                },
                receiveValue: { [weak self] _ in
                    // 发表成功，刷新评论列表
                    self?.fetchCommentList(refresh: true)
                    self?.commentPostResultPublisher.send(true)
                    print("评论发表成功")
                }
            )
            .store(in: &cancellables)
    }

    /// 评论点赞/取消点赞
    /// - Parameter commentId: 评论ID
    func commentLike(commentId: String) {
        let params = RequestParameters([
            "comment_id": commentId
        ])

        request(CommentService.like(params: params))
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error, operation: "评论点赞")
                    }
                },
                receiveValue: { [weak self] _ in
                    // 点赞成功，刷新评论列表
                    self?.fetchCommentList(refresh: true)
                    print("评论点赞操作成功")
                }
            )
            .store(in: &cancellables)
    }

    // MARK: - 帖子互动操作

    /// 帖子点赞/取消点赞
    func toggleLike() {
        // 构建请求参数
        let params = RequestParameters([
            "post_id": postId,
            "type": pageType
        ])

        request(CommentService.likeToggle(params: params))
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error, operation: "帖子点赞")
                        self?.likeResultPublisher.send(false)
                    }
                },
                receiveValue: { [weak self] _ in
                    // 点赞成功，重新获取详情数据
                    self?.fetchDetailData()
                    self?.likeResultPublisher.send(true)
                    print("帖子点赞操作成功")
                }
            )
            .store(in: &cancellables)
    }

    /// 帖子收藏/取消收藏
    func toggleFavorite() {
        // 构建请求参数
        let params = RequestParameters([
            "post_id": postId,
            "type": pageType
        ])

        request(CommentService.favoriteToggle(params: params))
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error, operation: "帖子收藏")
                        self?.favoriteResultPublisher.send(false)
                    }
                },
                receiveValue: { [weak self] _ in
                    // 收藏成功，重新获取详情数据
                    self?.fetchDetailData()
                    self?.favoriteResultPublisher.send(true)
                    print("帖子收藏操作成功")
                }
            )
            .store(in: &cancellables)
    }

    // MARK: - 用户关注操作

    /// 关注/取消关注用户
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - isFollow: 当前是否已关注（true: 已关注需要取消, false: 未关注需要关注）
    func toggleFollow(userId: String, isFollow: Bool) {
        guard !userId.isEmpty else {
            print("用户ID不能为空")
            return
        }

        // 构建请求参数
        let params = RequestParameters([
            "followee_id": userId
        ])

        // 选择API（注意：isFollow为true表示当前已关注，需要取消关注）
        let service: MineService = isFollow ? .unfollow(params: params) : .follow(params: params)
        let operation = isFollow ? "取消关注" : "关注"

        // 发起网络请求
        requestModel(service, type: EmptyResponse.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.handleError(error, operation: operation)
                        self?.followResultPublisher.send(false)
                    }
                },
                receiveValue: { [weak self] _ in
                    // 操作成功，重新获取详情数据
                    self?.fetchDetailData()
                    self?.followResultPublisher.send(true)

                    // 刷新用户信息以获取最新的关注数
                    UserManager.shared.refreshUserInfo()
                    print("\(operation)操作成功")
                }
            )
            .store(in: &cancellables)
    }

    // MARK: - 错误处理

    /// 统一的错误处理方法
    /// - Parameters:
    ///   - error: 网络错误
    ///   - operation: 操作描述
    override func handleError(_ error: NetworkError, operation: String) {
        // 调用父类方法处理空数据状态
        super.handleError(error, operation: operation)

        // 添加特定的错误处理逻辑
        switch error {
        case .networkError(let response):
            print("\(operation)失败: \(response.message)")
        case .decodingError(let message):
            print("\(operation)数据解析错误: \(message)")
        case .noConnection:
            print("\(operation)失败: 网络连接失败")
        case .tokenExpired:
            print("\(operation)失败: Token已过期")
        case .tokenError:
            print("\(operation)失败: Token错误")
        }
    }
}
