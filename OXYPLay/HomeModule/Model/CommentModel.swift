//
//  CommentModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/1.
//

import SmartCodable

/// 评论模型
struct CommentModel: SmartCodable {
    /// 评论ID
    var id: String = ""
    var post_id: String = ""
    /// 评论内容
    var content: String = ""
    /// 创建时间
    var created_at: String = ""
    var like_count: String = ""
    var is_liked: Bool = false
    var user:RecommendUserModel = RecommendUserModel()
    
}
