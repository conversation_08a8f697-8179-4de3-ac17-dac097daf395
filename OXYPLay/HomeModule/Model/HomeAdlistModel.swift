import Foundation
import SmartCodable

/// 首页内容数据模型 - 包含广告列表和官方内容列表
struct HomeAdlistModel: SmartCodable {
    /// 广告主内容列表 - 包含平台商家的推广内容
    var adList: [AdItem] = []

    /// 官方内容列表 - 包含平台官方的公告、活动和更新信息
    var offList: OffListContent = .init()
}

/// 官方内容分类模型 - 包含不同类型的官方内容
struct OffListContent: SmartCodable {
    /// 轮播图内容
    var banner: [BannerItem] = []

    /// 商品推荐内容
    var product: [ProductItem] = []

    /// 更新公告内容
    var update: [UpdateItem] = []
}

/// 通用内容项协议 - 定义所有内容项共有的属性
protocol ContentItem {
    var title: String { get }
    var imageUrl: String { get }
    var id: String { get }
    var price: String { get }
    var origin_price: String { get }
}

/// 广告内容项模型 - 表示广告列表中的单个项目
struct AdItem: SmartCodable, ContentItem {
    var origin_price: String = ""
    var price: String = ""
    var id: String = ""
    var userID: String = ""
    var title: String = ""
    var description: String = ""
    var content: String = ""
    var imgUrls: String = ""
    var type: String = ""
    var createdAt: String = ""
    var updatedAt: String = ""
    var category: String = ""
    var userTxt: String = ""
    var categoryTxt: String = ""

    // SmartCodable属性映射
    static func mappingForKey() -> [SmartKeyTransformer]? {
        return [
            CodingKeys.userID <--- "user_id",
            CodingKeys.imgUrls <--- "img_urls",
            CodingKeys.createdAt <--- "created_at",
            CodingKeys.updatedAt <--- "updated_at",
            CodingKeys.userTxt <--- "user_txt",
            CodingKeys.categoryTxt <--- "category_txt",
        ]
    }

    // 实现ContentItem协议
    var imageUrl: String {
        return imgUrls
    }

    // 获取类型枚举
    var itemType: ItemType? {
        return ItemType(rawValue: type)
    }

    // 获取分类枚举
    var itemCategory: ItemCategory? {
        return ItemCategory(rawValue: category)
    }
}

/// 轮播图内容项模型 - 表示轮播图中的单个项目
struct BannerItem: SmartCodable, ContentItem {
    var id: String = ""
    var price: String = ""
    var title: String = ""
    var origin_price: String = ""
    var imageURLs: [String] = []

    // SmartCodable属性映射
    static func mappingForKey() -> [SmartKeyTransformer]? {
        return [
            CodingKeys.imageURLs <--- "img_urls",
        ]
    }

    // 实现ContentItem协议
    var imageUrl: String {
        return imageURLs.first ?? ""
    }
}

/// 商品内容项模型 - 表示商品推荐中的单个项目
struct ProductItem: SmartCodable, ContentItem {
    var title: String  = ""
    var imageUrl: String  = ""
    var id: String = ""
    var price: String = ""
    var origin_price: String = ""
    var seller_id: String = ""
    static func mappingForKey() -> [SmartKeyTransformer]? {
        return [
            CodingKeys.imageUrl <--- "thumb_image",
        ]
    }
}
/// 更新内容项模型 - 表示更新公告中的单个项目
struct UpdateItem: SmartCodable, ContentItem {
    var id: String = ""
    var origin_price: String = ""
    var price: String = ""
    var title: String = ""
    var description: String = ""
    var content: String = ""
    var createdAt: String = ""

    // SmartCodable属性映射
    static func mappingForKey() -> [SmartKeyTransformer]? {
        return [
            CodingKeys.createdAt <--- "created_at",
        ]
    }

    // 实现ContentItem协议
    var imageUrl: String {
        return ""
    }
}

/// 内容类型枚举
enum ItemType: String {
    case advertiser = "1"
    case official = "2"
}

/// 内容分类枚举
enum ItemCategory: String {
    case product = "1"
    case banner = "2"
    case updateNotice = "3"

    var displayText: String {
        switch self {
        case .product:
            return "商品"
        case .banner:
            return "普通banner"
        case .updateNotice:
            return "提示更新"
        }
    }
}
