import Foundation
import SmartCodable

/// 广告详情模型 - 表示单个广告的详细信息
struct HomeDetailModel: SmartCodable {
    /// 帖子ID
    var id: String = ""
    
    /// 发布用户ID
    var user_id: String = ""
    var user_avatar: String = ""
    var user_nickname: String = ""
    var is_following: Bool = false
    var img: String = ""
    var postage_fee: String = "0"
    var coupon:CouponModel = CouponModel()
    var productAddressModel:ProductAddressModel = ProductAddressModel()

    ///状态（状态：1发布中，2已下架，3已成交 4草稿）
    var status: String = ""

    /// 标题
    var title: String = ""
    
    /// 帖子描述
    var description: String = ""
    
    /// 帖子内容正文
    var content: String = ""
    
    /// 发布位置
    var location: String = ""
    
    /// 点赞数
    var likeCount: Int = 0
    
    /// 收藏数
    var favoriteCount: Int = 0
    
    /// 评论数
    var commentCount: Int = 0
    
    /// 是否已点赞
    var is_liked: Bool = false
    
    /// 是否已收藏
    var is_favorited: Bool = false
    /// 是否显示够买
    var has_price: Bool = false
    /// 配图URL列表
    var imgUrls: [String] = []
    
    /// 外链商品ID
    var linkID: String?
    
    /// 官方商品IDs
    var skuIDs: String = ""
    
    /// 是否公开：1公开，2私密
    var isPublic: String = "1"
    
    /// 创建时间
    var createdAt: String = ""
    
    /// 更新时间
    var updatedAt: String = ""
    
    /// 分类
    var category: String = ""
    
    /// 类型
    var type: String = ""
    var type_text: String = ""
    var postage_type_text: String = ""
    var price: String = ""
    var origin_price: String = ""
    var quantity: String = ""
    /// 浏览量
    var views: Int = 0
    
    /// 发布用户信息
    var user: RecommendUserModel = .init()
    
    /// 外链产品信息
    var product: AdDetailProductInfo?
    
    /// 官方商品列表
    var skuList: [AdDetailSkuItem] = []
    
    // SmartCodable属性映射
    static func mappingForKey() -> [SmartKeyTransformer]? {
        return [
            CodingKeys.likeCount <--- "like_count",
            CodingKeys.favoriteCount <--- "favorite_count",
            CodingKeys.commentCount <--- "comment_count",
            CodingKeys.imgUrls <--- "img_urls",
            CodingKeys.linkID <--- "link_id",
            CodingKeys.skuIDs <--- "sku_ids",
            CodingKeys.isPublic <--- "is_public",
            CodingKeys.createdAt <--- "created_at",
            CodingKeys.updatedAt <--- "updated_at",
            CodingKeys.skuList <--- "sku_list"
        ]
    }
    
    /// 获取类型枚举
    var itemType: ItemType? {
        return ItemType(rawValue: type)
    }
    
    /// 获取分类枚举
    var itemCategory: ItemCategory? {
        return ItemCategory(rawValue: category)
    }
    
    /// 是否为公开帖子
    var isPublicPost: Bool {
        return isPublic == "1"
    }
    
    /// 城市（从location分离）
    var city: String {
        return location.components(separatedBy: " · ").first ?? ""
    }
    
    /// 场馆（从location分离）
    var venue: String {
        return location.components(separatedBy: " · ").last ?? ""
    }
    
    /// 是否为热门/精选内容
    var isHot: Bool {
        return true // 模拟数据，实际应该根据API返回
    }
    
    /// 格式化的创建时间
    var createTime: String {
        // 实际项目中可以进行更复杂的时间格式化
        return createdAt
    }
    
    /// 商品列表的便捷访问
    var skuItems: [AdDetailSkuItem] {
        return skuList
    }
}


/// 广告详情外链产品信息模型
struct AdDetailProductInfo: SmartCodable {
    /// 产品ID
    var id: String = ""
    
    /// 产品名称
    var name: String = ""
    
    /// 链接地址
    var linkUrl: String = ""
    
    /// 图片URL
    var imageUrl: String = ""
    
    /// 来源平台（如淘宝、京东、Burton官网）
    var sourcePlatform: String = ""
    
    /// 简要标签（如"推荐款"）
    var tag: String = ""
    
    // SmartCodable属性映射
    static func mappingForKey() -> [SmartKeyTransformer]? {
        return [
            CodingKeys.linkUrl <--- "link_url",
            CodingKeys.imageUrl <--- "image_url",
            CodingKeys.sourcePlatform <--- "source_platform"
        ]
    }
}

/// 广告详情SKU商品模型
struct AdDetailSkuItem: SmartCodable {
    /// 商品ID
    var product_id: String = ""
    
    /// 商品名称
    var name: String = ""
    
    /// 价格
    var price: String = ""
    
    /// 库存
    var cover_image: String = ""
    /// 库存
    var view: String = ""

}
