//
//  HomeLocationViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/26.
//

import UIKit
import Combine

class HomeLocationViewController: JXBaseViewController {
    // MARK: - Properties
    
    private let viewModel = HomeViewModel()
    @Published var location = ""
    var filterView = HomeLocationHeaderView()

    // MARK: - UI Components
    
    lazy var recommendView: RecommendView = {
        let recommendView = RecommendView()
        recommendView.delegate = self
        return recommendView
    }()
  
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
    }
    
    // MARK: - Private Methods
    
    override func configUI() {
        view.addSubview(recommendView)
        view.addSubview(filterView)
        filterView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
        }
        recommendView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(filterView.snp.bottom)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }
    }
    
   override func setupBindings() {
        $location
            .receive(on: RunLoop.main)
            .sink { [weak self] models in
                guard let self = self else { return }
                self.loadData()
            }
            .store(in: &cancellables)
        // 绑定推荐列表
        viewModel.$locationList
            .receive(on: RunLoop.main)
            .sink { [weak self] models in
                guard let self = self else { return }
                self.recommendView.updateRecommendList(models)
            }
            .store(in: &cancellables)
       // 绑定位置信息
       viewModel.$regionList
           .receive(on: RunLoop.main)
           .sink { [weak self] models in
               guard let self = self else { return }
               self.filterView.setupRegionFilter(regionList: models)
           }
           .store(in: &cancellables)
       // 监听空数据状态变化
       viewModel.$emptyDataSetType
           .receive(on: DispatchQueue.main)
           .sink { [weak self] emptyType in
               guard let self = self else { return }
               if let type = emptyType {
                   self.setEmptyDataSet(for: self.recommendView.collectionView, type: type)
               } else {
                   self.hideEmptyDataSet(for: self.recommendView.collectionView)
               }
           }
           .store(in: &cancellables)
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: recommendView.collectionView, with: viewModel)
        // 绑定like结果
        viewModel.likeResultPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] success in
                guard let self = self else { return }
                if success {
                    self.loadData()
                }
            }
            .store(in: &cancellables)
        
    }
    
    private func loadData() {
        // 加载位置筛选的数据
        viewModel.fetchLocationRecommendList(location: location, isRefresh: true)
        viewModel.fetchRegionList(parent_code: nil)
    }
}

// MARK: - RecommendViewDelegate

extension HomeLocationViewController: RecommendViewDelegate {
    func likeItem(item: RecommendModel) {
        viewModel.toggleLike(type: item.type, postId: item.id)

    }
    
    func pushToAdDetail(item: any ContentItem) {
        
    }
    
    func pushToProductDetail(item: ProductItem) {
        
    }
    
    func refreshRecommendData() {
        // 下拉刷新，重新加载数据
        viewModel.fetchLocationRecommendList(location: location, isRefresh: true)
    }
    
    func loadMoreRecommendData() {
        // 上拉加载更多数据
        viewModel.fetchLocationRecommendList(location: location, isRefresh: false)
    }
    
    func pushToRecommendDetail(item: RecommendModel) {
        let vc = HomeDetailController(postId: item.id, pageType: item.type)
        subJXPushVC(vc: vc, hiddenTabbar: true, animated: true)
    }
}

