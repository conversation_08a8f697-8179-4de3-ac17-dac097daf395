//
//  HomeViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/22.
//

import UIKit

class HomeViewController: JXBaseRootViewController {
    let locationVc = HomeLocationViewController()
    let recommendVc = HomeRecommendViewController()
    let followVc = HomeFollowViewController()

    override func viewDidLoad() {
        configSegement()
        super.viewDidLoad()
        configUI()
    }
    func configSegement(){
        // 配置属性
        titles = ["地区", "推荐", "关注"]
        viewControllers = [locationVc, recommendVc, followVc]
        locationVc.filterView.delegate = self
        // 配置UI
        segmentedViewTop = 0
        segmentedViewHeight = 36
        segmentedViewBackgroundColor = color_00000004
        segmentedViewCornerRadius = 18
        segmentedViewContentEdgeInsetLeft = 17
        segmentedViewContentEdgeInsetRight = 17
        segmentedViewMaskedCorners = [.layerMinXMinYCorner,.layerMinXMaxYCorner,.layerMaxXMaxYCorner,.layerMaxXMinYCorner]
        
        // 配置指示器
        indicatorType = .background
        indicatorColor = .white
        indicatorWidth = 28
        indicatorHeight = 30
        
        // 配置标题
        titleSelectedColor = color_2B2C2F
        titleNormalColor = color_2B2C2F64
        titleNormalFont = .systemFont(ofSize: 14, weight: .regular)
        titleSelectedFont = .systemFont(ofSize: 14, weight: .medium)
        
        // 设置默认选中索引
        defaultSelectedIndex = 1
       
    }
    override func configUI(){
        // 添加自定义导航栏
        view.addSubview(navView)
        navView.addSubview(segmentedView)
        navView.addSubview(menuButton)
        navView.addSubview(qrCodeButton)
        
        // 重新布局
        navView.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalNavBarHeight)
        }
        
        menuButton.snp.makeConstraints { make in
            make.centerY.equalTo(segmentedView)
            make.left.equalTo(20)
        }
        
        qrCodeButton.snp.makeConstraints { make in
            make.centerY.equalTo(segmentedView)
            make.right.equalTo(-20)
        }
        
        segmentedView.snp.remakeConstraints { make in
            make.centerX.equalTo(self.view.snp.centerX)
            make.width.equalTo(165)
            make.bottom.equalTo(navView.snp.bottom).offset(-5)
            make.height.equalTo(segmentedViewHeight)
        }
        
        listContainerView.snp.remakeConstraints { make in
            make.top.equalTo(navView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
        
        // 设置子控制器的父控制器引用
        for vc in viewControllers {
            if let locationVC = vc as? HomeLocationViewController {
                locationVC.superJXVC = self
            } else if let recommendVC = vc as? HomeRecommendViewController {
                recommendVC.superJXVC = self
            } else if let followVC = vc as? HomeFollowViewController {
                followVC.superJXVC = self
            }
        }
    }
    lazy var menuButton = UIButton().then {
        $0.setImage(UIImage(named: "home_leftmenu"), for: .normal)
        $0.addTarget(self, action: #selector(showSlideMenu), for: .touchUpInside)
    }
    
    lazy var qrCodeButton = UIButton().then {
        $0.setImage(UIImage(named: "home_qrcode"), for: .normal)
        $0.addTarget(self, action: #selector(showQrCode), for: .touchUpInside)
    }
    
    lazy var navView = UIView().then {
        $0.backgroundColor = .white
    }
    
}
extension HomeViewController:HomeLocationHeaderViewDelegate{
    func filterView(_ filterView: BaseView, didSelectRegion region: RegionListItemModel) {
        locationVc.location = region.name
        titles = [region.name,"推荐","关注"]
        reloadData()
    }
}
