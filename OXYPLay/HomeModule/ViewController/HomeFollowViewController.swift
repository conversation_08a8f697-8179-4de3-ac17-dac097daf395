//
//  HomeFollowViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/26.
//

import UIKit

class HomeFollowViewController: JXBaseViewController {
    // MARK: - Properties
    
    private let viewModel = HomeViewModel()
    lazy var noFollowVC = MineaAddFriendController().then{
        $0.isNoFollow = true
    }
    // MARK: - UI Components
    
    lazy var recommendView: RecommendView = {
        let recommendView = RecommendView()
        recommendView.delegate = self
        return recommendView
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        loadData()
    }
    
    // MARK: - Private Methods
    
    override func configUI() {
        
        self.addChild(noFollowVC)
        view.addSubview(recommendView)
        recommendView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }
        view.addSubview(noFollowVC.view)
        noFollowVC.view.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }
    }
    
   override func setupBindings() {
        
        // 绑定关注列表
        viewModel.$followList
            .receive(on: RunLoop.main)
            .sink { [weak self] models in
                guard let self = self else {
                    return
                }
                guard !models.isEmpty else {
                    self.recommendView.isHidden = true
                    self.noFollowVC.view.isHidden = false
                    return
                }
                self.recommendView.isHidden = false
                self.noFollowVC.view.isHidden = true
                self.recommendView.updateRecommendList(models)
            }
            .store(in: &cancellables)
        
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: recommendView.collectionView, with: viewModel)
        // 绑定like结果
        viewModel.likeResultPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] success in
                guard let self = self else { return }
                if success {
                    self.loadData()
                }
            }
            .store(in: &cancellables)
    }
    
    private func loadData() {
        viewModel.fetchFollowList(isRefresh: true)
    }
}

// MARK: - RecommendViewDelegate

extension HomeFollowViewController: RecommendViewDelegate {
    func likeItem(item: RecommendModel) {
        viewModel.toggleLike(type: item.type, postId: item.id)
    }
    
    func pushToAdDetail(item: any ContentItem) {
        
    }
    
    func pushToRecommendDetail(item: RecommendModel) {
        let vc = HomeDetailController(postId: item.id, pageType: item.type)
        subJXPushVC(vc: vc, hiddenTabbar: true, animated: true)
    }
    
    func refreshRecommendData() {
        // 下拉刷新，重新加载数据
        viewModel.fetchFollowList(isRefresh: true)
    }
    
    func loadMoreRecommendData() {
        viewModel.fetchFollowList(isRefresh: false)
    }
    
    
}
