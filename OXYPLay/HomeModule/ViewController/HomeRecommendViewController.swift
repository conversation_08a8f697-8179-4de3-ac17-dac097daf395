//
//  HomeRecommendViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/26.
//

import JXSegmentedView
import UIKit
import Combine

class HomeRecommendViewController: JXBaseViewController {
    // MARK: - Properties

    private lazy var recommendView = RecommendView()
    private let viewModel = HomeViewModel()

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        loadData()
    }

    // MARK: - Private Methods

    internal override func configUI() {
        view.addSubview(recommendView)

        recommendView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }
        recommendView.delegate = self
    }
    
   override func setupBindings() {
        // 绑定广告列表
        viewModel.$adList
            .receive(on: RunLoop.main)
            .sink { [weak self] adModels in
                guard let self = self, let adModel = adModels else { return }
                self.recommendView.adModel = adModel
            }
            .store(in: &cancellables)
        
        // 绑定推荐列表
        viewModel.$recommendList
            .receive(on: RunLoop.main)
            .sink { [weak self] models in
                guard let self = self else { return }
                self.recommendView.updateRecommendList(models)
            }
            .store(in: &cancellables)
        
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: recommendView.collectionView, with: viewModel)
        // 绑定like结果
        viewModel.likeResultPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] success in
                guard let self = self else { return }
                if success {
                    self.loadData()
                }
            }
            .store(in: &cancellables)
        
    }

    private func loadData() {
        viewModel.fetchAdList()
        viewModel.fetchRecommendList(isRefresh: true)
    }
}

// MARK: - RecommendViewDelegate

extension HomeRecommendViewController: RecommendViewDelegate {
    func likeItem(item: RecommendModel) {
        viewModel.toggleLike(type: item.type, postId: item.id)
    }
    
    func refreshRecommendData() {
        recommendView.clearHeightCache()
        loadData()
    }
    
    func loadMoreRecommendData() {
        viewModel.fetchRecommendList(isRefresh: false)
    }
    
    func pushToRecommendDetail(item: RecommendModel) {
        let vc = HomeDetailController(postId: item.id, pageType: item.type)
        subJXPushVC(vc: vc, hiddenTabbar: true, animated: true)
    }

    func pushToAdDetail(item: ContentItem) {
        if let adItem = item as? AdItem {
            let vc = HomeDetailController(postId: adItem.id, pageType: 1)
            subJXPushVC(vc: vc, hiddenTabbar: true, animated: true)
        }
        if let product = item as? ProductItem {
            let vc = ProductDetailController(postId: product.id, seller_id: product.seller_id)
            subJXPushVC(vc: vc, hiddenTabbar: true, animated: true)
        }
    }
    
}
