//
//  HomeDetailController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/2.
//

import UIKit
class HomeDetailController: BaseViewController {
    
    // MARK: - 属性
    
    private let viewModel: HomeDetailViewModel
    private let postId: String
    private let pageType: Int?//帖子类型
    
    // 区域类型
    private enum SectionType: Int, CaseIterable {
        case images = 0
        case info
        case productlink
        case recommend
        case commentTitle
        case comments
    }
    
    // MARK: - UI组件
    
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .grouped)
        tv.backgroundColor = color_F3F6F7
        tv.showsVerticalScrollIndicator = false
        tv.delegate = self
        tv.dataSource = self
        tv.separatorStyle = .none
        tv.estimatedRowHeight = 100
        tv.rowHeight = UITableView.automaticDimension
        tv.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 60, right: 0)
        tv.backgroundColor = .clear
        // 注册Cell
        tv.register(DetailImageTableCell.self, forCellReuseIdentifier: "DetailImageTableCell")
        tv.register(DetailInfoTableCell.self, forCellReuseIdentifier: "DetailInfoTableCell")
        tv.register(DetailProductLinkTableCell.self, forCellReuseIdentifier: "DetailProductLinkTableCell")
        tv.register(DetailRecommendTableCell.self, forCellReuseIdentifier: "DetailRecommendTableCell")
        tv.register(DetailCommentTitleTableCell.self, forCellReuseIdentifier: "DetailCommentTitleTableCell")
        tv.register(CommentItemTableCell.self, forCellReuseIdentifier: "CommentItemTableCell")
        return tv
    }()
    
    private lazy var toolBar = DetailBottomToolBar().then {
        $0.delegate = self
    }
    private lazy var navBar = DetailNavBarView().then{
        $0.backgroundColor = .white
        $0.delegate = self
    }
    // MARK: - 初始化
    
    init(postId: String,pageType:Int?) {
        self.postId = postId
        self.pageType = pageType
        self.viewModel = HomeDetailViewModel(postId: postId,pageType: pageType)
        super.init()
    }
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
        viewModel.loadData()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        
        // 设置导航栏
        self.fd_prefersNavigationBarHidden = true
        view.addSubview(navBar)
        view.addSubview(tableView)
        view.addSubview(toolBar)

        navBar.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalNavBarHeight)
        }
        tableView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(navBar.snp.bottom)
            make.bottom.equalTo(toolBar.snp.top)
        }
        
        toolBar.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
    }
    
    // MARK: - 数据绑定
    
   override func setupBindings() {
        // 使用BaseViewController的便捷方法设置刷新（只启用底部加载更多）
        setupRefresh(for: tableView, with: viewModel, enableHeader: false, enableFooter: true)

        // 监听详情数据变化
        viewModel.$detailData
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.tableView.reloadData()
                self?.updateToolBar()
            }
            .store(in: &cancellables)
        // 监听评论列表变化
        viewModel.$commentList
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 辅助方法
    
    private func updateToolBar() {
        if let detailData = viewModel.detailData {
            toolBar.configure(with: detailData)
            if let _ = pageType {
                navBar.configUser(userName: detailData.user_nickname, userAvatar: detailData.user_avatar, is_following: detailData.is_following)
            }else{
                navBar.configUser(userName: detailData.user.nickname, userAvatar: detailData.user.avatar, is_following: detailData.is_following)
            }
            navBar.isSelfPush(userId: detailData.user_id)
        }
    }
    
}

// MARK: - UITableViewDataSource, UITableViewDelegate

extension HomeDetailController: UITableViewDataSource, UITableViewDelegate {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return SectionType.allCases.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let detailData = viewModel.detailData else { return 0 }
        
        guard let sectionType = SectionType(rawValue: section) else { return 0 }
        
        switch sectionType {
        case .images, .info:
            return 1
        case .productlink:
            return detailData.product != nil ? 1 : 0
        case  .recommend:
            return detailData.skuList.isEmpty ? 0 : 1
        case  .commentTitle:
            return  viewModel.commentList.isEmpty ? 0 : 1
        case .comments:
            return viewModel.commentList.count
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let detailData = viewModel.detailData else {
            return UITableViewCell()
        }
        
        guard let sectionType = SectionType(rawValue: indexPath.section) else {
            return UITableViewCell()
        }
        
        switch sectionType {
        case .images:
            let cell = tableView.dequeueReusableCell(withIdentifier: "DetailImageTableCell", for: indexPath) as! DetailImageTableCell
            cell.configure(with: detailData)
            return cell
            
        case .info:
            let cell = tableView.dequeueReusableCell(withIdentifier: "DetailInfoTableCell", for: indexPath) as! DetailInfoTableCell
            cell.delegate = self
            cell.configure(with: detailData)
            return cell
            
        case .productlink:
            let cell = tableView.dequeueReusableCell(withIdentifier: "DetailProductLinkTableCell", for: indexPath) as! DetailProductLinkTableCell
            if let product = detailData.product {
                cell.configure(with: product)
            }
            cell.delegate = self
            return cell
            
        case .recommend:
            let cell = tableView.dequeueReusableCell(withIdentifier: "DetailRecommendTableCell", for: indexPath) as! DetailRecommendTableCell
            cell.delegate = self
            let limitedSkuList = Array(detailData.skuList.prefix(4))
            cell.configure(with: limitedSkuList, hideMoreButton: false)
            return cell
            
        case .commentTitle:
            let cell = tableView.dequeueReusableCell(withIdentifier: "DetailCommentTitleTableCell", for: indexPath) as! DetailCommentTitleTableCell
            cell.moreButton.isHidden = true
            cell.configure(with: detailData.commentCount)
            return cell
            
        case .comments:
            let cell = tableView.dequeueReusableCell(withIdentifier: "CommentItemTableCell", for: indexPath) as! CommentItemTableCell
            let comment = viewModel.commentList[indexPath.row]
            cell.configure(with: comment)
            cell.delegate = self
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 0.01
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 0.01
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        return UIView()
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return UIView()
    }
}

// MARK: - DetailBottomToolBarDelegate

extension HomeDetailController: DetailBottomToolBarDelegate {
    
    func toolBar(_ toolBar: DetailBottomToolBar, didClickComment text: String) {
        viewModel.postComment(content: text)
    }
    
    func toolBar(_ toolBar: DetailBottomToolBar, didClickLike isLiked: Bool) {
        viewModel.toggleLike()
    }
    
    func toolBar(_ toolBar: DetailBottomToolBar, didClickFavorite isFavorited: Bool) {
        viewModel.toggleFavorite()
    }
    func toolBar(_ toolBar: DetailBottomToolBar, didClickShare sender: UIButton) {
    }
}
extension HomeDetailController:DetailCellDelegate{
    
    func jumpButtonDidClick() {
        if let url = URL(string: viewModel.detailData?.product?.linkUrl) {
            UIApplication.shared.open(url, options: [.universalLinksOnly: false]) { success in
                if !success {
                    print("Universal Link 未注册，或 URL 无效")
                }
            }
        }
    }
    
    func likeButtonDidClick(model: CommentModel) {
        viewModel.commentLike(commentId: model.id)
    }
    
}
extension HomeDetailController:DetailNavBarViewDelegate{
    func navBarViewDidTapBack(_ navBarView: DetailNavBarView) {
        self.navigationController?.popViewController(animated: true)
    }
    
    func navBarViewDidTapUser(_ navBarView: DetailNavBarView) {
        
    }
    
    func navBarViewDidTapChat(_ navBarView: DetailNavBarView) {
        
    }
    
    func navBarViewDidTapFollow(_ navBarView: DetailNavBarView) {
        guard let detail = viewModel.detailData else { return }
        self.viewModel.toggleFollow(userId: detail.user_id, isFollow: detail.is_following)
    }
    
    func navBarViewDidTapMore(_ navBarView: DetailNavBarView) {
        
    }

}
extension HomeDetailController:DetailInfoTableCellDelegate{
    func buyButtonClick() {
        let vc = ProductOrderConfirmation​Controller()
        vc.detailModel = self.viewModel.detailData
        pushVc(vc, animated: true)
    }
}
extension HomeDetailController:DetailRecommendTableCellDelegate{
    func moreButtonClick() {
        guard let model = self.viewModel.detailData else {return}
        let vc = ProductRecommendListController(adId: model.id, productId: "")
        pushVc(vc, animated: true)
    }
    
    
}
