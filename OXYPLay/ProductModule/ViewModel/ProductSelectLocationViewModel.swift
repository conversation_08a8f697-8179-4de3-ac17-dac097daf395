//
//  ProductSelectLocationViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/17.
//

import Foundation
import Combine

class ProductSelectLocationViewModel: BaseViewModel {

    // MARK: - Published Properties

    /// 地区列表数据
    @Published var regionList: [RegionListItemModel] = []

    /// 当前选中的省份
    @Published var selectedProvince: RegionListItemModel?

    /// 当前选中的城市
    @Published var selectedCity: RegionListItemModel?

    /// 当前选中的区县
    @Published var selectedDistrict: RegionListItemModel?

    // MARK: - Initialization

    override init() {
        super.init()
    }

    // MARK: - Public Methods

    /// 获取地区列表
    /// - Parameter parent_code: 父级地区代码，nil表示获取省份列表
    func fetchRegionList(parent_code: String?) {
        var params = RequestParameters(["": ""])
        if let parent_code = parent_code {
            params = RequestParameters([
                "parent_code": parent_code,
            ])
        }

        requestModel(CommentService.regionList(params: params), type: [RegionListItemModel].self)
            .sink { [weak self] completion in
                switch completion {
                case .finished:
                    break
                case .failure(let error):
                    self?.regionList = []
                }
            } receiveValue: { [weak self] models in
                self?.regionList = models
            }
            .store(in: &cancellables)
    }

    /// 设置选中的省份
    func setSelectedProvince(_ province: RegionListItemModel) {
        selectedProvince = province
        selectedCity = nil
        selectedDistrict = nil
    }

    /// 设置选中的城市
    func setSelectedCity(_ city: RegionListItemModel) {
        selectedCity = city
        selectedDistrict = nil
    }

    /// 设置选中的区县
    func setSelectedDistrict(_ district: RegionListItemModel) {
        selectedDistrict = district
    }

    /// 获取完整的地址字符串
    func getFullAddress() -> String {
        var address = ""

        if let province = selectedProvince {
            address += province.name
        }

        if let city = selectedCity, city.name != selectedProvince?.name {
            address += city.name
        }

        if let district = selectedDistrict {
            address += district.name
        }

        return address
    }

    /// 重置选择状态
    func resetSelection() {
        selectedProvince = nil
        selectedCity = nil
        selectedDistrict = nil
        regionList = []
    }
}
