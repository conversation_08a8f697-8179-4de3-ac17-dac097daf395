//
//  ProductOrderConfirmation​ViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/18.
//

class ProductOrderConfirmation​ViewModel: BaseViewModel {

    // MARK: - Published Properties

    /// 优惠卷列表数据
    @Published var couponModel: CouponModel?
    @Published var productAddressModel: ProductAddressModel?

    /// 订单创建结果
    @Published var createOrderResult: CreateOrderResponse?

    /// 支付响应结果
    @Published var paymentResult: PaymentResponse?

    /// 支付成功状态
    @Published var paymentSuccess: Bool = false

    /// 支付流程状态
    @Published var paymentFlowState: PaymentFlowState = .idle

    /// 支付错误信息
    @Published var paymentError: String?

    /// 支付方式选择状态
    @Published var selectedPaymentMethod: String = PaymentMethodIdentifiers.alipay // 默认支付宝支付

    /// 余额支付金额
    @Published var walletAmount: Float = 0.0

    /// 第三方支付金额
    @Published var thirdPartyAmount: Float = 0.0

    /// 钱包概览数据
    @Published var walletSummary: WalletSummaryModel?
    /// 组合支付时三方的支付方式

    private var combineThirdPartyPaymentMethod = ""
    // MARK: - Initialization

    override init() {
        super.init()
    }

    // MARK: - Public Methods

    /// 获取优惠卷列表
    func fetchCouponList(advertiser_id: String,order_amount: String) {
        var params = RequestParameters(["": ""])
            params = RequestParameters([
                "advertiser_id": advertiser_id,
                "order_amount": order_amount,
            ])
        requestModel(MineService.couponList(params: params), type:CouponModel.self)
            .sink { [weak self] completion in

            } receiveValue: { [weak self] models in
                // 设置默认选择后再赋值
                var mutableModels = models
                let processedModels = mutableModels.setDefaultSelection()
                self?.couponModel = processedModels
            }
            .store(in: &cancellables)
    }
    
    /// 获取位置列表
    func fetchLocationList() {
        var params = RequestParameters(["": ""])
            params = RequestParameters([
                "type": "1",
            ])
        requestModel(MineService.addressList(params: params), type:ProductAddressModel.self)
            .sink { [weak self] completion in
            } receiveValue: { [weak self] models in
                // 只在没有选中状态时设置默认选择
                var mutableModels = models
                let processedModels = mutableModels.setDefaultSelectionIfNeeded()
                self?.productAddressModel = processedModels
            }
            .store(in: &cancellables)
    }
    /// 获取钱包概览（余额 + 优惠券）
    func fetchWalletSummary() {
        let request = WalletSummaryRequest()
        
        requestModel(WalletService.summary(params: request), type: WalletSummaryModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                },
                receiveValue: { [weak self] summary in
                    self?.walletSummary = summary
                }
            )
            .store(in: &cancellables)
    }

    /// 设置默认收货地址
    /// - Parameter addressId: 地址ID
    func setDefaultAddress(addressId: String, completion: @escaping (Bool) -> Void) {
        let params = RequestParameters([
            "address_id": addressId,
            "type": "1"
        ])

        request(MineService.setDefaultAddress(params: params))
            .sink { completionResult in
                switch completionResult {
                case .failure:
                    completion(false)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                if response.code == 200 {
                    // 设置成功后重新获取地址列表
                    self?.fetchLocationList()
                    completion(true)
                } else {
                    completion(false)
                }
            }
            .store(in: &cancellables)
    }

    /// 创建新地址
    /// - Parameters:
    ///   - addressData: 地址数据字典
    ///   - completion: 完成回调
    func createAddress(addressData: [String: Any], completion: @escaping (Bool) -> Void) {
        let params = RequestParameters(addressData)

        requestModel(MineService.createAddress(params: params), type: CreateAddressResponse.self)
            .sink { completionResult in
                switch completionResult {
                case .failure:
                    completion(false)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                // 创建成功后重新获取地址列表
                self?.fetchLocationList()
                completion(true)
            }
            .store(in: &cancellables)
    }

    /// 更新地址
    /// - Parameters:
    ///   - addressData: 地址数据字典
    ///   - completion: 完成回调
    func updateAddress(addressData: [String: Any], completion: @escaping (Bool) -> Void) {
        let params = RequestParameters(addressData)

        request(MineService.updateAddress(params: params))
            .sink { completionResult in
                switch completionResult {
                case .failure:
                    completion(false)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                if response.code == 200 {
                    // 更新成功后重新获取地址列表
                    self?.fetchLocationList()
                    completion(true)
                } else {
                    completion(false)
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 订单创建方法

    /// 创建服务订单
    /// - Parameters:
    ///   - serviceModel: 服务帖详情模型
    ///   - completion: 完成回调
    func createServiceOrder(serviceModel: HomeDetailModel, completion: @escaping (Bool, String) -> Void) {
        // 验证必要参数
        guard let selectedAddress = productAddressModel?.selectedAddress() else {
            completion(false, "请选择收货地址")
            return
        }

        // 计算支付金额
        let servicePrice = Float(serviceModel.price) ?? 0.0
        let discountAmount = Float(couponModel?.getCurrentSelectedPrice() ?? "0") ?? 0.0
        let finalAmount = servicePrice - discountAmount

        // 根据支付方式设置支付金额
        var walletPayAmount: Float = 0.0
        var thirdPartyPayAmount: Float = 0.0

        switch selectedPaymentMethod {
        case PaymentMethodIdentifiers.balance:
            walletPayAmount = finalAmount
        case PaymentMethodIdentifiers.combine:
            walletPayAmount = walletAmount
            thirdPartyPayAmount = thirdPartyAmount
        default: // 支付宝或微信
            thirdPartyPayAmount = finalAmount
        }

        // 验证支付金额
        if abs((walletPayAmount + thirdPartyPayAmount) - finalAmount) > 0.01 {
            completion(false, "支付金额不一致")
            return
        }

        // 构建请求参数
        var params: [String: Any] = [
            "service_id": serviceModel.id,
            "service_type": serviceModel.type,
            "quantity": 1,
            "address_id": selectedAddress.id,
            "wallet_amount": walletPayAmount,
            "third_party_amount": thirdPartyPayAmount,
            "payment_type": getPaymentType()
        ]

        // 添加优惠券参数
        if let selectedCoupon = couponModel?.getCurrentSelectedCoupon() {
            params["coupon_id"] = selectedCoupon.id
            params["discount_amount"] = discountAmount
        }

        let requestParams = RequestParameters(params)

        requestModel(OrderService.createService(params: requestParams), type: CreateOrderResponse.self)
            .sink { completionResult in
                switch completionResult {
                case .failure(let error):
                    completion(false, error.localizedDescription)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                self?.createOrderResult = response
                completion(true, "服务下单成功")
            }
            .store(in: &cancellables)
    }

    /// 立即购买商品
    /// - Parameters:
    ///   - productModel: 商品详情模型
    ///   - completion: 完成回调
    func buyNowProduct(productModel: ProductDetailModel, completion: @escaping (Bool, String) -> Void) {
        // 验证必要参数
        guard let selectedAddress = productAddressModel?.selectedAddress() else {
            completion(false, "请选择收货地址")
            return
        }

        // 获取当前选中的SKU信息
        guard let selectedSku = productModel.getSelectedSku() else {
            completion(false, "请选择商品规格")
            return
        }

        // 计算支付金额
        let productPrice = Float(productModel.getcurrentQuantityPrice()) ?? 0.0
        let discountAmount = Float(couponModel?.getCurrentSelectedPrice() ?? "0") ?? 0.0
        let finalAmount = productPrice - discountAmount

        // 根据支付方式设置支付金额
        var walletPayAmount: Float = 0.0
        var thirdPartyPayAmount: Float = 0.0

        switch selectedPaymentMethod {
        case PaymentMethodIdentifiers.balance:
            walletPayAmount = finalAmount
        case PaymentMethodIdentifiers.combine:
            walletPayAmount = walletAmount
            thirdPartyPayAmount = thirdPartyAmount
        default: // 支付宝或微信
            thirdPartyPayAmount = finalAmount
        }

        // 验证支付金额
        if abs((walletPayAmount + thirdPartyPayAmount) - finalAmount) > 0.01 {
            completion(false, "支付金额不一致")
            return
        }

        // 构建请求参数
        var params: [String: Any] = [
            "product_id": productModel.product.id,
            "sku_id": selectedSku.sku_id,
            "quantity": productModel.currentQuantity,
            "address_id": selectedAddress.id,
            "wallet_amount": walletPayAmount,
            "third_party_amount": thirdPartyPayAmount,
            "payment_type": getPaymentType()
        ]

        // 添加优惠券参数
        if let selectedCoupon = couponModel?.getCurrentSelectedCoupon() {
            params["coupon_id"] = selectedCoupon.id
            params["discount_amount"] = discountAmount
        }

        let requestParams = RequestParameters(params)

        requestModel(OrderService.buyNow(params: requestParams), type: CreateOrderResponse.self)
            .sink { completionResult in
                switch completionResult {
                case .failure(let error):
                    completion(false, error.localizedDescription)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                self?.createOrderResult = response
                completion(true, "下单成功")
            }
            .store(in: &cancellables)
    }

    // MARK: - 辅助方法

    /// 根据选择的支付方式获取支付类型字符串
    private func getPaymentType() -> String {
        switch selectedPaymentMethod {
        case PaymentMethodIdentifiers.balance:
            return "internal_wallet"
        case PaymentMethodIdentifiers.alipay:
            return "alipay"
        case PaymentMethodIdentifiers.wechat:
            return "wechat"
        case PaymentMethodIdentifiers.combine:
            return combineThirdPartyPaymentMethod
        default:
            return "internal_wallet"
        }
    }

    /// 根据余额和订单金额选择默认支付方式
    /// - Parameter orderAmount: 订单金额
    /// - Returns: 默认支付方式标识符
    func selectDefaultPaymentMethod(orderAmount: Float) -> String {
        guard let availableBalance = walletSummary?.available_amount else {
            // 如果没有余额信息，默认选择支付宝
            return PaymentMethodIdentifiers.alipay
        }

        if availableBalance >= orderAmount {
            // 余额充足，默认选择余额支付
            return PaymentMethodIdentifiers.balance
        } else {
            // 余额不足，默认选择支付宝支付
            return PaymentMethodIdentifiers.alipay
        }
    }

    /// 检查余额是否足够支付订单
    /// - Parameter orderAmount: 订单金额
    /// - Returns: 余额是否充足
    func isBalanceSufficient(orderAmount: Float) -> Bool {
        guard let availableBalance = walletSummary?.available_amount else {
            return false
        }
        return availableBalance >= orderAmount
    }

    /// 更新支付方式选择
    /// - Parameter paymentMethod: 支付方式标识
    func updatePaymentMethod(_ paymentMethod: String) {
        selectedPaymentMethod = paymentMethod
    }
    /// 更新组合支付方式三方选择
    /// - Parameter paymentMethod: 支付方式标识
    func updateCombineThirdPartyPaymentMethod(_ paymentMethod: String) {
        combineThirdPartyPaymentMethod = paymentMethod
    }
    /// 更新支付金额
    /// - Parameters:
    ///   - walletAmount: 余额支付金额
    ///   - thirdPartyAmount: 第三方支付金额
    func updatePaymentAmounts(walletAmount: Float, thirdPartyAmount: Float) {
        self.walletAmount = walletAmount
        self.thirdPartyAmount = thirdPartyAmount
    }

    // MARK: - 支付相关方法

    /// 发起支付请求
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - completion: 完成回调
    func initiatePayment(orderId: Int, completion: @escaping (Bool, String) -> Void) {
        // 根据选择的支付方式确定pay_method参数
        let payMethod = getPayMethodString()

        let paymentRequest = PaymentRequest(order_id: orderId, pay_method: payMethod)

        requestModel(OrderService.pay(params: paymentRequest), type: PaymentResponse.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completionResult in
                    switch completionResult {
                    case .failure(let error):
                        completion(false, error.localizedDescription)
                    case .finished:
                        break
                    }
                },
                receiveValue: { [weak self] response in
                    self?.paymentResult = response
                    completion(true, "支付参数获取成功")
                }
            )
            .store(in: &cancellables)
    }

    /// 通知服务器支付成功
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - tradeNo: 第三方支付交易号（可选）
    ///   - completion: 完成回调
    func notifyPaymentSuccess(orderId: Int, tradeNo: String? = nil, completion: @escaping (Bool, String) -> Void) {
        let payMethod = getPayMethodString()

        let notifyRequest = PaymentNotifyRequest(
            order_id: orderId,
            pay_method: payMethod,
            trade_no: tradeNo
        )

        request(OrderService.paymentNotify(params: notifyRequest))
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completionResult in
                    switch completionResult {
                    case .failure(let error):
                        completion(false, error.localizedDescription)
                    case .finished:
                        break
                    }
                },
                receiveValue: { [weak self] response in
                    if response.code == 200 {
                        self?.paymentSuccess = true
                        completion(true, "支付成功")
                    } else {
                        completion(false, response.message)
                    }
                }
            )
            .store(in: &cancellables)
    }

    /// 获取支付方式字符串（用于API调用）
    private func getPayMethodString() -> String {
        switch selectedPaymentMethod {
        case PaymentMethodIdentifiers.balance:
            return "wallet"
        case PaymentMethodIdentifiers.alipay:
            return "alipay"
        case PaymentMethodIdentifiers.wechat:
            return "wechat"
        case PaymentMethodIdentifiers.combine:
            return "mix"
        default:
            return "wallet"
        }
    }
}

// MARK: - 支付流程处理扩展
extension ProductOrderConfirmation​ViewModel {

    /// 开始完整的支付流程
    /// - Parameters:
    ///   - serviceModel: 服务模型（可选）
    ///   - productModel: 商品模型（可选）
    func startPaymentFlow(serviceModel: HomeDetailModel? = nil, productModel: ProductDetailModel? = nil) {
        // 验证必要信息
        guard productAddressModel?.selectedAddress() != nil else {
            paymentFlowState = .paymentFailed(error: "请选择收货地址")
            return
        }

        // 开始创建订单
        paymentFlowState = .creatingOrder

        if let serviceModel = serviceModel {
            createServiceOrderForPayment(serviceModel: serviceModel)
        } else if let productModel = productModel {
            buyNowProductForPayment(productModel: productModel)
        } else {
            paymentFlowState = .paymentFailed(error: "订单信息错误")
        }
    }

    /// 创建服务订单（用于支付流程）
    private func createServiceOrderForPayment(serviceModel: HomeDetailModel) {
        createServiceOrder(serviceModel: serviceModel) { [weak self] success, message in
            DispatchQueue.main.async {
                if success {
                    guard let orderId = self?.createOrderResult?.order_id, orderId > 0 else {
                        self?.paymentFlowState = .paymentFailed(error: "订单创建失败")
                        return
                    }
                    self?.paymentFlowState = .orderCreated(orderId: orderId)
                    self?.handleOrderCreatedInPaymentFlow(orderId: orderId)
                } else {
                    self?.paymentFlowState = .paymentFailed(error: message)
                }
            }
        }
    }

    /// 立即购买商品（用于支付流程）
    private func buyNowProductForPayment(productModel: ProductDetailModel) {
        buyNowProduct(productModel: productModel) { [weak self] success, message in
            DispatchQueue.main.async {
                if success {
                    guard let orderId = self?.createOrderResult?.order_id, orderId > 0 else {
                        self?.paymentFlowState = .paymentFailed(error: "订单创建失败")
                        return
                    }
                    self?.paymentFlowState = .orderCreated(orderId: orderId)
                    self?.handleOrderCreatedInPaymentFlow(orderId: orderId)
                } else {
                    self?.paymentFlowState = .paymentFailed(error: message)
                }
            }
        }
    }

    /// 处理订单创建成功后的支付流程
    private func handleOrderCreatedInPaymentFlow(orderId: Int) {
        let paymentMethod = selectedPaymentMethod

        // 如果是余额支付，直接通知支付成功
        if paymentMethod == PaymentMethodIdentifiers.balance {
            handleBalancePayment(orderId: orderId)
        } else {
            // 第三方支付，先获取支付参数
            handleThirdPartyPayment(orderId: orderId)
        }
    }

    /// 处理余额支付
    private func handleBalancePayment(orderId: Int) {
        paymentFlowState = .notifyingPaymentSuccess

        notifyPaymentSuccess(orderId: orderId) { [weak self] success, message in
            DispatchQueue.main.async {
                if success {
                    self?.paymentFlowState = .paymentCompleted(orderId: orderId)
                } else {
                    self?.paymentFlowState = .paymentFailed(error: message)
                }
            }
        }
    }

    /// 处理第三方支付
    private func handleThirdPartyPayment(orderId: Int) {
        paymentFlowState = .initiatingPayment

        initiatePayment(orderId: orderId) { [weak self] success, message in
            DispatchQueue.main.async {
                if success {
                    self?.paymentFlowState = .waitingThirdPartyPayment
                    // 支付参数获取成功，等待Controller处理第三方支付
                } else {
                    self?.paymentFlowState = .paymentFailed(error: message)
                }
            }
        }
    }

    /// 处理第三方支付结果
    /// - Parameters:
    ///   - result: 支付结果
    ///   - orderId: 订单ID
    func handleThirdPartyPaymentResult(_ result: PaymentResult, orderId: Int) {
        switch result {
        case .success(let tradeNo):
            paymentFlowState = .notifyingPaymentSuccess

            notifyPaymentSuccess(orderId: orderId, tradeNo: tradeNo) { [weak self] success, message in
                DispatchQueue.main.async {
                    if success {
                        self?.paymentFlowState = .paymentCompleted(orderId: orderId)
                    } else {
                        self?.paymentFlowState = .paymentFailed(error: message)
                    }
                }
            }

        case .failure(let error):
            paymentFlowState = .paymentFailed(error: "支付失败: \(error)")

        case .cancelled:
            paymentFlowState = .paymentFailed(error: "支付已取消")
        }
    }

    /// 重置支付流程状态
    func resetPaymentFlow() {
        paymentFlowState = .idle
        paymentError = nil
        paymentSuccess = false
        paymentResult = nil
        createOrderResult = nil
    }
}

// MARK: - 第三方支付处理扩展
extension ProductOrderConfirmation​ViewModel {

    /// 处理支付宝支付
    /// - Parameter orderId: 订单ID
    func handleAlipayPayment(orderId: Int) {
        guard let result = paymentResult,
              let signedString = result.alipay_signed_string else {
            paymentFlowState = .paymentFailed(error: "支付参数错误")
            return
        }

        // 调用支付宝支付
        PaymentManager.shared.startAlipayPayment(signedString: signedString) { [weak self] paymentResult in
            DispatchQueue.main.async {
                self?.handleThirdPartyPaymentResult(paymentResult, orderId: orderId)
            }
        }
    }

    /// 处理微信支付
    /// - Parameter orderId: 订单ID
    func handleWechatPayment(orderId: Int) {
        guard let result = paymentResult,
              let payParams = result.wechat_pay_params else {
            paymentFlowState = .paymentFailed(error: "支付参数错误")
            return
        }

        // 调用微信支付
        PaymentManager.shared.startWechatPayment(payParams: payParams) { [weak self] paymentResult in
            DispatchQueue.main.async {
                self?.handleThirdPartyPaymentResult(paymentResult, orderId: orderId)
            }
        }
    }

    /// 处理组合支付
    /// - Parameter orderId: 订单ID
    func handleCombinePayment(orderId: Int) {
        // 组合支付根据第三方支付方式选择对应的处理方法
        switch combineThirdPartyPaymentMethod {
        case "alipay":
            handleAlipayPayment(orderId: orderId)
        case "wechat":
            handleWechatPayment(orderId: orderId)
        default:
            handleAlipayPayment(orderId: orderId) // 默认使用支付宝
        }
    }

    /// 根据支付方式处理第三方支付
    /// - Parameter orderId: 订单ID
    func processThirdPartyPayment(orderId: Int) {
        switch selectedPaymentMethod {
        case PaymentMethodIdentifiers.alipay:
            handleAlipayPayment(orderId: orderId)
        case PaymentMethodIdentifiers.wechat:
            handleWechatPayment(orderId: orderId)
        case PaymentMethodIdentifiers.combine:
            handleCombinePayment(orderId: orderId)
        default:
            paymentFlowState = .paymentFailed(error: "不支持的支付方式")
        }
    }
}
