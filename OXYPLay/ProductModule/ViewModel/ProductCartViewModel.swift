//
//  ProductCartViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/19.
//

import Foundation
import Combine

/// 购物车ViewModel
/// 负责购物车数据的获取、管理和业务逻辑处理
class ProductCartViewModel: BaseViewModel {

    // MARK: - 发布属性

    /// 购物车数据
    @Published var cartData: CartListResponse = CartListResponse()

    /// 是否全选
    @Published var isAllSelected: Bool = false

    /// 选中商品总价（含邮费）
    @Published var totalPrice: Float = 0.0

    /// 选中商品商品价格（不含邮费）
    @Published var totalProductPrice: Float = 0.0

    /// 选中商品总邮费
    @Published var totalPostageFee: Float = 0.0

    /// 选中商品数量
    @Published var selectedCount: Int = 0

    /// 是否显示空状态
    @Published var isEmpty: Bool = false

    // MARK: - 初始化

    override init() {
        super.init()
        setupCartBindings()
    }

    // MARK: - 绑定设置

    override func setupBindings() {
        super.setupBindings()
        setupCartBindings()
    }

    /// 设置购物车相关的数据绑定
    private func setupCartBindings() {
        // 监听购物车数据变化，更新统计信息
        $cartData
            .sink { [weak self] cartData in
                self?.updateStatistics(cartData)
            }
            .store(in: &cancellables)

        // 监听全选状态变化
        $isAllSelected
            .sink { [weak self] isAllSelected in
                self?.handleAllSelectionChange(isAllSelected)
            }
            .store(in: &cancellables)
    }

    // MARK: - 数据加载

    /// 刷新数据
    override func refreshData() {
        refreshState = .headerRefreshing
        currentPage = 1
        hasMoreData = false // 购物车通常不需要分页
        loadCartList()
    }

    /// 加载更多数据（购物车通常不需要分页，这里保持接口一致性）
    override func loadMoreData() {
        // 购物车列表通常不需要分页加载
        refreshState = .noMoreData
    }

    /// 加载购物车列表
    private func loadCartList() {
        let params = EmptyRequest()

        requestModels(CartService.list(params: params), type: [CartSeller].self)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    if case .failure(let error) = completion {
                        print("购物车列表加载失败: \(error)")
                        self.refreshState = .refreshFailure(error.localizedDescription)
                    }
                },
                receiveValue: { [weak self] sellers in
                    guard let self = self else { return }
                 
                        let response = CartListResponse()
                        response.data = sellers
                        self.cartData = response
                        self.isEmpty = false
                    self.refreshState = .refreshSuccess
                }
            )
            .store(in: &cancellables)
    }


    // MARK: - 业务逻辑

    /// 更新统计信息
    private func updateStatistics(_ cartData: CartListResponse) {
        totalProductPrice = cartData.totalSelectedPrice
        totalPostageFee = cartData.totalSelectedPostageFee
        totalPrice = cartData.totalSelectedPriceWithPostage
        selectedCount = cartData.totalSelectedCount
        isAllSelected = cartData.isAllSelected
        isEmpty = cartData.data.isEmpty
    }

    /// 处理全选状态变化
    private func handleAllSelectionChange(_ isAllSelected: Bool) {
        // 避免循环调用
        if cartData.isAllSelected != isAllSelected {
            cartData.setAllSelected(isAllSelected)
            // 触发数据更新
            cartData = cartData
        }
    }

    /// 切换商品选中状态
    func toggleItemSelection(sellerId: String, cartId: String) {
        guard let sellerIndex = cartData.data.firstIndex(where: { $0.seller_id == sellerId }),
              let itemIndex = cartData.data[sellerIndex].items.firstIndex(where: { $0.cart_id == cartId }) else {
            return
        }

        // 切换商品选中状态
        cartData.data[sellerIndex].items[itemIndex].isSelected.toggle()

        // 更新卖家选中状态
        cartData.data[sellerIndex].updateSelectionState()

        // 触发数据更新
        cartData = cartData
    }

    /// 切换卖家选中状态
    func toggleSellerSelection(sellerId: String) {
        guard let sellerIndex = cartData.data.firstIndex(where: { $0.seller_id == sellerId }) else {
            return
        }

        let newState = !cartData.data[sellerIndex].isSelected
        cartData.data[sellerIndex].setAllItemsSelected(newState)

        // 触发数据更新
        cartData = cartData
    }

    /// 更新商品数量
    func updateItemQuantity(cartId: String, quantity: Int) {
    
        // 真实接口调用
        guard let cartIdInt = Int(cartId) else { return }

        let params = CartUpdateQuantityRequest(id: cartIdInt, quantity: quantity)

        request(CartService.updateQuantity(params: params))
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("更新商品数量失败: \(error)")
                    }
                },
                receiveValue: { [weak self] response in
                    if response.isSuccess {
                        // 成功后刷新列表，保持选中状态
                        self?.refreshCartListKeepingSelection()
                    }
                }
            )
            .store(in: &cancellables)
    }

    /// 确保商品被选中
    func ensureItemSelected(cartId: String) {
        for sellerIndex in cartData.data.indices {
            if let itemIndex = cartData.data[sellerIndex].items.firstIndex(where: { $0.cart_id == cartId }) {
                if !cartData.data[sellerIndex].items[itemIndex].isSelected {
                    cartData.data[sellerIndex].items[itemIndex].isSelected = true
                    cartData.data[sellerIndex].updateSelectionState()
                    // 触发数据更新
                    cartData = cartData
                }
                break
            }
        }
    }

    /// 更新本地商品数量
    private func updateLocalItemQuantity(cartId: String, quantity: Int) {
        for sellerIndex in cartData.data.indices {
            if let itemIndex = cartData.data[sellerIndex].items.firstIndex(where: { $0.cart_id == cartId }) {
                cartData.data[sellerIndex].items[itemIndex].quantity = quantity
                // 触发数据更新
                cartData = cartData
                break
            }
        }
    }

   

    /// 删除单个商品
    func removeSingleItem(cartId: String) {
        // 调用接口删除，传单个id的数组
        guard let cartIdInt = Int(cartId) else { return }
        let params = CartRemoveRequest(ids: [cartIdInt])

        request(CartService.remove(params: params))
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("删除商品失败: \(error)")
                    }
                },
                receiveValue: { [weak self] response in
                    if response.isSuccess {
                        // 成功后刷新列表，保持选中状态
                        self?.refreshCartListKeepingSelection()
                    }
                }
            )
            .store(in: &cancellables)
    }

    /// 清空购物车
    func clearCart() {

        // 真实接口调用
        let params = EmptyRequest()

        request(CartService.clear(params: params))
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("清空购物车失败: \(error)")
                    }
                },
                receiveValue: { [weak self] response in
                    if response.isSuccess {
                        self?.cartData.data.removeAll()
                        self?.cartData = self?.cartData ?? CartListResponse()
                    }
                }
            )
            .store(in: &cancellables)
    }


    // MARK: - 私有方法

    /// 刷新购物车列表并保持选中状态
    private func refreshCartListKeepingSelection() {
        // 保存当前选中的商品ID
        let selectedCartIds = cartData.selectedCartIds

        // 刷新列表
        let params = EmptyRequest()
        requestModels(CartService.list(params: params), type: [CartSeller].self)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }
                    if case .failure(let error) = completion {
                        print("购物车列表加载失败: \(error)")
                        self.refreshState = .refreshFailure(error.localizedDescription)
                    }
                },
                receiveValue: { [weak self] sellers in
                    guard let self = self else { return }
                    let response = CartListResponse()
                    response.data = sellers
                    self.cartData = response
                    self.isEmpty = false
                    self.refreshState = .refreshSuccess

                    // 恢复选中状态
                    self.restoreSelectionState(selectedCartIds: selectedCartIds)
                }
            )
            .store(in: &cancellables)
    }

    /// 恢复选中状态
    private func restoreSelectionState(selectedCartIds: [String]) {
        for sellerIndex in cartData.data.indices {
            for itemIndex in cartData.data[sellerIndex].items.indices {
                let cartId = cartData.data[sellerIndex].items[itemIndex].cart_id
                if selectedCartIds.contains(cartId) {
                    cartData.data[sellerIndex].items[itemIndex].isSelected = true
                }
            }
            // 更新卖家选中状态
            cartData.data[sellerIndex].updateSelectionState()
        }
        // 触发数据更新
        cartData = cartData
    }
}
