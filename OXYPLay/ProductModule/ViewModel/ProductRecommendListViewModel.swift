//
//  ProductRecommendListViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/23.
//

import Foundation
import Combine
import UIKit

/// 商品推荐列表ViewModel
class ProductRecommendListViewModel: BaseViewModel {
    
    // MARK: - 输入参数
    
    /// 广告ID
    var adId: String = ""
    
    /// 商品ID
    var productId: String = ""
    
    // MARK: - 输出数据

    /// 商品推荐列表
    @Published var productList: [ProductRecommendItem] = []
    
    // MARK: - 初始化
    
    /// 初始化方法
    /// - Parameters:
    ///   - adId: 广告ID
    ///   - productId: 商品ID
    init(adId: String, productId: String) {
        self.adId = adId
        self.productId = productId
        super.init()
    }
    
    // MARK: - 网络请求方法

    /// 加载推荐商品列表（首次加载）
    func loadRecommendList() {
        fetchRecommendListData(refresh: true)
    }

    /// 获取推荐商品列表数据
    /// - Parameter refresh: 是否为刷新操作
    func fetchRecommendListData(refresh: Bool = false) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

        // 构建请求参数
        let params = ProductRecommendListParams(
            ad_id: adId,
            product_id: productId,
            limit: pageSize,
            page: currentPage
        )

        let service = HomeService.recommendGoods(params: params)

        // 使用便捷的分页数据请求方法
        requestPageData(service, type: ProductRecommendItem.self, isRefresh: refresh) { [weak self] newData, isRefresh in
            guard let self = self else { return }

            if isRefresh {
                self.productList = newData
            } else {
                self.productList.append(contentsOf: newData)
            }
        }
        .sink(
            receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    print("获取推荐商品失败: \(error.localizedDescription)")
                }
            },
            receiveValue: { [weak self] pageResponse in
                guard let self = self else { return }
                print("推荐商品加载成功，共\(pageResponse.list.count)条数据")
            }
        )
        .store(in: &cancellables)
    }
    /// 下拉刷新回调
    override func refreshData() {
        fetchRecommendListData(refresh: true)
    }

    /// 上拉加载回调
    override func loadMoreData() {
        fetchRecommendListData(refresh: false)
    }
}


// MARK: - 瀑布流布局支持

extension ProductRecommendListViewModel {
    
    /// 计算商品项的高度
    /// - Parameters:
    ///   - item: 商品项
    ///   - itemWidth: 项宽度
    /// - Returns: 项高度
    func calculateItemHeight(for item: ProductRecommendItem, itemWidth: CGFloat) -> CGFloat {
        // 图片高度固定为128
        let imageHeight: CGFloat = 128

        // 标题高度计算（最多2行）
        let titleFont = UIFont.systemFont(ofSize: 13, weight: .regular)
        let titleHeight = item.name.heightForLabel(
            font: titleFont,
            width: itemWidth,
            maxLines: 2
        )

        // 价格和浏览量高度
        let priceHeight: CGFloat = 18

        // 间距：图片下方8pt + 标题下方8pt
        let spacing: CGFloat = 16

        return imageHeight + titleHeight + priceHeight + spacing
    }
}

// MARK: - String扩展支持最大行数计算

extension String {
    /// 计算字符串在指定宽度、字体和最大行数下的高度
    /// - Parameters:
    ///   - font: 字体
    ///   - width: 宽度限制
    ///   - maxLines: 最大行数
    /// - Returns: 计算后的高度
    func heightForLabel(font: UIFont, width: CGFloat, maxLines: Int) -> CGFloat {
        // 计算单行高度
        let singleLineHeight = "A".height(withConstrainedWidth: width, font: font)

        // 计算实际需要的高度
        let actualHeight = self.height(withConstrainedWidth: width, font: font)

        // 计算实际行数
        let actualLines = Int(ceil(actualHeight / singleLineHeight))

        // 返回限制行数后的高度
        let limitedLines = min(actualLines, maxLines)
        return CGFloat(limitedLines) * singleLineHeight
    }
}
