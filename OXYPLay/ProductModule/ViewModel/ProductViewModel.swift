//
//  ProductViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/14.
//
class ProductViewModel: BaseViewModel {
    
    // MARK: - 属性
    

    /// 详情数据
    @Published var productDetailData: ProductDetailModel?
    /// 评论列表
    @Published var commentList: [CommentModel] = []

    /// 购物车操作状态
    @Published var addToCartState: RequestState = .idle
    
    /// 帖子ID
    private var postId: String = ""
    ///商品所属广告主ID
    var advertiserId: String = ""
    private let pageType: Int?
    // MARK: - 初始化
    
    init(postId: String,pageType:Int?) {
        self.postId = postId
        self.pageType = pageType
        super.init()
    }
    
    // MARK: - 数据请求
    /// 获取商品详情数据
    func fetchProductDetailData() {
        var params = RequestParameters([
            "productId": postId,
            "advertiserId":advertiserId
        ])
        if let model = UserManager.shared.getCurrentUser() {
            params = RequestParameters([
                "productId": postId,
                "userId":model.id,
                "advertiserId":advertiserId
            ])
        }
        requestModel(HomeService.offdetail(params: params), type: ProductDetailModel.self)
            .sink { [weak self] completion in
            } receiveValue: { [weak self] model in
                // 使用model的setDefaultSelections方法设置默认选项
                var mutableModel = model
                let processedModel = mutableModel.setDefaultSelections()
                self?.productDetailData = processedModel
            }
            .store(in: &cancellables)
    }
    /// 获取推荐好物
    func fetchRecommendGoodsData() {
        guard let user = UserManager.shared.getCurrentUser() else { return }
        let params = RequestParameters([
            "product_id": postId,
            "limit":4
        ])
        requestModel(HomeService.recommendGoods(params: params), type: [RecommendModel].self)
            .sink { [weak self] completion in
            } receiveValue: { [weak self] model in
            }
            .store(in: &cancellables)
    }
    /// 获取商品评论列表
    func fetchProductCommentList(refresh: Bool = false) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

        // 构建请求参数
        let params = RequestParameters([
            "product_id": postId,
            "page": currentPage,
            "pageSize": pageSize,
        ])

        requestPageData(HomeService.commentList(params: params), type: CommentModel.self, isRefresh: refresh) { [weak self] newData, isRefresh in
            guard let self = self else { return }

            if isRefresh {
                self.commentList = newData
            } else {
                self.commentList.append(contentsOf: newData)
            }
        }
        .sink(
            receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    print("加载商品评论失败: \(error.localizedDescription)")
                }
            },
            receiveValue: { _ in
                print("商品评论加载成功")
            }
        )
        .store(in: &cancellables)
    }

    override func refreshData() {
        fetchProductCommentList(refresh: true)
    }

    override func loadMoreData() {
        fetchProductCommentList(refresh: false)
    }

    // MARK: - 购物车相关方法

    /// 添加商品到购物车
    func addToCart() {
        // 验证商品数据
        guard let detailData = productDetailData else {
            addToCartState = .failure("商品信息加载中，请稍后再试")
            return
        }

        // 验证是否选择了规格
        guard let selectedSku = detailData.getSelectedSku() else {
            addToCartState = .failure("请先选择商品规格")
            return
        }

        // 验证库存
        let currentStock = detailData.getCurrentStock()
        if currentStock <= 0 {
            addToCartState = .failure("商品库存不足")
            return
        }

        // 验证数量是否超过库存
        if detailData.currentQuantity > currentStock {
            addToCartState = .failure("选择数量超过库存限制")
            return
        }

        // 验证商品ID和SKU ID
        guard let productId = Int(detailData.product.id),
              let skuId = Int(selectedSku.sku_id) else {
            addToCartState = .failure("商品信息异常，请重试")
            return
        }

        // 调用添加购物车接口
        addToCartRequest(productId: productId, skuId: skuId, quantity: detailData.currentQuantity)
    }

    /// 调用添加购物车接口
    /// - Parameters:
    ///   - productId: 商品ID
    ///   - skuId: SKU ID
    ///   - quantity: 数量
    private func addToCartRequest(productId: Int, skuId: Int, quantity: Int) {
        addToCartState = .loading

        // 构建请求参数
        let params = CartAddRequest(product_id: productId, sku_id: skuId, quantity: quantity)

        request(CartService.add(params: params))
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    if case .failure(let error) = completion {
                        let errorMessage = self.handleAddToCartError(error)
                        self.addToCartState = .failure(errorMessage)
                    }
                },
                receiveValue: { [weak self] response in
                    guard let self = self else { return }

                    if response.isSuccess {
                        self.addToCartState = .success
                    } else {
                        self.addToCartState = .failure(response.message)
                    }
                }
            )
            .store(in: &cancellables)
    }

    /// 处理添加购物车错误
    /// - Parameter error: 网络错误
    /// - Returns: 错误信息
    private func handleAddToCartError(_ error: NetworkError) -> String {
        switch error {
        case .networkError(let response):
            return response.message
        case .decodingError:
            return "数据解析失败，请重试"
        case .noConnection:
            return "网络连接失败，请检查网络设置"
        case .tokenExpired:
            return "登录已过期，请重新登录"
        case .tokenError:
            return "登录状态异常，请重新登录"
        }
    }

}

