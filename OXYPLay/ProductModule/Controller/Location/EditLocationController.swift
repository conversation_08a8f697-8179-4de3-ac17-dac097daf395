//
//  EditLocationController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/17.
//
class EditLocationController: BasePresentController {

    // MARK: - Properties
    let pushtoSelectLocationPublisher = PassthroughSubject<[String:Any], Never>()
    let addressSavePublisher = PassthroughSubject<[String:Any], Never>()
    var dic = Dictionary<String, Any>()
    var isEditMode: Bool = false // 是否为编辑模式

    private lazy var locationTitleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textColor = UIColor(hexString: "#2B2C2F")
        $0.text = "地址信息"
    }
    
    private lazy var defualtButton = BaseButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.setTitle("默认收货", for: .normal)
        $0.setTitleColor(UIColor(hexString: "#2B2C2F"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)

    }
    lazy var listView = BaseListView().then{
        $0.delegate = self
    }
    // 实现CustomPresentable协议
    override var presentationHeight: CGFloat {
        return kScreenHeight - 430
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configureListItems()
        setupBindings()
    }
   
    // 设置双向绑定
   override func setupBindings() {
        // 检查多种可能的默认地址标识
        if let isDefault = self.dic["isDefualt"] as? Bool {
            self.defualtButton.isSelected = isDefault
        } else if let isDefault = self.dic["is_default"] as? Bool {
            self.defualtButton.isSelected = isDefault
        } else {
            // 默认为false
            self.defualtButton.isSelected = false
        }
        // 配置确认按钮事件
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.saveAddress()
            }
            .store(in: &cancellables)
        // 配置确认按钮事件
        defualtButton.tapPublisher
            .sink { [weak self] _ in
                self?.defualtButton.isSelected.toggle()
            }
            .store(in: &cancellables)
    }
    
    override func configUI() {
        let title = isEditMode ? "编辑收货地址" : "新增收货地址"
        configView(title: title, bottomTitle: "确认")
        contentView.addSubview(locationTitleLabel)
        contentView.addSubview(defualtButton)
        locationTitleLabel.snp.makeConstraints { make in
            make.left.top.equalTo(12)
        }
        defualtButton.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalTo(locationTitleLabel)
        }
        contentView.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.top.equalTo(locationTitleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(0)
        }
       
    }
    private func configureListItems() {
        let items: [[ListItemConfig]] = [

            [
                ListItemConfig(type: .titleInput, identifier: "联系人",data:self.dic["联系人"],placeholder: "请输入联系人姓名", title:"联系人"),
                ListItemConfig(type: .titleInput, identifier: "手机号",data:self.dic["手机号"], placeholder: "请输入收货手机号", title:"手机号"),
                ListItemConfig(type: .starselect, identifier: "所在地区",data:self.dic["所在地区"],placeholder: "请选择您的收货地址", title:"所在地区"),
                ListItemConfig(type: .titleInput, identifier: "详细地址",data:self.dic["详细地址"],placeholder: "请输入您的详细地址", title:"详细地址")
            ]
        ]
        // 设置列表项
        listView.setItems(items)
        listView.scrollView.isScrollEnabled = false
    }

    // MARK: - Public Methods

    /// 更新地区信息
    func updateLocationData(_ locationData: [String: Any]) {
        // 更新dic中的地区信息
        for (key, value) in locationData {
            dic[key] = value
        }

        // 重新配置列表项以更新UI
        configureListItems()
    }

    // MARK: - Private Methods

    private func saveAddress() {
        // 获取表单数据
        let formData = listView.getAllData()

        // 验证必填字段
        guard let recipientName = formData["联系人"] as? String, !recipientName.isEmpty,
              let phone = formData["手机号"] as? String, !phone.isEmpty,
              let region = formData["所在地区"] as? String, !region.isEmpty else {
            // 显示错误提示
            print("请填写完整的地址信息")
            return
        }

        // 验证地区相关的必传参数
        guard let province = dic["province"] as? String, !province.isEmpty,
              let provinceCode = dic["province_code"] as? String, !provinceCode.isEmpty,
              let city = dic["city"] as? String, !city.isEmpty,
              let cityCode = dic["city_code"] as? String, !cityCode.isEmpty,
              let district = dic["district"] as? String, !district.isEmpty,
              let districtCode = dic["district_code"] as? String, !districtCode.isEmpty,
              let street = dic["street"] as? String, !street.isEmpty,
              let streetCode = dic["street_code"] as? String, !streetCode.isEmpty else {
            // 显示错误提示
            print("请选择完整的地区信息")
            return
        }

        // 构建地址数据 - 包含所有必传参数
        var addressData: [String: Any] = [
            // 基本信息（可选）
            "recipient_name": recipientName,
            "phone": phone,

            // 地区信息（必传）
            "province": province,
            "province_code": provinceCode,
            "city": city,
            "city_code": cityCode,
            "district": district,
            "district_code": districtCode,
            "street": street,
            "street_code": streetCode,

            // 其他信息（可选）
            "set_default": defualtButton.isSelected
        ]

        // 添加详细地址（可选）
        if let detail = formData["详细地址"] as? String, !detail.isEmpty {
            addressData["detail"] = detail
        }

        // 如果是编辑模式，添加必传的address_id
        if isEditMode {
            guard let addressId = dic["address_id"] else {
                print("编辑模式缺少地址ID")
                return
            }
            addressData["address_id"] = addressId
        }

        // 发送保存事件
        addressSavePublisher.send(addressData)

        // 关闭页面
        dismiss(animated: true)
    }
}
extension EditLocationController: BaseListViewDelegate{
    func listViewUpdate(_ listView: BaseListView, with data: Any?) {
        
    }
    
    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        if config.identifier == "所在地区"{
            dismiss(animated: true) {
                self.dic = listView.getAllData()
                self.dic["isDefualt"] = self.defualtButton.isSelected
                self.pushtoSelectLocationPublisher.send(self.dic)
            }
        }
    }
    
    func listViewValidate(_ listView: BaseListView, message: String) {
        
    }
    
}


