//
//  ProductSelectLocationController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/17.
//

import UIKit
import Combine

class ProductSelectLocationController: BaseViewController {
    
    // MARK: - Properties
    var viewModel = ProductSelectLocationViewModel()
    
    // 当前选中的地区数据
    private var selectedProvinceIndex: Int = -1
    private var selectedCityIndex: Int = -1
    private var selectedDistrictIndex: Int = -1
    
    // 存储选中的地区对象
    private var selectedProvince: RegionListItemModel?
    private var selectedCity: RegionListItemModel?
    private var selectedDistrict: RegionListItemModel?
    private var selectedStreet: RegionListItemModel?
    
    // 索引相关数据
    private var sectionTitles: [String] = []
    private var groupedRegions: [String: [RegionListItemModel]] = [:]
    private var sortedSectionKeys: [String] = []
    
    let locationSelectedPublisher = PassthroughSubject<(RegionListItemModel, RegionListItemModel?, RegionListItemModel?, RegionListItemModel?), Never>()
    
    // MARK: - UI Components
    
    // 顶部选中区域显示
    private lazy var selectedLocationView: UIStackView = {
        let view = UIStackView()
        view.spacing = 0
        view.axis = .vertical
        return view
    }()
    
    // 省份选择项
    private lazy var provinceSelectionView: LocationSelectionItemView = {
        let view = LocationSelectionItemView()
        view.configure(title: "北京", isSelected: false, type: 0)
        view.onTap = { [weak self] in
            self?.provinceButtonTapped()
        }
        return view
    }()
    
    // 城市选择项
    private lazy var citySelectionView: LocationSelectionItemView = {
        let view = LocationSelectionItemView()
        view.configure(title: "北京市", isSelected: false, type: 1)
        view.onTap = { [weak self] in
            self?.cityButtonTapped()
        }
        return view
    }()
    
    // 区县选择项
    private lazy var districtSelectionView: LocationSelectionItemView = {
        let view = LocationSelectionItemView()
        view.configure(title: "请选择区县", isSelected: false, type: 2)
        view.onTap = { [weak self] in
            self?.districtButtonTapped()
        }
        return view
    }()

    // 街道选择项
    private lazy var streetSelectionView: LocationSelectionItemView = {
        let view = LocationSelectionItemView()
        view.configure(title: "请选择街道", isSelected: false, type: 3)
        view.bottomLine.isHidden = true
        view.onTap = { [weak self] in
            self?.streetButtonTapped()
        }
        return view
    }()
    
    
    // 省份/地区标题
    private lazy var sectionTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "选择省份/地区"
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = color_2B2C2F
        return label
    }()
    
    // 表格视图
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.backgroundColor = .white
        tv.layer.cornerRadius = 16
        tv.masksToBounds = true
        tv.separatorStyle = .none
        tv.showsVerticalScrollIndicator = false
        tv.delegate = self
        tv.dataSource = self
        tv.register(LocationTableViewCell.self, forCellReuseIdentifier: "LocationTableViewCell")
        tv.register(LocationSectionHeaderView.self, forHeaderFooterViewReuseIdentifier: "LocationSectionHeaderView")
        tv.sectionIndexBackgroundColor = .clear
        tv.sectionIndexColor = color_3D3E40
        tv.sectionIndexTrackingBackgroundColor = color_F6F8F9
        return tv
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        loadInitialData()
    }
    
    // MARK: - Setup Methods
    
    override func configUI() {
        title = "选择所在地区"
        setupSubviews()
        setupConstraints()
    }
    
    private func setupSubviews() {
        view.addSubview(selectedLocationView)
        
        // 添加选择项视图
        selectedLocationView.addArrangedSubview(provinceSelectionView)
        selectedLocationView.addArrangedSubview(citySelectionView)
        selectedLocationView.addArrangedSubview(districtSelectionView)
        selectedLocationView.addArrangedSubview(streetSelectionView)
        view.addSubview(sectionTitleLabel)
        view.addSubview(tableView)
    }
    
    private func setupConstraints() {
        selectedLocationView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
        }
        
        // 省份选择项
        provinceSelectionView.snp.makeConstraints { make in
            make.height.equalTo(38)
        }
        
        // 城市选择项（初始隐藏）
        citySelectionView.snp.makeConstraints { make in
            make.height.equalTo(38)
        }
        
        // 区县选择项（初始隐藏）
        districtSelectionView.snp.makeConstraints { make in
            make.height.equalTo(38)
        }
        streetSelectionView.snp.makeConstraints { make in
            make.height.equalTo(38)
        }
        sectionTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(selectedLocationView.snp.bottom).offset(14)
            make.left.equalTo(12)
            make.height.equalTo(16)
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(sectionTitleLabel.snp.bottom).offset(12)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalToSuperview()
        }
    }
    
   override func setupBindings() {
        // 监听地区数据变化
        viewModel.$regionList
            .receive(on: DispatchQueue.main)
            .sink { [weak self] regions in
                self?.processRegionData(regions)
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)
    }
    
    private func loadInitialData() {
        // 初始化UI状态
        provinceSelectionView.configure(title: "选择省份/地区", isSelected: false, type: 0)
        citySelectionView.isHidden = true
        districtSelectionView.isHidden = true
        streetSelectionView.isHidden = true
        // 加载省份数据
        viewModel.fetchRegionList(parent_code: nil)

    }
    
    
    // MARK: - Button Actions
    
    @objc private func provinceButtonTapped() {
        // 重新加载省份数据
        viewModel.fetchRegionList(parent_code: nil)
        sectionTitleLabel.text = "选择省份/地区"

        // 重置选择状态
        selectedCity = nil
        selectedDistrict = nil
        selectedStreet = nil

        // 重置省份选择项状态（取消选中，重置线条）
        provinceSelectionView.updateSelectionState(isSelected: false, type: 0)

        // 隐藏城市、区县和街道选择项
        citySelectionView.isHidden = true
        districtSelectionView.isHidden = true
        streetSelectionView.isHidden = true
    }
    
    @objc private func cityButtonTapped() {
        guard let province = selectedProvince else { return }

        // 加载省份下的城市数据
        viewModel.fetchRegionList(parent_code: province.code)
        sectionTitleLabel.text = "选择城市"

        // 重置区县和街道选择
        selectedDistrict = nil
        selectedStreet = nil

        // 重置城市选择项状态（取消选中，重置线条）
        citySelectionView.updateSelectionState(isSelected: false, type: 1)

        districtSelectionView.isHidden = true
        streetSelectionView.isHidden = true

    }
    
    @objc private func districtButtonTapped() {
        guard let city = selectedCity else { return }

        // 加载城市下的区县数据
        viewModel.fetchRegionList(parent_code: city.code)
        sectionTitleLabel.text = "选择区县"

        // 重置区县选择项状态（取消选中，重置线条）
        districtSelectionView.updateSelectionState(isSelected: false, type: 2)

        // 重置街道选择
        selectedStreet = nil
        streetSelectionView.isHidden = true
    }

    @objc private func streetButtonTapped() {
        guard let district = selectedDistrict else { return }

        // 加载区县下的街道数据
        viewModel.fetchRegionList(parent_code: district.code)
        sectionTitleLabel.text = "选择街道"
    }
    
    // MARK: - Data Processing
    
    /// 处理地区数据，按字母分组
    private func processRegionData(_ regions: [RegionListItemModel]) {
        // 清空现有数据
        groupedRegions.removeAll()
        sectionTitles.removeAll()
        
        // 如果没有数据，添加一个空分组避免崩溃
        if regions.isEmpty {
            groupedRegions["#"] = []
            sortedSectionKeys = ["#"]
            sectionTitles = ["#"]
            return
        }
        
        // 按首字母分组
        for region in regions {
            // 安全获取前缀
            let prefix = getEnglishPrefix(for: region.name)
            
            // 确保分组存在
            if groupedRegions[prefix] == nil {
                groupedRegions[prefix] = []
            }
            
            // 安全添加到分组
            groupedRegions[prefix]?.append(region)
        }
        
        // 排序分组标题
        sortedSectionKeys = groupedRegions.keys.sorted()
        sectionTitles = sortedSectionKeys
        
        // 对每个分组内的数据进行排序
        for key in sortedSectionKeys {
            groupedRegions[key]?.sort { $0.name < $1.name }
        }
    }
    
    /// 获取地区名称对应的英文字母前缀
    private func getEnglishPrefix(for name: String) -> String {
        // 特殊地区的快速处理（避免系统转换的不确定性）
        if let quickResult = getQuickMappingLetter(for: name) {
            return quickResult
        }
        
        // 清理地区名称，去掉常见后缀
        let cleanName = cleanRegionName(name)
        
        // 获取首字符
        guard let firstChar = cleanName.first else {
            return "#"
        }
        
        // 使用系统API获取拼音首字母
        return getPinyinFirstLetter(from: String(firstChar))
    }
    
    /// 对常见地区进行快速映射，避免系统转换的不确定性
    private func getQuickMappingLetter(for name: String) -> String? {
        // 只处理最常见的省份和直辖市，减少硬编码
        let quickMappings = [
            "北京": "B", "上海": "S", "天津": "T", "重庆": "C",
            "安徽": "A", "福建": "F", "甘肃": "G", "广东": "G", "广西": "G", "贵州": "G",
            "海南": "H", "河北": "H", "河南": "H", "黑龙江": "H", "湖北": "H", "湖南": "H",
            "吉林": "J", "江苏": "J", "江西": "J", "辽宁": "L",
            "内蒙古": "N", "宁夏": "N", "青海": "Q",
            "山东": "S", "山西": "S", "陕西": "S", "四川": "S",
            "西藏": "X", "新疆": "X", "云南": "Y", "浙江": "Z"
        ]
        
        // 检查完整匹配
        if let result = quickMappings[name] {
            return result
        }
        
        // 检查去掉后缀的匹配
        let cleanName = cleanRegionName(name)
        return quickMappings[cleanName]
    }
    
    /// 清理地区名称，去掉常见的行政区划后缀
    private func cleanRegionName(_ name: String) -> String {
        let suffixes = ["省", "市", "区", "县", "自治区", "特别行政区", "壮族自治区", "回族自治区", "维吾尔自治区"]
        var cleanName = name
        
        for suffix in suffixes {
            if cleanName.hasSuffix(suffix) {
                cleanName = String(cleanName.dropLast(suffix.count))
                break
            }
        }
        
        return cleanName.isEmpty ? name : cleanName
    }
    
    /// 使用系统API获取中文字符的拼音首字母
    private func getPinyinFirstLetter(from char: String) -> String {
        // 使用CFStringTransform获取拼音
        let mutableString = NSMutableString(string: char)
        
        // 转换为拉丁字母（拼音）
        if CFStringTransform(mutableString, nil, kCFStringTransformToLatin, false) {
            // 去掉音调符号
            CFStringTransform(mutableString, nil, kCFStringTransformStripDiacritics, false)
            
            let pinyin = mutableString as String
            
            // 获取拼音的首字母并转换为大写
            if let firstChar = pinyin.first, firstChar.isLetter {
                return String(firstChar).uppercased()
            }
        }
        
        // 如果系统转换失败，使用简单的fallback方案
        return getSimpleFallbackLetter(for: char)
    }
    
    /// 简单的fallback方案，当系统拼音转换失败时使用
    private func getSimpleFallbackLetter(for char: String) -> String {
        guard let unicodeScalar = char.unicodeScalars.first else {
            return "#"
        }
        
        let value = unicodeScalar.value
        
        // 使用简单的哈希算法将Unicode值映射到A-Z
        let letterIndex = Int(value % 26)
        let letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        let index = letters.index(letters.startIndex, offsetBy: letterIndex)
        return String(letters[index])
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate

extension ProductSelectLocationController: UITableViewDataSource, UITableViewDelegate {
    
    // MARK: - Section Methods
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return sortedSectionKeys.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        // 安全检查section索引
        guard section < sortedSectionKeys.count else { return 0 }
        
        let sectionKey = sortedSectionKeys[section]
        return groupedRegions[sectionKey]?.count ?? 0
    }
    
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 0.01
    }
    
    // MARK: - Cell Methods
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "LocationTableViewCell", for: indexPath) as! LocationTableViewCell
        
        // 安全检查section索引
        guard indexPath.section < sortedSectionKeys.count else {
            return cell
        }
        
        let sectionKey = sortedSectionKeys[indexPath.section]
        guard let regions = groupedRegions[sectionKey], indexPath.row < regions.count else {
            return cell
        }
        
        let region = regions[indexPath.row]
        let isSelected = isRegionSelected(region: region)
        var sectionTitle = ""
        if indexPath.row == 0 {
            sectionTitle = sectionKey
        }
        cell.configure(with: region, isSelected: isSelected, sectionTitle: sectionTitle)
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        // 安全检查section索引
        guard indexPath.section < sortedSectionKeys.count else {
            return
        }
        
        let sectionKey = sortedSectionKeys[indexPath.section]
        guard let regions = groupedRegions[sectionKey], indexPath.row < regions.count else {
            return
        }
        
        let selectedRegion = regions[indexPath.row]
        
        // 根据当前显示的内容类型处理选择
        if sectionTitleLabel.text == "选择省份/地区" {
            handleProvinceSelection(region: selectedRegion)
        } else if sectionTitleLabel.text == "选择城市" {
            handleCitySelection(region: selectedRegion)
        } else if sectionTitleLabel.text == "选择区县" {
            handleDistrictSelection(region: selectedRegion)
        } else if sectionTitleLabel.text == "选择街道" {
            handleStreetSelection(region: selectedRegion)
        }
        
        tableView.reloadData()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 38
    }
    
    //     MARK: - Index Methods
//    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
//        guard section < sortedSectionKeys.count else { return nil }
//        
//        let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: "LocationSectionHeaderView") as! LocationSectionHeaderView
//        headerView.configure(with: sortedSectionKeys[section])
//        return headerView
//    }
//    
//    func sectionIndexTitles(for tableView: UITableView) -> [String]? {
//        // 只有在有数据时才显示索引
//        return sectionTitles.isEmpty ? nil : sectionTitles
//    }
//    
//    func tableView(_ tableView: UITableView, sectionForSectionIndexTitle title: String, at index: Int) -> Int {
//        // 安全查找索引位置
//        if let sectionIndex = sortedSectionKeys.firstIndex(of: title) {
//            return sectionIndex
//        }
//        
//        // 如果找不到精确匹配，返回最接近的索引
//        return min(index, sortedSectionKeys.count - 1)
//    }
    
    // MARK: - Selection Handlers
    
    private func handleProvinceSelection(region: RegionListItemModel) {
        selectedProvince = region
        selectedCity = nil
        selectedDistrict = nil
        selectedStreet = nil

        // 更新省份选择项
        provinceSelectionView.updateTitle(region.name)
        provinceSelectionView.updateSelectionState(isSelected: true, type: 0)

        // 重置城市、区县和街道选择项
        if region.has_children {
            // 显示城市选择项
            citySelectionView.configure(title: "请选择城市", isSelected: false, type: 1)
            citySelectionView.isHidden = false

            // 隐藏区县和街道选择项
            districtSelectionView.isHidden = true
            streetSelectionView.isHidden = true
            // 加载城市数据
            viewModel.fetchRegionList(parent_code: region.code)
            sectionTitleLabel.text = "选择城市"
        } else {
            // 直辖市情况，直接完成选择
            locationSelectedPublisher.send((region, nil, nil, nil))
            navigationController?.popViewController(animated: true)
        }
    }
    
    private func handleCitySelection(region: RegionListItemModel) {
        selectedCity = region
        selectedDistrict = nil
        selectedStreet = nil

        // 更新城市选择项
        citySelectionView.updateTitle(region.name)
        citySelectionView.updateSelectionState(isSelected: true, type: 1)

        if region.has_children {
            // 有区县，继续选择
            districtSelectionView.configure(title: "请选择区县", isSelected: false, type: 2)
            districtSelectionView.isHidden = false

            // 隐藏街道选择项
            streetSelectionView.isHidden = true

            // 加载区县数据
            viewModel.fetchRegionList(parent_code: region.code)
            sectionTitleLabel.text = "选择区县"
        } else {
            // 没有区县，完成选择
            locationSelectedPublisher.send((selectedProvince!, region, nil, nil))

            navigationController?.popViewController(animated: true)
        }
    }
    
    private func handleDistrictSelection(region: RegionListItemModel) {
        selectedDistrict = region
        selectedStreet = nil

        // 更新区县选择项
        districtSelectionView.updateTitle(region.name)
        districtSelectionView.updateSelectionState(isSelected: true, type: 2)

        if region.has_children {
            // 有街道，继续选择
            streetSelectionView.configure(title: "请选择街道", isSelected: false, type: 3)
            streetSelectionView.isHidden = false

            // 加载街道数据
            viewModel.fetchRegionList(parent_code: region.code)
            sectionTitleLabel.text = "选择街道"
        } else {
            // 没有街道，完成选择
            locationSelectedPublisher.send((selectedProvince!, selectedCity, selectedDistrict, nil))
            navigationController?.popViewController(animated: true)
        }
    }

    private func handleStreetSelection(region: RegionListItemModel) {
        selectedStreet = region

        // 更新街道选择项
        streetSelectionView.updateTitle(region.name)
        streetSelectionView.updateSelectionState(isSelected: true, type: 3)

        // 完成选择，返回结果
        locationSelectedPublisher.send((selectedProvince!, selectedCity, selectedDistrict, selectedStreet))
        navigationController?.popViewController(animated: true)
    }

    private func isRegionSelected(region: RegionListItemModel) -> Bool {
        if sectionTitleLabel.text == "选择省份/地区" {
            return selectedProvince?.code == region.code
        } else if sectionTitleLabel.text == "选择城市" {
            return selectedCity?.code == region.code
        } else if sectionTitleLabel.text == "选择区县" {
            return selectedDistrict?.code == region.code
        } else if sectionTitleLabel.text == "选择街道" {
            return selectedStreet?.code == region.code
        }
        return false
    }
}
