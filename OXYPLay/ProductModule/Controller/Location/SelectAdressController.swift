//
//  SelectAdressController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/24.
//

import UIKit
import SnapKit
import Combine
import Then

class SelectAdressController: BaseViewController {

    // MARK: - Properties
    private var viewModel: ProductOrderConfirmation​ViewModel?
    private var addressModel: ProductAddressModel?

    // 用于存储地址编辑过程中的临时数据
    private var tempAddressData: [String: Any] = [:]
    private var isEditingAddress: Bool = false
    private var editingAddressId: String?

    // 地址选择完成的Publisher
    let addressSelectedPublisher = PassthroughSubject<ProductAddressItemModel, Never>()

    // 地址数据更新的Publisher（用于通知上级页面数据已更新）
    let addressDataUpdatedPublisher = PassthroughSubject<Void, Never>()

    // MARK: - UI Components
    private lazy var toolBar = BaseTabToolBar().then {
        $0.delegate = self
    }

    private lazy var tableView = UITableView(frame: .zero, style: .grouped).then {
        $0.backgroundColor = color_F6F8F9
        $0.separatorStyle = .none
        $0.showsVerticalScrollIndicator = false
        $0.delegate = self
        $0.dataSource = self
        $0.register(AddressTableViewCell.self, forCellReuseIdentifier: "AddressTableViewCell")
        $0.sectionHeaderHeight = 44
        $0.sectionFooterHeight = 0.01
        if #available(iOS 15.0, *) {
            $0.sectionHeaderTopPadding = 0
        }
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViewModel()
        configUI()
        setupBindings()
        loadData()
    }

    private func setupViewModel() {
        viewModel = ProductOrderConfirmation​ViewModel()
    }

    private func loadData() {
        // 如果没有预设的地址数据，则获取地址数据
        if addressModel == nil {
            viewModel?.fetchLocationList()
        } else {
            // 如果有预设数据，直接刷新UI
            tableView.reloadData()
        }
    }

    // MARK: - Public Methods

    /// 设置地址数据（从外部传入）
    func setAddressModel(_ model: ProductAddressModel) {
        self.addressModel = model
        tableView.reloadData()
    }

    override func configUI() {
        title = "选择地址"
        view.backgroundColor = color_F6F8F9

        // 配置 toolBar
        toolBar.configureLeftMultipleRightFull(leftItems: [], rightButtonTitle: "确认选择")

        // 添加表格视图
        view.addSubview(tableView)
        // 添加底部工具栏
        view.addSubview(toolBar)
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(toolBar.snp.top)
        }

      
        toolBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
    }

    override func setupBindings() {
        // 监听地址数据变化
        viewModel?.$productAddressModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] model in
                guard let self = self, let model = model else { return }
                // 更新地址数据并刷新UI
                self.addressModel = model
                self.tableView.reloadData()
            }
            .store(in: &cancellables)
    }

    // MARK: - Private Methods

    private func addNewAddress() {
        // 清空临时数据
        tempAddressData = [:]
        isEditingAddress = false
        editingAddressId = nil

        showEditLocationController(with: [:], isEditMode: false)
    }

    // MARK: - Action Methods

    private func selectAddress(at index: Int) {
        guard var addressModel = addressModel else { return }

        // 清除原地址的选中状态
        addressModel.default_address.isSelect = false

        // 更新新地址列表的选中状态
        for i in 0..<addressModel.address_list.count {
            addressModel.address_list[i].isSelect = (i == index)
        }

        self.addressModel = addressModel

        // 刷新表格
        tableView.reloadData()
    }

    private func selectDefaultAddress() {
        guard var addressModel = addressModel else { return }

        // 设置原地址为选中状态
        addressModel.default_address.isSelect = true

        // 清除新地址列表的选中状态
        for i in 0..<addressModel.address_list.count {
            addressModel.address_list[i].isSelect = false
        }

        self.addressModel = addressModel

        // 刷新表格
        tableView.reloadData()
    }

    private func editAddress(_ address: ProductAddressItemModel) {
        // 设置编辑状态
        isEditingAddress = true
        editingAddressId = address.id

        // 准备地址数据，包含完整的地区信息
        let addressData: [String: Any] = [
            "联系人": address.recipient_name,
            "手机号": address.phone,
            "所在地区": address.region,
            "详细地址": address.detail,
            "isDefualt": address.is_default,  // 保持原有的key
            "is_default": address.is_default, // 添加标准的key
            "address_id": address.id,
            // 地区相关信息
            "province": address.province,
            "province_code": address.province_code,
            "city": address.city,
            "city_code": address.city_code,
            "district": address.district,
            "district_code": address.district_code,
            "street": address.street,
            "street_code": address.street_code
        ]

        showEditLocationController(with: addressData, isEditMode: true)
    }

    // MARK: - Address Edit Flow Methods

    /// 显示地址编辑控制器
    private func showEditLocationController(with data: [String: Any], isEditMode: Bool) {
        let editLocationController = EditLocationController()
        editLocationController.dic = data
        editLocationController.isEditMode = isEditMode

        // 监听地区选择事件
        editLocationController.pushtoSelectLocationPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] addressData in
                self?.handleLocationSelection(addressData: addressData)
            }
            .store(in: &cancellables)

        // 监听地址保存事件
        editLocationController.addressSavePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] addressData in
                if isEditMode {
                    self?.updateAddress(addressData: addressData)
                } else {
                    self?.createAddress(addressData: addressData)
                }
            }
            .store(in: &cancellables)

        customPresent(editLocationController, animated: true)
    }

    /// 处理地区选择
    private func handleLocationSelection(addressData: [String: Any]) {
        // 保存当前的地址数据
        tempAddressData = addressData

        // 跳转到地区选择页面
        let locationController = ProductSelectLocationController()

        // 监听地区选择完成事件
        locationController.locationSelectedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (province, city, district, street) in
                self?.handleLocationSelected(province: province, city: city, district: district, street: street)
            }
            .store(in: &cancellables)

        pushVc(locationController, animated: true)
    }

    /// 处理地区选择完成
    private func handleLocationSelected(province: RegionListItemModel, city: RegionListItemModel?, district: RegionListItemModel?, street: RegionListItemModel?) {
        // 构建地区字符串
        var regionString = province.name
        if let city = city, city.name != province.name {
            regionString += city.name
        }
        if let district = district {
            regionString += district.name
        }
        if let street = street {
            regionString += street.name
        }

        // 更新临时数据中的地区信息
        tempAddressData["所在地区"] = regionString
        tempAddressData["province"] = province.name
        tempAddressData["province_code"] = province.code

        if let city = city {
            tempAddressData["city"] = city.name
            tempAddressData["city_code"] = city.code
        }

        if let district = district {
            tempAddressData["district"] = district.name
            tempAddressData["district_code"] = district.code
        }

        if let street = street {
            tempAddressData["street"] = street.name
            tempAddressData["street_code"] = street.code
        }

        // 返回到SelectAddressController，然后重新弹出EditLocationController
        navigationController?.popViewController(animated: true)

        // 延迟一点时间再弹出EditLocationController，确保页面切换完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            self?.showEditLocationController(with: self?.tempAddressData ?? [:], isEditMode: self?.isEditingAddress ?? false)
        }
    }

    // MARK: - Address API Methods

    private func createAddress(addressData: [String: Any]) {
        // 验证必传参数
        guard let recipientName = addressData["recipient_name"] as? String, !recipientName.isEmpty,
              let phone = addressData["phone"] as? String, !phone.isEmpty,
              let province = addressData["province"] as? String, !province.isEmpty,
              let provinceCode = addressData["province_code"] as? String, !provinceCode.isEmpty,
              let city = addressData["city"] as? String, !city.isEmpty,
              let cityCode = addressData["city_code"] as? String, !cityCode.isEmpty,
              let district = addressData["district"] as? String, !district.isEmpty,
              let districtCode = addressData["district_code"] as? String, !districtCode.isEmpty,
              let street = addressData["street"] as? String, !street.isEmpty,
              let streetCode = addressData["street_code"] as? String, !streetCode.isEmpty else {
            print("创建地址失败：缺少必传参数")
            return
        }

        // 构建创建地址的参数
        var params = addressData
        params["type"] = 1 // 收货地址（必传）

        viewModel?.createAddress(addressData: params) { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    // 创建成功，显示提示
                    print("地址创建成功")
                    // 重新获取最新的地址数据
                    self?.refreshAddressList()
                } else {
                    // 创建失败，显示错误提示
                    print("地址创建失败")
                }
            }
        }
    }

    private func updateAddress(addressData: [String: Any]) {
        // 验证必传参数
        guard let addressId = addressData["address_id"],
              let province = addressData["province"] as? String, !province.isEmpty,
              let provinceCode = addressData["province_code"] as? String, !provinceCode.isEmpty,
              let city = addressData["city"] as? String, !city.isEmpty,
              let cityCode = addressData["city_code"] as? String, !cityCode.isEmpty,
              let district = addressData["district"] as? String, !district.isEmpty,
              let districtCode = addressData["district_code"] as? String, !districtCode.isEmpty,
              let street = addressData["street"] as? String, !street.isEmpty,
              let streetCode = addressData["street_code"] as? String, !streetCode.isEmpty else {
            print("更新地址失败：缺少必传参数")
            return
        }

        // 构建更新地址的参数
        var params = addressData
        params["type"] = 1 // 收货地址（必传）

        viewModel?.updateAddress(addressData: params) { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    // 更新成功，显示提示
                    print("地址更新成功")
                    // 重新获取最新的地址数据
                    self?.refreshAddressList()
                } else {
                    // 更新失败，显示错误提示
                    print("地址更新失败")
                }
            }
        }
    }

    /// 刷新地址列表数据
    private func refreshAddressList() {
        // 重新获取最新的地址数据
        viewModel?.fetchLocationList()

        // 通知上级页面数据已更新
        addressDataUpdatedPublisher.send(())
    }
}



// MARK: - UITableViewDataSource

extension SelectAdressController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 2 // 原地址 + 选择新地址
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let addressModel = addressModel else { return 0 }

        if section == 0 {
            // 原地址部分
            return addressModel.default_address.id.isEmpty ? 0 : 1
        } else {
            // 选择新地址部分
            return addressModel.address_list.count
        }
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "AddressTableViewCell", for: indexPath) as! AddressTableViewCell

        guard let addressModel = addressModel else { return cell }

        if indexPath.section == 0 {
            // 原地址 - 也可以选择
            cell.configure(with: addressModel.default_address, isDefaultSection: true)
        } else {
            // 选择新地址
            let address = addressModel.address_list[indexPath.row]
            cell.configure(with: address, isDefaultSection: false)
        }

        cell.editButtonTapped = { [weak self] address in
            self?.editAddress(address)
        }

        cell.selectionButtonTapped = { [weak self] address in
            if indexPath.section == 0 {
                // 选择原地址
                self?.selectDefaultAddress()
            } else {
                // 选择新地址列表中的地址
                if let index = addressModel.address_list.firstIndex(where: { $0.id == address.id }) {
                    self?.selectAddress(at: index)
                }
            }
        }

        return cell
    }
}

// MARK: - UITableViewDelegate

extension SelectAdressController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = UIView()
        headerView.backgroundColor = color_F6F8F9

        let titleLabel = UILabel().then {
            $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            $0.textColor = color_3D3E40
        }

        if section == 0 {
            titleLabel.text = "原地址"
        } else {
            titleLabel.text = "选择新地址"

            // 添加新增地址按钮
            let addButton = UIButton(type: .custom).then {
                $0.setTitle("+ 新增地址", for: .normal)
                $0.setTitleColor(color_blue, for: .normal)
                $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
            }

            addButton.tapPublisher
                .sink { [weak self] _ in
                    self?.addNewAddress()
                }
                .store(in: &cancellables)

            headerView.addSubview(addButton)
            addButton.snp.makeConstraints { make in
                make.right.equalToSuperview().offset(-16)
                make.centerY.equalToSuperview()
            }
        }

        headerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }

        return headerView
    }
}

// MARK: - BaseTabToolBarDelegate

extension SelectAdressController: TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        // 确认选择地址
        guard let addressModel = addressModel else { return }

        var selectedAddress: ProductAddressItemModel?

        // 检查是否选中了原地址
        if addressModel.default_address.isSelect {
            selectedAddress = addressModel.default_address
        } else {
            // 检查新地址列表中是否有选中的
            selectedAddress = addressModel.address_list.first(where: { $0.isSelect })
        }

        if let address = selectedAddress {
            print("选中的地址: \(address.recipient_name)")
            // 发送选中的地址给上级页面
            addressSelectedPublisher.send(address)
        } else {
            print("请选择一个地址")
            return
        }

        navigationController?.popViewController(animated: true)
    }
}

// MARK: - AddressTableViewCell

class AddressTableViewCell: UITableViewCell {

    // MARK: - Properties
    var editButtonTapped: ((ProductAddressItemModel) -> Void)?
    var selectionButtonTapped: ((ProductAddressItemModel) -> Void)?
    private var address: ProductAddressItemModel?

    // MARK: - UI Components
    private lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
    }

    private lazy var defaultLabel = UILabel().then {
        $0.text = "默认"
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.backgroundColor = UIColor(hexString: "#FF0000",transparency: 0.08)
        $0.textColor = UIColor(hexString: "#FF0000")
        $0.textAlignment = .center
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
        $0.layer.maskedCorners = [.layerMinXMaxYCorner,.layerMaxXMinYCorner]
        $0.isHidden = true
    }

    private lazy var radioButton = UIButton(type: .custom).then {
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
    }

    private lazy var namePhoneLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textColor = color_2B2C2F
    }

    private lazy var editButton = UIButton(type: .custom).then {
        $0.setImage(UIImage(systemName: "pencil"), for: .normal)
        $0.tintColor = color_999DA1
    }

    private lazy var addressLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.textColor = UIColor(hexString: "#2B2C2F", transparency: 0.56)
        $0.numberOfLines = 0
    }

    private lazy var detailLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.textColor = UIColor(hexString: "#2B2C2F", transparency: 1)
        $0.numberOfLines = 0
    }

    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupBindings()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupUI() {
        backgroundColor = color_F6F8F9
        selectionStyle = .none

        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(0)
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview().offset(-12)
        }

        containerView.addSubview(defaultLabel)
        containerView.addSubview(radioButton)
        containerView.addSubview(namePhoneLabel)
        containerView.addSubview(editButton)
        containerView.addSubview(addressLabel)
        containerView.addSubview(detailLabel)

        setupConstraints()
    }

    private func setupConstraints() {
        defaultLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.right.equalToSuperview()
            make.width.equalTo(42)
            make.height.equalTo(19)
        }

        radioButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(12)
            make.width.height.equalTo(20)
        }

        namePhoneLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalTo(36)
            make.right.lessThanOrEqualTo(editButton.snp.left).offset(-8)
        }

        editButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalTo(-12)
            make.width.height.equalTo(20)
        }

        addressLabel.snp.makeConstraints { make in
            make.top.equalTo(namePhoneLabel.snp.bottom).offset(10)
            make.left.equalTo(namePhoneLabel)
        }

        detailLabel.snp.makeConstraints { make in
            make.top.equalTo(addressLabel.snp.bottom).offset(10)
            make.left.equalTo(namePhoneLabel)
            make.right.equalTo(editButton.snp.left).offset(-8)
            make.bottom.equalTo(-12)
        }
    }

    private func setupBindings() {
        editButton.addTarget(self, action: #selector(editButtonTapped(_:)), for: .touchUpInside)
        radioButton.addTarget(self, action: #selector(radioButtonTapped(_:)), for: .touchUpInside)
    }

    // MARK: - Actions
    @objc private func editButtonTapped(_ sender: UIButton) {
        guard let address = address else { return }
        editButtonTapped?(address)
    }

    @objc private func radioButtonTapped(_ sender: UIButton) {
        guard let address = address else { return }
        selectionButtonTapped?(address)
    }

    // MARK: - Configuration
    func configure(with address: ProductAddressItemModel, isDefaultSection: Bool = false) {
        self.address = address

        radioButton.isSelected = address.isSelect
        namePhoneLabel.text = "\(address.recipient_name) 86-\(address.phone.maskPhoneNumber)"
        addressLabel.text = address.region
        detailLabel.text = address.detail

        // 根据是否为默认地址区域来显示默认标签
        if isDefaultSection {
            // 原地址区域，始终显示默认标签
            defaultLabel.isHidden = false
        } else {
            // 地址列表区域，根据is_default字段显示
            defaultLabel.isHidden = !address.is_default
        }
    }
}
