//
//  ProductSellingPurchasedController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import UIKit

class ProductSellingPurchasedController: JXBaseRootViewController {
    var isSelling = true

    override func viewDidLoad() {
        configSegement()
        super.viewDidLoad()
        self.title = isSelling ? "我卖出的":"我买到的"
    }

    func configSegement(){
        // 配置属性
        titles = ["全部","待付款","待发货","待收货","评价","退款"]
        shouldHideNavigationBar = false

        // 创建子控制器
        for (index, _) in titles.enumerated() {
            let vc = ProductSellingPurchasedListController()
            vc.orderType = isSelling ? .selling : .purchased
            vc.statusFilter = OrderStatusFilter(rawValue: index) ?? .all
            viewControllers.append(vc)
        }
    }
}
