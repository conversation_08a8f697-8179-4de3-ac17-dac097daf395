//
//  ProductCartController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import UIKit
import SnapKit
import Combine

/// 购物车控制器
class ProductCartController: BaseViewController {

    // MARK: - 属性

    private lazy var viewModel = ProductCartViewModel()

    // MARK: - UI组件

    /// 主表格视图
    private lazy var tableView = UITableView(frame: .zero, style: .grouped).then {
        $0.backgroundColor = color_F6F8F9
        $0.separatorStyle = .none
        $0.showsVerticalScrollIndicator = false
        $0.delegate = self
        $0.dataSource = self
        $0.estimatedRowHeight = 130
        $0.rowHeight = UITableView.automaticDimension
        $0.sectionHeaderHeight = UITableView.automaticDimension
        $0.estimatedSectionHeaderHeight = 56
        $0.tableHeaderView = UIView(frame: CGRect(x: 0, y: 0, width: 0, height: 0.01))
        $0.register(ProductCartItemCell.self, forCellReuseIdentifier: NSStringFromClass(ProductCartItemCell.self))
        $0.register(ProductCartSellerHeaderView.self, forHeaderFooterViewReuseIdentifier:NSStringFromClass(ProductCartSellerHeaderView.self))
    }

    /// 底部结算栏
    private lazy var bottomView = ProductCartBottomView().then {
        $0.delegate = self
    }

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configLayout()
        setupBindings()
        setupRefresh()
        loadData()
    }

    // MARK: - UI配置

    override func configUI() {
        title = "我的购物车"
        view.addSubview(tableView)
        view.addSubview(bottomView)
      
    }

    override func configLayout() {
        tableView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.bottom.equalTo(bottomView.snp.top)
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
        }

        bottomView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalTo(0)
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }

     
    }

    // MARK: - 数据绑定

    override func setupBindings() {
        super.setupBindings()

        // 监听购物车数据变化
        viewModel.$cartData
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)


        // 监听全选状态变化
        viewModel.$isAllSelected
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isAllSelected in
                self?.bottomView.updateAllSelection(isAllSelected)
            }
            .store(in: &cancellables)

        // 监听总价变化
        viewModel.$totalPrice
            .receive(on: DispatchQueue.main)
            .sink { [weak self] totalPrice in
                guard let self = self else { return }
                self.bottomView.updatePriceInfo(totalPrice: totalPrice, postageFee: self.viewModel.totalPostageFee)
            }
            .store(in: &cancellables)

        // 监听邮费变化
        viewModel.$totalPostageFee
            .receive(on: DispatchQueue.main)
            .sink { [weak self] postageFee in
                guard let self = self else { return }
                self.bottomView.updatePriceInfo(totalPrice: self.viewModel.totalPrice, postageFee: postageFee)
            }
            .store(in: &cancellables)

        // 监听选中数量变化
        viewModel.$selectedCount
            .receive(on: DispatchQueue.main)
            .sink { [weak self] selectedCount in
                self?.bottomView.updateCheckoutButton(enabled: selectedCount > 0, selectedCount: selectedCount)
            }
            .store(in: &cancellables)

    }

    // MARK: - 刷新设置

    private func setupRefresh() {
        // 使用BaseViewController的便捷方法设置刷新（只启用头部刷新）
        setupRefresh(for: tableView, with: viewModel, enableHeader: true, enableFooter: false)
    }

    // MARK: - 数据加载

    private func loadData() {
        viewModel.refreshData()
    }
}

// MARK: - UITableViewDataSource

extension ProductCartController: UITableViewDataSource {

    func numberOfSections(in tableView: UITableView) -> Int {
        return viewModel.cartData.data.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.cartData.data[section].items.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: NSStringFromClass(ProductCartItemCell.self), for: indexPath) as! ProductCartItemCell
        let items = viewModel.cartData.data[indexPath.section].items
        cell.configure(with:items[indexPath.row],isLast:items.count - 1 == indexPath.row)
        cell.delegate = self
        return cell
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: NSStringFromClass(ProductCartSellerHeaderView.self)) as! ProductCartSellerHeaderView
        let seller = viewModel.cartData.data[section]
        headerView.configure(with: seller)
        headerView.delegate = self
        return headerView
    }
}

// MARK: - UITableViewDelegate

extension ProductCartController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForHeaderInSection section: Int) -> CGFloat {
        return 56 // 提供一个估算高度
    }

    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 0.01
    }

    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        let footerView = UIView()
        footerView.backgroundColor = .clear
        return footerView
    }
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        // 移除cell点击选择逻辑，只通过按钮点击来处理选择
        tableView.deselectRow(at: indexPath, animated: false)
    }

    // MARK: - 右滑删除

    func tableView(_ tableView: UITableView, canEditRowAt indexPath: IndexPath) -> Bool {
        return true
    }

    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            let item = viewModel.cartData.data[indexPath.section].items[indexPath.row]
            // 删除单个商品
            viewModel.removeSingleItem(cartId: item.cart_id)
        }
    }

    func tableView(_ tableView: UITableView, titleForDeleteConfirmationButtonForRowAt indexPath: IndexPath) -> String? {
        return "删除"
    }
}

// MARK: - ProductCartItemCellDelegate

extension ProductCartController: ProductCartItemCellDelegate {

    func cartItemCell(_ cell: ProductCartItemCell, didChangeSelection item: CartItem) {
        // 找到对应的卖家和商品，切换选择状态
        for seller in viewModel.cartData.data {
            if let cartItem = seller.items.first(where: { $0.cart_id == item.cart_id }) {
                viewModel.toggleItemSelection(sellerId: seller.seller_id, cartId: item.cart_id)
                break
            }
        }
    }

    func cartItemCell(_ cell: ProductCartItemCell, didChangeQuantity quantity: Int, for item: CartItem) {
        // 更新商品数量
        viewModel.updateItemQuantity(cartId: item.cart_id, quantity: quantity)

        // 如果是增加数量，自动选中该商品
        if quantity > item.quantity {
            viewModel.ensureItemSelected(cartId: item.cart_id)
        }
    }

    func cartItemCell(_ cell: ProductCartItemCell, didRequestDeleteItem item: CartItem) {
        // 显示删除确认弹窗
        let alert = UIAlertController(title: "提示", message: "确定要删除这件商品吗？", preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
        alert.addAction(UIAlertAction(title: "删除", style: .destructive) { [weak self] _ in
            // 删除商品
            self?.viewModel.removeSingleItem(cartId: item.cart_id)
        })

        present(alert, animated: true)
    }
}

// MARK: - ProductCartSellerHeaderViewDelegate

extension ProductCartController: ProductCartSellerHeaderViewDelegate {

    func cartSellerHeaderView(_ headerView: ProductCartSellerHeaderView, didChangeSelection isSelected: Bool, seller: CartSeller) {
        viewModel.toggleSellerSelection(sellerId: seller.seller_id)
    }
}

// MARK: - ProductCartBottomViewDelegate

extension ProductCartController: ProductCartBottomViewDelegate {

    func cartBottomView(_ bottomView: ProductCartBottomView, didChangeAllSelection isSelected: Bool) {
        // 通过ViewModel的方法来处理全选，而不是直接设置属性
        viewModel.cartData.setAllSelected(isSelected)
        // 手动触发数据更新
        viewModel.cartData = viewModel.cartData
    }

    func cartBottomViewDidTapCheckout(_ bottomView: ProductCartBottomView) {
        let selectedItems = viewModel.cartData.selectedItems
        if selectedItems.isEmpty {
            // 显示提示：请选择要结算的商品
            return
        }

        // 跳转到结算页面
        print("跳转到结算页面，选中商品数量：\(selectedItems.count)")
    }
}
