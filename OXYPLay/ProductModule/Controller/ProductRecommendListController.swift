//
//  ProductRecommendListController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/23.
//

import UIKit
import Combine
import SnapKit

/// 商品推荐列表控制器
class ProductRecommendListController: BaseViewController {
    
    // MARK: - 属性
    
    /// ViewModel
    private var viewModel: ProductRecommendListViewModel!
    
    /// 瀑布流布局
    private lazy var waterfallLayout = WaterfallMutiSectionFlowLayout().then {
        $0.delegate = self
    }
    
    /// CollectionView
    private lazy var collectionView = UICollectionView(frame: .zero, collectionViewLayout: waterfallLayout).then {
        $0.backgroundColor = color_F6F8F9
        $0.showsVerticalScrollIndicator = false
        $0.delegate = self
        $0.dataSource = self
        $0.register(ProductRecommendCollectionCell.self, forCellWithReuseIdentifier: ProductRecommendCollectionCell.identifier)
    }
    
    
    // MARK: - 初始化
    
    /// 初始化方法
    /// - Parameters:
    ///   - adId: 广告ID
    ///   - productId: 商品ID
    init(adId: String, productId: String) {
        super.init()
        self.viewModel = ProductRecommendListViewModel(adId: adId, productId: productId)
    }
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configLayout()
        setupBindings()
        setupRefresh()
        
        // 加载数据
        viewModel.loadRecommendList()
    }
    
    // MARK: - UI配置
    
    override func configUI() {
        title = "关联商品"
        view.backgroundColor = color_F6F8F9
        
        // 添加子视图
        view.addSubview(collectionView)
        
    }
    
    
    override func configLayout() {
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(12)
            make.bottom.equalToSuperview()
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
        
    }
    
    // MARK: - 数据绑定
    
    override func setupBindings() {
        // 监听商品列表变化
        viewModel.$productList
            .receive(on: DispatchQueue.main)
            .sink { [weak self] productList in
                self?.collectionView.reloadData()
            }
            .store(in: &cancellables)
        
        
        // 监听刷新状态
        viewModel.$refreshState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                switch state {
                case .refreshFailure(let message), .loadMoreFailure(let message):
                    self?.showErrorMessage(message)
                default:
                    break
                }
            }
            .store(in: &cancellables)
    }
    
    
    /// 显示错误信息
    /// - Parameter message: 错误信息
    private func showErrorMessage(_ message: String) {
    }
    
    // MARK: - 刷新设置
    
    private func setupRefresh() {
        setupRefresh(
            for: collectionView,
            with: viewModel,
            enableHeader: true,
            enableFooter: true
        )
    }
    /// 商品点击回调
    var productTapCallback: ((ProductRecommendItem) -> Void)?
}

// MARK: - 公开方法

extension ProductRecommendListController {
    
    /// 刷新数据
    func refreshData() {
        viewModel.refreshData()
    }
    
    /// 获取当前商品数量
    var productCount: Int {
        return viewModel.productList.count
    }
    
    /// 是否有数据
    var hasData: Bool {
        return !viewModel.productList.isEmpty
    }
    
    /// 设置自定义标题
    /// - Parameter title: 标题
    func setCustomTitle(_ title: String) {
        self.title = title
    }
    
}


// MARK: - UICollectionViewDataSource

extension ProductRecommendListController: UICollectionViewDataSource {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return viewModel.productList.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: ProductRecommendCollectionCell.identifier, for: indexPath) as! ProductRecommendCollectionCell
        
        let item = viewModel.productList[indexPath.item]
        cell.configure(with: item)
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate

extension ProductRecommendListController: UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let item = viewModel.productList[indexPath.item]
        let detailController = ProductDetailController(postId: item.product_id, seller_id: item.seller_id)
        pushVcHiddenTabBar(detailController, animated: true)
    }
    
    
}

// MARK: - WaterfallMutiSectionDelegate

extension ProductRecommendListController: WaterfallMutiSectionDelegate {
    
    /// 计算每个item的高度
    func heightForRowAtIndexPath(collectionView collection: UICollectionView, layout: WaterfallMutiSectionFlowLayout, indexPath: IndexPath, itemWidth: CGFloat) -> CGFloat {
        let item = viewModel.productList[indexPath.item]
        return ProductRecommendCollectionCell.calculateHeight(for: item, width: itemWidth)
    }
    
    /// 列数（2列）
    func columnNumber(collectionView collection: UICollectionView, layout: WaterfallMutiSectionFlowLayout, section: Int) -> Int {
        return 2
    }
    
    /// section内边距
    func sectionInsets(collectionView collection: UICollectionView, layout: WaterfallMutiSectionFlowLayout, section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
    }
    
    /// 行间距
    func lineSpacing(collectionView collection: UICollectionView, layout: WaterfallMutiSectionFlowLayout, section: Int) -> CGFloat {
        return 12
    }
    
    /// 列间距
    func interitemSpacing(collectionView collection: UICollectionView, layout: WaterfallMutiSectionFlowLayout, section: Int) -> CGFloat {
        return 9
    }
}
