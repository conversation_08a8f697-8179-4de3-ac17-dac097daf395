//
//  ProductOrderConfirmation​Controller.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/15.
//
// MARK: - 支付方式标识符常量
struct PaymentMethodIdentifiers {
    /// 余额支付
    static let balance = "baselist_pay_balance"
    /// 支付宝支付
    static let alipay = "baselist_pay_alipay"
    /// 微信支付
    static let wechat = "baselist_pay_wechat"
    /// 组合支付
    static let combine = "baselist_pay_combine"
}
class ProductOrderConfirmation​Controller: BaseViewController {
    // MARK: - 属性

    var detailModel: HomeDetailModel?
    var productDetailModel: ProductDetailModel?
    var dic = Dictionary<String, Any>()
    var viewModel: ProductOrderConfirmation​ViewModel?
    private lazy var toolBar = BaseTabToolBar().then {
        $0.delegate = self
    }
    lazy var listView = BaseListView().then{
        $0.delegate = self
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configureOrderListItems()
        setupBindings()
        getData()
    }

    /// 配置UI界面
    override func configUI() {
        title = "确认订单"

        // 配置 toolBar
        toolBar.configureLeftMultipleRightFull(leftItems: [], rightButtonTitle: "立即支付")
        toolBar.setRightButtonGradient(colors: [UIColor(hexString: "FF5D48")!,UIColor(hexString: "FF3434")!], direction: .leftToRight)

        view.addSubview(toolBar)
        toolBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
        view.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.top.equalTo(self.view.safeAreaLayoutGuide.snp.top)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(toolBar.snp.top).offset(0)
        }
    }

    /// 获取数据
    func getData() {
        viewModel?.fetchLocationList()
        viewModel?.fetchWalletSummary()
        if let detailModel = detailModel {
            viewModel?.fetchCouponList(advertiser_id: detailModel.user_id, order_amount: detailModel.price)
        }
        if let proDuctDetailModel = productDetailModel {
            viewModel?.fetchCouponList(advertiser_id: proDuctDetailModel.product.seller_id, order_amount: proDuctDetailModel.getcurrentQuantityPrice())
        }
    }

    /// 设置数据绑定
    override func setupBindings() {
        self.viewModel = ProductOrderConfirmation​ViewModel()

        // 监听优惠券数据变化
        viewModel?.$couponModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] model in
                guard let model = model else {return }
                self?.productDetailModel?.coupon = model
                self?.detailModel?.coupon = model
                self?.configureOrderListItems()
            }
            .store(in: &cancellables)

        // 监听地址数据变化
        viewModel?.$productAddressModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] model in
                guard let model = model else { return }
                self?.updateAddressModelInAllData(model)
                self?.refreshUI()
            }
            .store(in: &cancellables)

        // 监听支付流程状态变化
        viewModel?.$paymentFlowState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.handlePaymentFlowStateChange(state)
            }
            .store(in: &cancellables)

        // 监听支付方式变化
        viewModel?.$selectedPaymentMethod
            .receive(on: DispatchQueue.main)
            .sink { [weak self] paymentMethod in
                self?.updatePaymentMethodUI(paymentMethod)
            }
            .store(in: &cancellables)
        viewModel?.$walletSummary
            .receive(on: DispatchQueue.main)
            .sink { [weak self] walletSummary in
                guard let self = self, let walletSummary = walletSummary else { return }

                // 钱包数据加载完成后，设置默认支付方式
                let totalAmount = self.calculateTotalAmount()
                let defaultPaymentMethod = self.viewModel?.selectDefaultPaymentMethod(orderAmount: totalAmount) ?? PaymentMethodIdentifiers.alipay
                self.viewModel?.updatePaymentMethod(defaultPaymentMethod)

                self.refreshUI()
            }
            .store(in: &cancellables)
        
    }
}

// MARK: - List Configuration
extension ProductOrderConfirmation​Controller {

    /// 配置订单确认列表项
    /// 统一处理商品订单和服务订单的列表配置
    func configureOrderListItems() {
        // 获取数据模型和地址模型
        let (orderData, addressModel) = getOrderDataAndAddress()

        guard let orderData = orderData, let addressModel = addressModel else { return }

        // 创建地址项配置
        let locationItem = createLocationItem(from: addressModel)

        // 创建支付方式配置
        let paymentItems = createPaymentMethodItems()

        // 构建列表项
        let items: [[ListItemConfig]] = [
            [
                ListItemConfig(type: .orderConfirmation, identifier: "orderConfirmation", data: orderData)
            ],
            [
                locationItem
            ],
            paymentItems
        ]

        // 设置列表项
        listView.setItems(items)
    }

    /// 获取订单数据和地址模型
    /// - Returns: 元组包含订单数据和地址模型
    private func getOrderDataAndAddress() -> (orderData: Any?, addressModel: ProductAddressModel?) {
        if let productModel = productDetailModel {
            return (productModel, productModel.productAddressModel)
        } else if let detailModel = detailModel {
            return (detailModel, detailModel.productAddressModel)
        }
        return (nil, nil)
    }

    /// 创建支付方式列表项
    /// - Returns: 支付方式配置数组
    private func createPaymentMethodItems() -> [ListItemConfig] {
        // 根据是否为商品订单决定是否显示余额子标题
        var balanceSubTitle = ""
        if let availableBalance =  viewModel?.walletSummary?.available_amount {
            balanceSubTitle = "可用余额￥\(availableBalance)"
        }

        // 计算订单总金额
        let totalAmount = calculateTotalAmount()

        // 根据余额情况选择默认支付方式
        let defaultPaymentMethod = viewModel?.selectDefaultPaymentMethod(orderAmount: totalAmount) ?? PaymentMethodIdentifiers.alipay

        return [
            ListItemConfig.singleSelect(
                identifier: PaymentMethodIdentifiers.balance,
                title: "余额支付",
                iconString: "baselist_pay_balance",
                isSelected: defaultPaymentMethod == PaymentMethodIdentifiers.balance,
                subTitle: balanceSubTitle
            ),
            ListItemConfig(
                type: .select,
                identifier: PaymentMethodIdentifiers.combine,
                iconString: "baselist_pay_combine",
                title: "组合支付"
            ),
            ListItemConfig.singleSelect(
                identifier: PaymentMethodIdentifiers.alipay,
                title: "支付宝支付",
                iconString: "baselist_pay_ali",
                isSelected: defaultPaymentMethod == PaymentMethodIdentifiers.alipay
            ),
            ListItemConfig.singleSelect(
                identifier: PaymentMethodIdentifiers.wechat,
                title: "微信支付",
                iconString: "baselist_pay_wechat",
                isSelected: defaultPaymentMethod == PaymentMethodIdentifiers.wechat
            )
        ]
    }

    /// 刷新UI
    private func refreshUI() {
        configureOrderListItems()
    }
}
// MARK: - Address Management
extension ProductOrderConfirmation​Controller {

    /// 创建地址项配置
    func createLocationItem(from addressModel: ProductAddressModel) -> ListItemConfig {
        // 检查是否有选中的地址
        if let selectedAddress = addressModel.selectedAddress() {
            // 有选中的地址，显示地址信息
            return ListItemConfig(type: .orderLocation, identifier: "orderLocation", data: addressModel)
        } else {
            // 没有选中的地址，显示"请填写收货地址"
            return ListItemConfig(type: .select, identifier: "baselist_location", placeholder: "请填写收货地址", iconString: "baselist_location")
        }
    }

    /// 显示新增地址控制器
    func showAddNewAddressController() {
        let editLocationController = EditLocationController()
        // 设置为新增模式，传入空数据
        editLocationController.dic = [:]
        editLocationController.isEditMode = false

        // 监听地区选择事件
        editLocationController.pushtoSelectLocationPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] addressData in
                self?.handleLocationSelection(addressData: addressData, editController: editLocationController)
            }
            .store(in: &cancellables)

        // 监听地址保存事件
        editLocationController.addressSavePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] addressData in
                self?.createNewAddress(addressData: addressData)
            }
            .store(in: &cancellables)

        customPresent(editLocationController, animated: true)
    }

    /// 处理地区选择
    private func handleLocationSelection(addressData: [String: Any], editController: EditLocationController) {
        // 保存当前的地址数据
        dic = addressData

        // 关闭编辑地址页面，跳转到地区选择页面
        editController.dismiss(animated: true) { [weak self] in
            self?.pushToSelectLocationForNewAddress()
        }
    }

    /// 跳转到地区选择页面（新增地址流程）
    private func pushToSelectLocationForNewAddress() {
        let locationController = ProductSelectLocationController()

        // 监听地区选择完成事件
        locationController.locationSelectedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (province, city, district, street) in
                self?.handleLocationSelectedForNewAddress(province: province, city: city, district: district, street: street)
            }
            .store(in: &cancellables)

        pushVcHiddenTabBar(locationController, animated: true)
    }

    /// 处理地区选择完成（新增地址流程）
    private func handleLocationSelectedForNewAddress(province: RegionListItemModel, city: RegionListItemModel?, district: RegionListItemModel?, street: RegionListItemModel?) {
        // 构建地区字符串
        var regionString = province.name
        if let city = city, city.name != province.name {
            regionString += city.name
        }
        if let district = district {
            regionString += district.name
        }
        if let street = street {
            regionString += street.name
        }

        // 更新地区信息
        dic["所在地区"] = regionString
        dic["province"] = province.name
        dic["province_code"] = province.code

        if let city = city {
            dic["city"] = city.name
            dic["city_code"] = city.code
        }

        if let district = district {
            dic["district"] = district.name
            dic["district_code"] = district.code
        }

        if let street = street {
            dic["street"] = street.name
            dic["street_code"] = street.code
        }

        // 返回到订单确认页面，然后重新弹出编辑地址页面
        navigationController?.popViewController(animated: true)

        // 延迟一点时间再弹出EditLocationController，确保页面切换完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            self?.showEditLocationControllerWithLocationData()
        }
    }

    /// 显示带有地区数据的编辑地址控制器
    private func showEditLocationControllerWithLocationData() {
        let editLocationController = EditLocationController()
        editLocationController.dic = dic
        editLocationController.isEditMode = false

        // 监听地区选择事件（如果用户想重新选择地区）
        editLocationController.pushtoSelectLocationPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] addressData in
                self?.handleLocationSelection(addressData: addressData, editController: editLocationController)
            }
            .store(in: &cancellables)

        // 监听地址保存事件
        editLocationController.addressSavePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] addressData in
                self?.createNewAddress(addressData: addressData)
            }
            .store(in: &cancellables)

        customPresent(editLocationController, animated: true)
    }

    /// 创建新地址
    private func createNewAddress(addressData: [String: Any]) {
        // 验证必传参数
        guard let recipientName = addressData["recipient_name"] as? String, !recipientName.isEmpty,
              let phone = addressData["phone"] as? String, !phone.isEmpty,
              let province = addressData["province"] as? String, !province.isEmpty,
              let provinceCode = addressData["province_code"] as? String, !provinceCode.isEmpty,
              let city = addressData["city"] as? String, !city.isEmpty,
              let cityCode = addressData["city_code"] as? String, !cityCode.isEmpty,
              let district = addressData["district"] as? String, !district.isEmpty,
              let districtCode = addressData["district_code"] as? String, !districtCode.isEmpty,
              let street = addressData["street"] as? String, !street.isEmpty,
              let streetCode = addressData["street_code"] as? String, !streetCode.isEmpty else {
            print("创建地址失败：缺少必传参数")
            return
        }

        // 构建创建地址的参数
        var params = addressData
        params["type"] = 1 // 收货地址（必传）

        viewModel?.createAddress(addressData: params) { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    print("地址创建成功")
                    // 创建成功后重新获取地址列表并刷新UI
                    self?.refreshAddressList()
                } else {
                    print("地址创建失败")
                }
            }
        }
    }

    /// 刷新地址列表
    private func refreshAddressList() {
        // 重新获取地址列表
        viewModel?.fetchLocationList()
        // 注意：UI刷新会通过setupBindings中的监听自动触发
    }

    /// 处理地址选择
    func handleAddressSelected(_ selectedAddress: ProductAddressItemModel) {
        print("订单确认页面收到选中的地址: \(selectedAddress.recipient_name)")

        // 更新ViewModel中的地址数据
        updateAddressSelection(selectedAddress)
    }

    /// 更新地址选择状态
    private func updateAddressSelection(_ selectedAddress: ProductAddressItemModel) {
        guard var addressModel = viewModel?.productAddressModel else { return }

        // 清除所有地址的选中状态
        addressModel.default_address.isSelect = false
        for i in 0..<addressModel.address_list.count {
            addressModel.address_list[i].isSelect = false
        }

        // 设置选中的地址
        if selectedAddress.id == addressModel.default_address.id {
            // 选中的是原地址
            addressModel.default_address.isSelect = true
        } else {
            // 选中的是地址列表中的某个地址
            if let index = addressModel.address_list.firstIndex(where: { $0.id == selectedAddress.id }) {
                addressModel.address_list[index].isSelect = true
            }
        }

        // 更新ViewModel中的数据，这会触发UI自动刷新
        viewModel?.productAddressModel = addressModel
    }

    /// 更新所有数据模型中的地址信息
    func updateAddressModelInAllData(_ addressModel: ProductAddressModel) {
        // 更新productDetailModel中的地址数据
        productDetailModel?.productAddressModel = addressModel

        // 更新detailModel中的地址数据
        detailModel?.productAddressModel = addressModel
    }

    /// 跳转到地区选择页面
    func pushToSelectLocation() {
        let vc = ProductSelectLocationController()
        pushVcHiddenTabBar(vc, animated: true)
        vc.locationSelectedPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] (a,b,c,d) in
                guard let self = self else { return }
                let editLocationController = EditLocationController()
                editLocationController.dic = self.dic
                editLocationController.dic["所在地区"] = a.name + (b?.name ?? "") + (c?.name ?? "") + (d?.name ?? "")
                customPresent(editLocationController, animated: true)
            }
            .store(in: &cancellables)
    }
}
// MARK: - Payment Management
extension ProductOrderConfirmation​Controller {

    /// 处理支付方式选择
    func handlePaymentMethodSelection(_ selectedIdentifier: String) {
        print("选择的支付方式: \(selectedIdentifier)")

        // 如果选择余额支付，检查余额是否充足
        if selectedIdentifier == PaymentMethodIdentifiers.balance {
            let totalAmount = calculateTotalAmount()
            guard let viewModel = viewModel, viewModel.isBalanceSufficient(orderAmount: totalAmount) else {
                // 余额不足，显示提示并恢复UI状态
                MBProgressHUD.showPrompt("余额不足", in: view)

                // 恢复到当前ViewModel中的支付方式选择状态
                let currentPaymentMethod = self.viewModel?.selectedPaymentMethod ?? PaymentMethodIdentifiers.alipay
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                    self?.restorePaymentMethodSelection(currentPaymentMethod)
                }
                return
            }
        }

        // 更新ViewModel中的支付方式
        viewModel?.updatePaymentMethod(selectedIdentifier)

        switch selectedIdentifier {
        case PaymentMethodIdentifiers.balance:
            print("选择了余额支付")
        case PaymentMethodIdentifiers.alipay:
            print("选择了支付宝支付")
        case PaymentMethodIdentifiers.wechat:
            print("选择了微信支付")
        default:
            break
        }
    }

    /// 恢复支付方式选择状态
    /// - Parameter paymentMethod: 要恢复的支付方式标识符
    private func restorePaymentMethodSelection(_ paymentMethod: String) {
        // 直接设置BaseListView中对应项的选中状态
        listView.setSingleSelectItem(identifier: paymentMethod, selected: true)
    }

    /// 显示组合支付对话框
    func showCombinedPaymentDialog() {
        let combinedPaymentController = CombinedPaymentController()

        // 设置订单总金额
        let totalAmount = calculateTotalAmount()
        combinedPaymentController.setTotalAmount(totalAmount)

        // 设置可用余额（这里应该从用户信息或接口获取）
        combinedPaymentController.setAvailableBalance(viewModel?.walletSummary?.available_amount ?? 0.0) // 示例金额

        // 监听支付完成事件
        combinedPaymentController.paymentCompletedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] paymentInfo in
                self?.handleCombinedPaymentCompleted(
                    walletAmount: paymentInfo.walletAmount,
                    thirdPartyAmount: paymentInfo.thirdPartyAmount,
                    paymentType: paymentInfo.paymentType
                )
            }
            .store(in: &cancellables)

        customPresent(combinedPaymentController, animated: true)
    }

    /// 处理组合支付完成
    private func handleCombinedPaymentCompleted(walletAmount: Float, thirdPartyAmount: Float, paymentType: String) {
        // 更新ViewModel中的支付金额
        viewModel?.updatePaymentAmounts(walletAmount: walletAmount, thirdPartyAmount: thirdPartyAmount)

        // 更新支付方式为组合支付
        viewModel?.updatePaymentMethod(PaymentMethodIdentifiers.combine)
        viewModel?.updateCombineThirdPartyPaymentMethod(paymentType)
        //直接下单
        handlePaymentButtonClick()

        print("组合支付设置完成 - 余额: ¥\(walletAmount), 第三方: ¥\(thirdPartyAmount), 方式: \(paymentType)")
    }

    /// 计算订单总金额
    func calculateTotalAmount() -> Float {
        if let serviceModel = detailModel {
            let servicePrice = Float(serviceModel.price) ?? 0.0
            let discountAmount = Float(viewModel?.couponModel?.getCurrentSelectedPrice() ?? "0") ?? 0.0
            return servicePrice - discountAmount
        } else if let productModel = productDetailModel {
            let productPrice = Float(productModel.getcurrentQuantityPrice()) ?? 0.0
            let discountAmount = Float(viewModel?.couponModel?.getCurrentSelectedPrice() ?? "0") ?? 0.0
            return productPrice - discountAmount
        }
        return 0.0
    }

    /// 刷新支付方式UI
    private func refreshPaymentMethodUI() {
        configureOrderListItems()
    }

    /// 更新支付方式UI显示
    func updatePaymentMethodUI(_ paymentMethod: String) {
        // 这里可以根据支付方式更新UI显示
        // 比如更新按钮状态、显示/隐藏相关控件等
        print("更新支付方式UI: \(paymentMethod)")
    }
}

// MARK: - Coupon Management
extension ProductOrderConfirmation​Controller {

    /// 处理优惠券选择完成
    func handleCouponSelectionCompleted() {
        // 优惠券选择完成，可以从viewModel的couponModel中获取当前选中的优惠券
        if let selectedCoupon = viewModel?.couponModel?.getCurrentSelectedCoupon() {
            print("选中的优惠券: \(selectedCoupon.title)")
            print("优惠券金额: ¥\(selectedCoupon.amount)")
        } else {
            print("没有选中任何优惠券")
        }

        // 更新UI显示选中的优惠券信息
        configureOrderListItems()
    }
}

// MARK: - Order Management
extension ProductOrderConfirmation​Controller {

    /// 处理支付流程状态变化
    /// - Parameter state: 支付流程状态
    private func handlePaymentFlowStateChange(_ state: PaymentFlowState) {
        switch state {
        case .idle:
            hideLoadingIndicator()

        case .creatingOrder:
            showLoadingIndicator(text: "创建订单中...")

        case .orderCreated(let orderId):
            print("订单创建成功，订单ID: \(orderId)")

        case .initiatingPayment:
            showLoadingIndicator(text: "获取支付参数中...")

        case .waitingThirdPartyPayment:
            hideLoadingIndicator()
            handleWaitingThirdPartyPayment()

        case .notifyingPaymentSuccess:
            showLoadingIndicator(text: "处理支付结果中...")

        case .paymentCompleted(let orderId):
            hideLoadingIndicator()
            showPaymentSuccessAndNavigate(orderId: orderId)

        case .paymentFailed(let error):
            hideLoadingIndicator()
            handlePaymentFailed(error: error)
        }
    }



    /// 跳转到订单详情页面
    private func pushToOrderDetail(orderId: Int) {
        // 获取当前的导航控制器
        guard let currentVC = UIViewController.getCurrentViewController(),
              let navigationController = currentVC.navigationController else {
            return
        }

        let orderDetailController = ProductOrderDetailController()
        orderDetailController.orderId = orderId
        pushVc(orderDetailController, animated: true)
    }

    // MARK: - 支付流程状态处理

    /// 处理等待第三方支付状态
    private func handleWaitingThirdPartyPayment() {
        guard let viewModel = viewModel,
              let orderId = getOrderIdFromCreateResult() else {
            showErrorAlert(message: "获取订单信息失败")
            return
        }

        // 根据支付方式调用相应的第三方支付
        viewModel.processThirdPartyPayment(orderId: orderId)
    }

    /// 处理支付失败
    /// - Parameter error: 错误信息
    private func handlePaymentFailed(error: String) {
        // 显示错误提示
        showErrorAlert(message: error)

        // 如果有订单ID，跳转到订单详情
        if let orderId = getOrderIdFromCreateResult() {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
                self?.navigateToOrderDetail(orderId: orderId)
            }
        }
    }

    /// 从创建订单结果中获取订单ID
    /// - Returns: 订单ID
    private func getOrderIdFromCreateResult() -> Int? {
        guard let result = viewModel?.createOrderResult else { return nil }

        if !result.order_ids.isEmpty {
            return result.order_ids.first
        } else if result.order_id > 0 {
            return result.order_id
        }

        return nil
    }


    /// 显示支付成功提示并跳转
    /// - Parameter orderId: 订单ID
    private func showPaymentSuccessAndNavigate(orderId: Int) {
        let alert = UIAlertController(title: "支付成功", message: "订单支付成功，即将跳转到订单详情", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            self?.navigateToOrderDetail(orderId: orderId)
        })
        present(alert, animated: true)
    }

    /// 跳转到订单详情（单个订单）
    /// - Parameter orderId: 订单ID
    private func navigateToOrderDetail(orderId: Int) {
        // 先返回到顶层控制器
        navigationController?.popToRootViewController(animated: true)

        // 延迟一点时间确保返回完成，然后跳转到订单详情
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            self?.pushToOrderDetail(orderId: orderId)
        }
    }
}






extension ProductOrderConfirmation​Controller: TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        // 处理立即支付按钮点击
        handlePaymentButtonClick()
    }

    /// 处理支付按钮点击
    private func handlePaymentButtonClick() {
        // 验证必要信息
        guard let viewModel = viewModel else {
            showErrorAlert(message: "系统错误，请重试")
            return
        }

        // 验证地址选择
        guard viewModel.productAddressModel?.selectedAddress() != nil else {
            showErrorAlert(message: "请选择收货地址")
            return
        }

        // 显示加载状态
        showLoadingIndicator()

        // 开始支付流程
        viewModel.startPaymentFlow(serviceModel: detailModel, productModel: productDetailModel)
    }

    /// 显示错误提示
    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    /// 显示加载指示器
    /// - Parameter text: 加载文本
    private func showLoadingIndicator(text: String = "处理中...") {
        MBProgressHUD.showLoading(in: self.view, text: text)
    }

    /// 隐藏加载指示器
    private func hideLoadingIndicator() {
        MBProgressHUD.hide(for: self.view, animated: true)
    }
}

extension ProductOrderConfirmation​Controller:BaseListViewDelegate{

    func listViewUpdate(_ listView: BaseListView, with data: Any?) {
        // 处理支付方式选择更新
        if let selectedPaymentIdentifier = data as? String {
            handlePaymentMethodSelection(selectedPaymentIdentifier)
        }
    }

    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        //没有地址时，新增地址，跳出弹窗
        if config.identifier == "baselist_location"{
            showAddNewAddressController()
        }
        if config.identifier == PaymentMethodIdentifiers.combine{
            showCombinedPaymentDialog()
        }
        //跳转地址列表
        if config.identifier == "orderLocation"{
            let vc = SelectAdressController()

            // 传递当前的地址数据给SelectAddressController
            if let currentAddressModel = viewModel?.productAddressModel {
                vc.setAddressModel(currentAddressModel)
            }

            // 监听地址选择事件
            vc.addressSelectedPublisher
                .receive(on: DispatchQueue.main)
                .sink { [weak self] selectedAddress in
                    self?.handleAddressSelected(selectedAddress)
                }
                .store(in: &cancellables)

            // 监听地址数据更新事件
            vc.addressDataUpdatedPublisher
                .receive(on: DispatchQueue.main)
                .sink { [weak self] _ in
                    // 地址数据已更新，重新获取最新数据
                    self?.viewModel?.fetchLocationList()
                }
                .store(in: &cancellables)

            pushVc(vc, animated: true)
        }
        //选择优惠卷
        if config.identifier == "orderConfirmation"{
            let vc = CouponSelectController()
            // 传递优惠券数据
            if let couponModel = viewModel?.couponModel {
                vc.setCouponModel(couponModel)
            }

            // 监听优惠券选择结果
            vc.couponSelectedPublisher
                .receive(on: RunLoop.main)
                .sink { [weak self] _ in
                    guard let self = self else { return }
                    // 获取更新后的优惠券模型并更新到viewModel
                    if let updatedCouponModel = vc.getUpdatedCouponModel() {
                        self.viewModel?.couponModel = updatedCouponModel
                        // 同时更新productDetailModel中的coupon数据
                        self.productDetailModel?.coupon = updatedCouponModel
                    }
                    // 优惠券选择完成，更新UI
                    self.handleCouponSelectionCompleted()
                }
                .store(in: &cancellables)

            customPresent(vc, animated: true)
        }
    }
}
