//
//  ProductDetailController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/4.
//

import UIKit
import Combine

class ProductDetailController: BaseViewController {
    
    // MARK: - 属性
    
    private let viewModel: ProductViewModel
    private let postId: String
    private let seller_id: String
    // 区域类型
    private enum SectionType: Int, CaseIterable {
        case product = 0
        case commentTitle
        case comments
        case recommend
    }
    
    // MARK: - UI组件
    
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .grouped)
        tv.showsVerticalScrollIndicator = false
        tv.delegate = self
        tv.dataSource = self
        tv.separatorStyle = .none
        tv.estimatedRowHeight = 100
        tv.rowHeight = UITableView.automaticDimension
        tv.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 60, right: 0)
        
        // 注册Cell
        tv.register(DetailProductInfoTableCell.self, forCellReuseIdentifier: "DetailProductInfoTableCell")
        tv.register(DetailRecommendTableCell.self, forCellReuseIdentifier: "DetailRecommendTableCell")
        tv.register(DetailCommentTitleTableCell.self, forCellReuseIdentifier: "DetailCommentTitleTableCell")
        tv.register(ProductDetailCommentItemTableCell.self, forCellReuseIdentifier: "ProductDetailCommentItemTableCell")
        
        return tv
    }()
    
    private lazy var toolBar = BaseTabToolBar().then {
        $0.delegate = self
    }
    private lazy var navBar = DetailNavBarView().then{
        $0.delegate = self
        $0.backItem.setImage(UIImage(named: "home_product_detail_cart_back"), for: .normal)
        $0.configProduct()
    }
    // MARK: - 初始化
    
    init(postId: String,seller_id:String) {
        self.postId = postId
        self.seller_id = seller_id
        self.viewModel = ProductViewModel(postId: postId, pageType: nil)
        self.viewModel.advertiserId = seller_id
        super.init()
    }
    
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
        loadData()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        // 设置导航栏
        self.fd_prefersNavigationBarHidden = true

        // 配置 toolBar
        toolBar.configureLeftMultipleRightFull(leftItems: [
            ToolBarButtonItem(normalImage: UIImage(named:"home_product_detail_cart"),
                              selectedImage:  UIImage(named:"home_product_detail_cart"),
                              title: "购物车")
        ], rightButtonTitle: "选择规格")
        toolBar.setRightButtonGradient(colors: [UIColor(hexString: "FF5D48")!,UIColor(hexString: "FF3434")!], direction: .leftToRight)

        view.addSubview(tableView)
        view.addSubview(navBar)
        view.addSubview(toolBar)
        
        navBar.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalNavBarHeight)
        }
        toolBar.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
        tableView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(toolBar.snp.top)
            
        }
    }
    
    // MARK: - 数据绑定

    override func setupBindings() {
        // 监听详情数据变化
        viewModel.$productDetailData
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)

        // 监听购物车操作状态
        viewModel.$addToCartState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.handleAddToCartState(state)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 数据加载

    private func loadData() {
        viewModel.fetchProductDetailData()
    }

    // MARK: - 购物车相关方法

    /// 处理购物车操作状态变化
    private func handleAddToCartState(_ state: RequestState) {
        switch state {
        case .idle:
            break
        case .loading:
            MBProgressHUD.showLoading(in: self.view, text: "正在添加到购物车...")
        case .success:
            MBProgressHUD.hide(for: self.view, animated: true)
            MBProgressHUD.showSuccess("加入购物车成功", in: self.view)
        case .failure(let message):
            MBProgressHUD.hide(for: self.view, animated: true)
            showErrorAlert(message)
        }
    }

    /// 显示错误提示
    private func showErrorAlert(_ message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

}

// MARK: - UITableViewDataSource, UITableViewDelegate

extension ProductDetailController: UITableViewDataSource, UITableViewDelegate {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return SectionType.allCases.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let detailData = viewModel.productDetailData else { return 0 }
        guard let sectionType = SectionType(rawValue: section) else { return 0 }
        switch sectionType {
        case .product:
            return 1
        case  .commentTitle:
            return  detailData.product.recent_reviews.isEmpty ? 0 : 1
        case .comments:
            return  detailData.product.recent_reviews.count
        case .recommend:
            return detailData.product.recommend_list.isEmpty ? 0 : 1
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let detailData = viewModel.productDetailData else {
            return UITableViewCell()
        }
        
        guard let sectionType = SectionType(rawValue: indexPath.section) else {
            return UITableViewCell()
        }
        
        switch sectionType {
        case .product:
            let cell = tableView.dequeueReusableCell(withIdentifier: "DetailProductInfoTableCell", for: indexPath) as! DetailProductInfoTableCell
            cell.delegate = self
            cell.configureProduct(with: detailData)
            return cell
            
        case .commentTitle:
            let cell = tableView.dequeueReusableCell(withIdentifier: "DetailCommentTitleTableCell", for: indexPath) as! DetailCommentTitleTableCell
            cell.delegate = self
            cell.separatorLine.isHidden = true
            cell.configure(with: detailData.product.reviews_count)
            return cell
            
        case .comments:
            let cell = tableView.dequeueReusableCell(withIdentifier: "ProductDetailCommentItemTableCell", for: indexPath) as! ProductDetailCommentItemTableCell
            let comment = detailData.product.recent_reviews[indexPath.row]
            cell.configure(with: comment)
            return cell
        case .recommend:
            let cell = tableView.dequeueReusableCell(withIdentifier: "DetailRecommendTableCell", for: indexPath) as! DetailRecommendTableCell
            // 最多只显示4个推荐商品
            let limitedSkuList = Array(detailData.product.recommend_list.prefix(4))
            cell.configure(with: limitedSkuList, hideMoreButton: true)
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 0.01
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 0.01
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        return UIView()
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return UIView()
    }
}

// MARK: - DetailBottomToolBarDelegate

extension ProductDetailController: TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickLeftButtonAt index: Int, item: ToolBarButtonItem) {
        // 处理左侧按钮点击，购物车按钮点击
        if index == 0 && item.title == "购物车" {
            viewModel.addToCart()
        }
    }

    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        let productselectVc = ProductSelectController()
        productselectVc.detailModel = viewModel.productDetailData
        productselectVc.$detailModel
            .receive(on: RunLoop.main)
            .compactMap { $0 }
            .sink { [weak self] updatedModel in
                guard let self = self else { return }
                self.viewModel.productDetailData = updatedModel
            }
            .store(in: &cancellables)
        productselectVc.selectCompletePublisher
            .receive(on: RunLoop.main)
            .sink { _ in
                
            } receiveValue: {[weak self] _ in
                self?.pushToOrderConfirm()
            }.store(in: &cancellables)
        customPresent(productselectVc, animated: true)
    }
    
    func pushToOrderConfirm(){
        guard let model = viewModel.productDetailData else { return }
        let orderConfirmVc = ProductOrderConfirmation​Controller()
        orderConfirmVc.productDetailModel = model
        pushVc(orderConfirmVc, animated: true)
    }
}
extension ProductDetailController:DetailNavBarViewDelegate{
    func navBarViewDidTapBack(_ navBarView: DetailNavBarView) {
        self.navigationController?.popViewController(animated: true)
    }
    
    func navBarViewDidTapUser(_ navBarView: DetailNavBarView) {
        
    }
    
    func navBarViewDidTapChat(_ navBarView: DetailNavBarView) {
        
    }
    
    func navBarViewDidTapFollow(_ navBarView: DetailNavBarView) {
        
    }
    
    func navBarViewDidTapMore(_ navBarView: DetailNavBarView) {
        
    }
    
    
}
extension ProductDetailController:DetailCommentTitleTableCellDelegate{
    func moreCommentButtonClick() {
        let commentListVC = ProductCommentController()
        commentListVC.detailModel = viewModel.productDetailData
        customPresent(commentListVC, animated: true)
    }
}

// MARK: - DetailProductInfoTableCellDelegate
extension ProductDetailController: DetailProductInfoTableCellDelegate {
    func didSelectVariant(at selectedIndex: Int) {
        // 在控制器中处理变体选择逻辑
        guard var detailModel = viewModel.productDetailData else { return }
        
        // 使用model的selectVariant方法更新选中状态
        detailModel = detailModel.selectVariant(at: selectedIndex)
        
        // 更新ViewModel中的数据
        viewModel.productDetailData = detailModel
        
        // 刷新对应的cell（只刷新商品信息部分）
        let indexPath = IndexPath(row: 0, section: SectionType.product.rawValue)
        if let cell = tableView.cellForRow(at: indexPath) as? DetailProductInfoTableCell {
            cell.configureProduct(with: detailModel)
        }
    }
}
