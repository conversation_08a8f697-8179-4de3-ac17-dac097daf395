//
//  ProductSellingPurchasedListController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/16.
//

import UIKit
import SnapKit
import Combine
import JXSegmentedView
import MJRefresh

class ProductSellingPurchasedListController: JXBaseViewController {
    
    // MARK: - Properties
    
    /// ViewModel
    private var viewModel: ProductSellingPurchasedViewModel!
    
    /// 订单类型（从父控制器传入）
    var orderType: OrderViewType = .selling
    
    /// 状态筛选（从父控制器传入）
    var statusFilter: OrderStatusFilter = .all
    
    // MARK: - UI Components
    
    /// 主TableView
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = UIColor(hexString: "F3F6F7")
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(ProductOrderTableCell.self, forCellReuseIdentifier: NSStringFromClass(ProductOrderTableCell.self))
        tableView.register(ProductOrderSellerHeaderView.self, forHeaderFooterViewReuseIdentifier: NSStringFromClass(ProductOrderSellerHeaderView.self))
        tableView.register(ProductOrderSellerFooterView.self, forHeaderFooterViewReuseIdentifier: NSStringFromClass(ProductOrderSellerFooterView.self))
        tableView.estimatedRowHeight = 200
        tableView.rowHeight = UITableView.automaticDimension
        tableView.sectionHeaderHeight = UITableView.automaticDimension
        tableView.sectionFooterHeight = UITableView.automaticDimension
        tableView.estimatedSectionHeaderHeight = 56
        return tableView
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupViewModel()
        setupUI()
        setupBindings()
        setupRefresh()
        
        // 初始加载数据
        viewModel.refreshData()
    }
    
    // MARK: - Setup Methods
    
    private func setupViewModel() {
        viewModel = ProductSellingPurchasedViewModel(orderType: orderType)
        viewModel.setStatusFilter(statusFilter)
    }
    
    private func setupUI() {
        
        view.addSubview(tableView)
        
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    override func setupBindings() {
        // 订单列表数据绑定
        viewModel.$orders
            .receive(on: DispatchQueue.main)
            .sink { [weak self] orders in
                self?.updateUI(with: orders)
            }
            .store(in: &cancellables)

        // 绑定刷新状态
        viewModel.bindRefreshState(to: tableView)
            .store(in: &cancellables)
    }

    private func setupRefresh() {
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: tableView, with: viewModel)
    }
    
    // MARK: - Private Methods

    private func updateUI(with orders: [ProductOrderModel]) {
        tableView.reloadData()

    }
    
    /// 设置状态筛选
    func setStatusFilter(_ filter: OrderStatusFilter) {
        statusFilter = filter
        viewModel.setStatusFilter(filter)
        // 刷新数据
        viewModel.refreshData()
    }
}

// MARK: - UITableViewDataSource

extension ProductSellingPurchasedListController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return viewModel.orders.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.orders[section].items.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: NSStringFromClass(ProductOrderTableCell.self), for: indexPath) as? ProductOrderTableCell else {
            return UITableViewCell()
        }

        let order = viewModel.orders[indexPath.section]
        let item = order.items[indexPath.row]
        let isLast = indexPath.row == order.items.count - 1
        cell.configure(with: order, item: item, isLast: isLast)
        return cell
    }
}

// MARK: - UITableViewDelegate

extension ProductSellingPurchasedListController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        let order = viewModel.orders[indexPath.section]
        let orderDetailController = ProductOrderDetailController()
        orderDetailController.orderId = order.order_id
        pushVc(orderDetailController, animated: true)

    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForHeaderInSection section: Int) -> CGFloat {
        return 56
    }

    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        let order = viewModel.orders[section]
        // 如果没有按钮，footer高度为12，如果有按钮，按钮高度28 + 上下距离12*2 = 52
        return order.action_buttons.isEmpty ? 12 : 52
    }

    func tableView(_ tableView: UITableView, estimatedHeightForFooterInSection section: Int) -> CGFloat {
        return 52
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        guard let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: NSStringFromClass(ProductOrderSellerHeaderView.self)) as? ProductOrderSellerHeaderView else {
            return nil
        }

        let order = viewModel.orders[section]
        headerView.configure(with: order, orderType: orderType)

        return headerView
    }

    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        guard let footerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: NSStringFromClass(ProductOrderSellerFooterView.self)) as? ProductOrderSellerFooterView else {
            return nil
        }

        let order = viewModel.orders[section]
        footerView.configure(with: order)
        footerView.delegate = self

        return footerView
    }
}

// MARK: - ProductOrderTableCellDelegate

extension ProductSellingPurchasedListController: ProductOrderSellerFooterViewDelegate {
    func footerView(_ footerView: ProductOrderSellerFooterView, didTapButton action: String, order: ProductOrderModel) {
        // 处理按钮点击事件
        handleOrderAction(action, order: order)
    }

    private func handleOrderAction(_ action: String, order: ProductOrderModel) {
        switch action {
        case "pay":
            // 去付款
            print("去付款：\(order.order_sn)")
        case "cancel":
            // 取消订单
            print("取消订单：\(order.order_sn)")
        case "modify_price":
            // 修改价格
            print("修改价格：\(order.order_sn)")
        case "contact_buyer":
            // 联系买家
            print("联系买家：\(order.order_sn)")
        case "contact_seller":
            // 联系卖家
            print("联系卖家：\(order.order_sn)")
        case "ship":
            // 发货
            print("发货：\(order.order_sn)")
        case "confirm_receipt":
            // 确认收货
            print("确认收货：\(order.order_sn)")
        case "review":
            // 评价
            print("评价：\(order.order_sn)")
        case "refund":
            // 申请退款
            print("申请退款：\(order.order_sn)")
        default:
            print("未知操作：\(action)")
        }
    }
}

