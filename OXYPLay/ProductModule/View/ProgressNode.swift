//
//  ProgressNode.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/29.
//

import UIKit
import SnapKit
import Then

/// 进度节点视图
class ProgressNode: UIView {
    
    // MARK: - UI Components
    
    /// 节点图片视图
    private lazy var imageView = UIImageView().then {
        $0.contentMode = .scaleAspectFit
        $0.clipsToBounds = true
    }
    
    // MARK: - Properties
    
    /// 是否已完成
    private var isCompleted: Bool = false
    
    /// 是否进行中
    private var isInProgress: Bool = false
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        // 添加图片视图
        addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 设置默认图片
        updateAppearance()
    }
    
    // MARK: - Public Methods
    
    /// 更新节点状态
    /// - Parameters:
    ///   - isCompleted: 是否已完成
    ///   - isInProgress: 是否进行中
    ///   - isUpcoming: 是否未开始
    func updateState(isCompleted: Bool, isInProgress: Bool, isUpcoming: Bool = false) {
        self.isCompleted = isCompleted
        self.isInProgress = isInProgress
        
        updateAppearance()
    }
    
    // MARK: - Private Methods
    
    /// 更新外观
    private func updateAppearance() {
        if isCompleted {
            // 已完成状态：使用已完成图片
            imageView.image = UIImage(named: "prodcut_order_progress_completed")
        } else {
            // 未完成状态：使用未开始图片
            imageView.image = UIImage(named: "prodcut_order_progress_upcoming")
        }
    }
}
