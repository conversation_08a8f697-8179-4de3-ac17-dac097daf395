//
//  OrderInfoView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/25.
//

import UIKit
import Combine

/// 订单信息视图
class OrderInfoView: BaseView {
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 16
        view.masksToBounds = true
        return view
    }()
   
    private lazy var stackView: UIStackView = {
        let view = UIStackView()
        view.axis = .vertical
        view.spacing = 12
        return view
    }()
    //添加退款按钮
    lazy var refundButton = BaseButton().then{
        $0.backgroundColor = UIColor(hexString: "788092", transparency: 0.08)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.setTitle("去退款", for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 12, weight: .regular)
        $0.isRounded = true
    }
    lazy var showCloseButton = BaseButton().then{
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 11, weight: .regular)
        $0.imagePosition = .right
        $0.setTitle("展开", for: .selected)
        $0.setTitle("收起", for: .normal)
        $0.setImage(UIImage(named: "prodcut_order_toparrow_back"), for: .normal)
        $0.setImage(UIImage(named: "prodcut_order_bottomarrow_back"), for: .selected)

    }
    override func configUI() {
        self.addSubview(contentView)
        contentView.addSubview(stackView)
    }

    override func setupBindings() {
        // 基类的绑定设置
        super.setupBindings()
    }

    override func configLayout() {
        contentView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(-0)
            make.top.equalTo(0)
        }
        stackView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(-12)
            make.top.equalTo(12)
        }
    }
    // MARK: - 属性

    /// 当前订单模型
    private var currentModel: OrderDetailModel?

    /// 是否展开详细信息
    private var isExpanded = false

    /// 详细信息视图数组（用于展开/收起）
    private var detailViews: [UIView] = []

    /// 价格详情视图数组（成交价展开/收起控制）
    private var priceDetailViews: [UIView] = []

    /// 分隔线视图
    private var separatorLine: UIView?

    /// 价格详情容器视图（商品总价、运费、平台优惠券）
    private lazy var priceDetailContainerView = UIStackView().then {
        $0.axis = .vertical
        $0.spacing = 12
        $0.isHidden = false // 初始展开
    }

    /// 全部详情容器视图（退款按钮下面的所有内容，包括成交价）
    private lazy var allDetailContainerView = UIStackView().then {
        $0.axis = .vertical
        $0.spacing = 12
        $0.isHidden = false // 初始展开
    }

    /// 成交价视图
    private var dealPriceView: OrderInfoPriceView?

    // MARK: - 配置方法

    func configure(with model: OrderDetailModel) {
        currentModel = model

        // 清空之前的视图
        clearStackView()

        // 添加商品视图
        addOrderItems(model.items, orderType: model.order_type)

        if model.is_refunded_button{
            // 添加退款按钮容器
            addRefundButtonContainer()
        }
        
        // 添加价格信息
        addPriceInfo(model)

        // 设置展开/收起按钮事件
        setupToggleButton()
    }

    // MARK: - 私有方法

    /// 清空堆栈视图
    private func clearStackView() {
        stackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        detailViews.removeAll()
        priceDetailViews.removeAll()
    }

    /// 添加订单商品项
    private func addOrderItems(_ items: [OrderDetailItem], orderType: Int) {
        items.forEach { item in
            let orderItemView = OrderCommonView()
            orderItemView.configModel(model: item, isProduct: orderType == 1)
            stackView.addArrangedSubview(orderItemView)
        }
    }

    /// 添加退款按钮容器
    private func addRefundButtonContainer() {
        let containerView = UIView()
        containerView.addSubview(refundButton)
        refundButton.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.height.equalTo(24)
            make.width.equalTo(72)
        }
        stackView.addArrangedSubview(containerView)
    }



    /// 添加价格信息
    private func addPriceInfo(_ model: OrderDetailModel) {
        // 添加全部详情容器（包含成交价、分隔线和其他详细信息）
        stackView.addArrangedSubview(allDetailContainerView)

        // 创建成交价并添加到全部详情容器
        let dealPrice = createDealPriceView(model)
        dealPriceView = dealPrice
        allDetailContainerView.addArrangedSubview(dealPrice)

        // 添加价格详情容器（商品总价、运费、平台优惠券）
        allDetailContainerView.addArrangedSubview(priceDetailContainerView)
        createPriceDetailViews(model)

        // 创建其他详细信息并添加到全部详情容器
        createAllDetailViews(model)

        // 添加展开/收起按钮
        stackView.addArrangedSubview(showCloseButton)

        // 设置初始状态（全部展开，显示"收起"）
        showCloseButton.isSelected = false // false状态显示"收起"
        
     
    }

    /// 创建成交价视图
    private func createDealPriceView(_ model: OrderDetailModel) -> OrderInfoPriceView {
        let priceView = OrderInfoPriceView()
        priceView.titleButton.setTitle("成交价", for: .normal)
        priceView.contentLabel.text = model.formattedPaidAmount

        // 设置初始状态：价格详情展开，按钮显示收起状态
        priceView.titleButton.isSelected = !priceDetailContainerView.isHidden

        // 绑定点击事件
        priceView.titleButton.tapPublisher
            .sink { [weak self] _ in
                self?.togglePriceDetailExpansion()
            }
            .store(in: &cancellables)

        return priceView
    }

    /// 创建价格详情视图（成交价控制的三个子项）
    private func createPriceDetailViews(_ model: OrderDetailModel) {
       
        // 价格详情配置（只包含成交价控制的三个子项）
        let priceConfigs: [(title: String, value: String, type: PriceViewType)] = [
            ("商品总价", model.formattedFinalAmount, .subPrice),
            ("运费", model.formattedPostageFee, .subPrice),
            ("平台优惠券", formatDiscountAmount(model.discount_amount), .subPrice)
        ]

        // 创建价格详情视图并添加到价格详情容器
        priceConfigs.forEach { config in
            let view = createPriceView(config.title, config.value, config.type)
            priceDetailViews.append(view)
            priceDetailContainerView.addArrangedSubview(view)
        }
    }

    /// 创建全部详细信息视图（分隔线和其他详细信息）
    private func createAllDetailViews(_ model: OrderDetailModel) {
        // 其他信息配置
        let otherConfigs: [(title: String, value: String, type: PriceViewType)] = [
            ("订单编号", model.order_sn, .copy),
            ("交易快照", "", .select),
            ("支付宝交易号", model.alipay_trade_no ?? "", .copy),
            ("微信交易号", model.wechat_trade_no ?? "", .copy),
            ("下单时间", model.created_at, .text),
            ("付款时间", model.paid_at ?? "", .text),
            ("发货时间", model.shipped_at ?? "", .text)
        ]

        // 创建其他详细视图并添加到全部详情容器
        otherConfigs.forEach { config in
            let view = createPriceView(config.title, config.value, config.type)
            if shouldShowView(config.title, config.value) {
                detailViews.append(view)
                allDetailContainerView.addArrangedSubview(view)
            }
        }
    }

    /// 创建价格视图
    private func createPriceView(_ title: String, _ value: String, _ type: PriceViewType) -> UIView {
        switch type {
        case .subPrice:
            let view = OrderInfoPriceSubView()
            view.titleLabel.text = title
            view.contentLabel.text = value

            // 优惠券特殊样式
            if title.contains("优惠") && !value.isEmpty && value != "¥0" {
                view.contentLabel.textColor = UIColor(hexString: "FF0000")!
            }
            return view

        case .copy:
            if title == "交易快照" {
                return OrderInfoSelectView()
            } else {
                let view = OrderInfoCopyView()
                view.titleLabel.text = title
                view.contentLabel.text = value
                setupCopyAction(view, value: value)
                return view
            }

        case .select:
            return OrderInfoSelectView()

        case .text:
            let view = OrderInfoOnlyTextView()
            view.titleLabel.text = title
            view.contentLabel.text = value
            return view
        }
    }

    /// 设置复制功能
    private func setupCopyAction(_ view: OrderInfoCopyView, value: String) {
        view.copyButton.tapPublisher
            .sink { [weak self] _ in
                self?.copyToClipboard(value)
            }
            .store(in: &cancellables)
    }
    /// 复制到剪贴板
    private func copyToClipboard(_ text: String) {
        guard !text.isEmpty else { return }

        UIPasteboard.general.string = text

        // 显示复制成功提示
        if let superview = self.superview {
            MBProgressHUD.showPrompt("已复制到剪贴板", in: superview)
        }
    }

    /// 判断是否应该显示视图
    private func shouldShowView(_ title: String, _ value: String) -> Bool {
        // 交易快照始终显示
        if title == "交易快照" { return true }

        // 其他视图需要有值才显示
        return !value.isEmpty
    }

    /// 格式化优惠金额
    private func formatDiscountAmount(_ amount: Float) -> String {
        return amount > 0 ? String(format: "-¥%.2f", amount) : "¥0"
    }

    /// 设置展开/收起按钮事件
    private func setupToggleButton() {
        showCloseButton.tapPublisher
            .sink { [weak self] _ in
                self?.toggleAllExpansion()
            }
            .store(in: &cancellables)
    }

    /// 切换价格详情展开/收起状态（只控制商品总价、运费、平台优惠券）
    private func togglePriceDetailExpansion() {
        priceDetailContainerView.isHidden.toggle()

        // 更新成交价按钮的选中状态
        if let dealPriceView = dealPriceView {
            // 当价格详情隐藏时，按钮应该显示可以展开的状态
            // 当价格详情显示时，按钮应该显示可以收起的状态
            dealPriceView.titleButton.isSelected = !priceDetailContainerView.isHidden
        }
    }

    /// 切换全部展开/收起状态（控制退款按钮下面的所有内容，包括成交价）
    private func toggleAllExpansion() {
        allDetailContainerView.isHidden.toggle()

        // 更新按钮状态
        // isSelected = false 显示"收起"（normal状态）
        // isSelected = true 显示"展开"（selected状态）
        showCloseButton.isSelected = allDetailContainerView.isHidden
    }


}

// MARK: - 价格视图类型枚举

private enum PriceViewType {
    case subPrice   // 子价格视图
    case copy       // 可复制视图
    case select     // 可选择视图
    case text       // 纯文本视图
}
class OrderInfoPriceView: BaseView {
    lazy var titleButton = BaseButton().then{
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        $0.setImage(UIImage(named: "prodcut_order_toparrow"), for: .selected)
        $0.setImage(UIImage(named: "prodcut_order_bottomarrow"), for: .normal)
        $0.imagePosition = .right

    }
    lazy var contentLabel = UILabel().then{
        $0.textColor = color_2B2C2F
        $0.font = .systemFont(ofSize: 20, weight: .medium)
    }
    override func configUI() {
        self.addSubview(titleButton)
        self.addSubview(contentLabel)
    }
    override func configLayout() {
        titleButton.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
        }
        contentLabel.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.left.greaterThanOrEqualTo(titleButton.snp.right)
        }
    }
}
class OrderInfoPriceSubView: BaseView {
    lazy var titleLabel = UILabel().then{
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.56)
        $0.font = .systemFont(ofSize: 14, weight: .regular)
    }
    lazy var contentLabel = UILabel().then{
        $0.textColor = color_2B2C2F
        $0.font = .systemFont(ofSize: 14, weight: .medium)
    }
    override func configUI() {
        self.addSubview(titleLabel)
        self.addSubview(contentLabel)
    }
    override func configLayout() {
        titleLabel.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(24)
        }
        contentLabel.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.left.greaterThanOrEqualTo(titleLabel.snp.right)
        }
    }
}
class OrderInfoOnlyTextView: BaseView {
    lazy var titleLabel = UILabel().then{
        $0.textColor = color_2B2C2F
        $0.font = .systemFont(ofSize: 14, weight: .regular)
    }
    lazy var contentLabel = UILabel().then{
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.72)
        $0.font = .systemFont(ofSize: 14, weight: .medium)
    }
    override func configUI() {
        self.addSubview(titleLabel)
        self.addSubview(contentLabel)
    }
    override func configLayout() {
        titleLabel.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(0)
        }
        contentLabel.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.left.greaterThanOrEqualTo(titleLabel.snp.right)
        }
    }
}
class OrderInfoSelectView: BaseView {
    lazy var titleLabel = UILabel().then{
        $0.textColor = color_2B2C2F
        $0.font = .systemFont(ofSize: 14, weight: .regular)
        $0.text = "交易快照"
    }
    lazy var selectButton = BaseButton().then{
        $0.setTitleColor(UIColor(hexString: "2B2C2F", transparency: 0.72), for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 14, weight: .regular)
        $0.setTitle("发生交易争议时，可作为判断依据", for: .normal)
        $0.setImage(UIImage(named: "home_product_detail_right_arrow"), for: .normal)
        $0.imagePosition = .right

    }
    override func configUI() {
        self.addSubview(titleLabel)
        self.addSubview(selectButton)
    }
    override func configLayout() {
        titleLabel.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(0)
        }
        selectButton.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.left.greaterThanOrEqualTo(titleLabel.snp.right)
        }
    }
}
class OrderInfoCopyView: BaseView {
    lazy var titleLabel = UILabel().then{
        $0.textColor = color_2B2C2F
        $0.font = .systemFont(ofSize: 14, weight: .regular)
    }
    lazy var copyButton = BaseButton().then{
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 14, weight: .regular)
        $0.setTitle("复制", for: .normal)
    }
    lazy var contentLabel = UILabel().then{
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.72)
        $0.font = .systemFont(ofSize: 14, weight: .regular)
    }
    lazy var line = UIView().then{
        $0.backgroundColor = UIColor(hexString: "2B2C2F", transparency: 0.08)
    }

    override func configUI() {
        self.addSubview(titleLabel)
        self.addSubview(copyButton)
        self.addSubview(contentLabel)
        self.addSubview(line)

    }
    override func configLayout() {
        titleLabel.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(0)
        }
        copyButton.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview()
        }
        line.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalTo(1)
            make.height.equalTo(14)
            make.right.equalTo(copyButton.snp.left).offset(-10)
        }
        contentLabel.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.right.equalTo(line.snp.left).offset(-10)
            make.left.greaterThanOrEqualTo(titleLabel.snp.right)
        }
    }
}
