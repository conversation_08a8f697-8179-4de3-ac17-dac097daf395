//
//  OrderStatusView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/25.
//

import UIKit
import Combine
import SwiftDate

/// 订单状态视图
class OrderStatusView: BaseView {

    // MARK: - UI Components

    /// 主容器StackView
    private lazy var mainStackView = UIStackView().then {
        $0.axis = .vertical
        $0.spacing = 0
        $0.distribution = .fill
    }

    /// 顶部容器（标题和按钮）
    private lazy var topContainerView = UIView()

    /// 状态标题标签
    private lazy var statusTitleButton = BaseButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.imagePosition = .right
        $0.setImage(UIImage(named: "prodcut_order_toparrow_back"), for: .selected)
        $0.setImage(UIImage(named: "prodcut_order_bottomarrow_back"), for: .normal)
    }

    /// 倒计时容器视图
    private lazy var countdownContainerView = UIView()

    /// 倒计时标签
    private lazy var countdownLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12,weight: .regular)
        $0.textColor = color_2B2C2F
        $0.numberOfLines = 0
    }

    /// 进度容器视图
    private lazy var progressContainerView = UIView()

    /// 状态进度视图
    private lazy var progressView = OrderProgressView()

    // MARK: - Properties

    /// 是否展开进度视图
    private var isProgressExpanded = true

    /// 是否显示倒计时
    private var shouldShowCountdown = false

    /// 倒计时定时器
    private var countdownTimer: Timer?

    /// 截止时间
    private var deadlineDate: Date?

    // MARK: - Initialization

    override func configUI() {
        super.configUI()
        setupViewHierarchy()
        setupBindings()
    }

    override func configLayout() {
        super.configLayout()
        // 主StackView约束
        mainStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 12, left: 12, bottom: 0, right: 12))
        }

        // 顶部容器约束
        topContainerView.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(24)
        }

        // 状态标题约束
        statusTitleButton.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.lessThanOrEqualTo(12)
        }


        // 倒计时容器约束
        countdownContainerView.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(0)
        }

        // 倒计时标签约束
        countdownLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.right.bottom.equalToSuperview()
        }

        // 进度容器约束
        progressContainerView.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(0)
        }

        // 进度视图约束
        progressView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(40) // 增加高度以容纳节点和标签
        }
    }

    private func setupViewHierarchy() {
        // 添加主StackView
        addSubview(mainStackView)

        // 设置顶部容器
        topContainerView.addSubview(statusTitleButton)

        // 设置倒计时容器
        countdownContainerView.addSubview(countdownLabel)

        // 设置进度容器
        progressContainerView.addSubview(progressView)

        // 添加到主StackView
        mainStackView.addArrangedSubview(topContainerView)
        mainStackView.addArrangedSubview(countdownContainerView)
        mainStackView.addArrangedSubview(progressContainerView)
    }


    override func setupBindings() {
        super.setupBindings()

        // 绑定展开按钮点击事件
        statusTitleButton.tapPublisher
            .sink { [weak self] _ in
                self?.toggleProgressExpansion()
            }
            .store(in: &cancellables)
    }

    // MARK: - Private Methods

    /// 切换进度视图展开/收起状态
    private func toggleProgressExpansion() {
        isProgressExpanded.toggle()
        statusTitleButton.isSelected.toggle()

        UIView.animate(withDuration: 0.3) {
            self.progressContainerView.isHidden = !self.isProgressExpanded
            self.layoutIfNeeded()
        }
    }

    /// 更新倒计时显示状态
    private func updateCountdownVisibility() {
        countdownContainerView.isHidden = !shouldShowCountdown || countdownLabel.text?.isEmpty == true
    }

    // MARK: - Public Methods

    /// 配置视图数据
    /// - Parameters:
    ///   - orderStatus: 订单状态码
    ///   - statusDetailText: 状态详细文字（status_detail_text）
    ///   - statusDeadline: 截止时间字符串（status_deadline）
    ///   - statusMap: 状态映射数组（可选，用于动态显示进度）
    func configure(orderStatus: Int,
                  statusDetailText: String,
                  statusDeadline: String = "",
                  statusMap: [OrderDetailStatus] = []) {

        // 设置状态文字和颜色
        statusTitleButton.setTitle(statusDetailText, for: .normal)

        // 处理倒计时
        setupCountdown(deadline: statusDeadline, detailText: statusDetailText)

        progressView.configure(currentStep: orderStatus, statusMap: statusMap)

        // 重置展开状态
        isProgressExpanded = false
        progressContainerView.isHidden = true
    }



    /// 设置倒计时
    /// - Parameters:
    ///   - deadline: 截止时间字符串（格式：2025-07-26 11:11:11）
    ///   - detailText: 状态详细文字
    private func setupCountdown(deadline: String, detailText: String) {
        // 停止之前的倒计时
        stopCountdown()

        // 检查截止时间是否有效
        guard !deadline.isEmpty else {
            // 截止时间为空，隐藏倒计时容器
            shouldShowCountdown = false
            updateCountdownVisibility()
            return
        }

        // 使用SwiftDate解析时间，与项目中其他地方保持一致
        guard let deadlineDate = deadline.toDate("yyyy-MM-dd HH:mm:ss")?.date else {
            // 时间格式错误，隐藏倒计时容器
            shouldShowCountdown = false
            updateCountdownVisibility()
            return
        }

        // 检查时间是否已过期
        let now = Date()
        if deadlineDate <= now {
            // 时间已过期，隐藏倒计时容器
            shouldShowCountdown = false
            updateCountdownVisibility()
            return
        }

        self.deadlineDate = deadlineDate
        shouldShowCountdown = true

        // 立即更新一次倒计时显示
        updateCountdownDisplay(detailText: detailText)

        // 启动定时器，每秒更新一次
        countdownTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateCountdownDisplay(detailText: detailText)
        }

        updateCountdownVisibility()
    }

    /// 更新倒计时显示
    /// - Parameter detailText: 状态详细文字
    private func updateCountdownDisplay(detailText: String) {
        guard let deadlineDate = deadlineDate else {
            stopCountdown()
            return
        }

        let now = Date()
        let timeInterval = deadlineDate.timeIntervalSince(now)

        // 如果时间已过，停止倒计时并隐藏容器
        if timeInterval <= 0 {
            stopCountdown()
            shouldShowCountdown = false
            updateCountdownVisibility()
            return
        }

        // 格式化剩余时间
        let timeString = formatRemainingTime(timeInterval)
        let fullText = "\(timeString)\(detailText)"

        // 设置富文本样式
        setCountdownText(fullText, timeString: timeString)
    }

    /// 格式化剩余时间
    /// - Parameter timeInterval: 时间间隔（秒）
    /// - Returns: 格式化的时间字符串
    private func formatRemainingTime(_ timeInterval: TimeInterval) -> String {
        let totalSeconds = Int(timeInterval)
        let days = totalSeconds / (24 * 3600)
        let hours = (totalSeconds % (24 * 3600)) / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60

        if days > 0 {
            return "\(days)天\(hours)小时\(minutes)分\(seconds)秒"
        } else if hours > 0 {
            return "\(hours)小时\(minutes)分\(seconds)秒"
        } else if minutes > 0 {
            return "\(minutes)分\(seconds)秒"
        } else {
            return "\(seconds)秒"
        }
    }

    /// 设置倒计时富文本
    /// - Parameters:
    ///   - text: 完整文本
    ///   - timeString: 时间部分文本
    private func setCountdownText(_ text: String, timeString: String) {
        let attributedString = NSMutableAttributedString(string: text)

        // 设置默认颜色
        attributedString.addAttribute(.foregroundColor,
                                    value: color_2B2C2F,
                                    range: NSRange(location: 0, length: text.count))

        // 将时间部分设置为橙色
        if let timeRange = text.range(of: timeString) {
            let nsRange = NSRange(timeRange, in: text)
            attributedString.addAttribute(.foregroundColor,
                                        value: color_FF8C00,
                                        range: nsRange)
        }

        countdownLabel.attributedText = attributedString
    }

    /// 停止倒计时
    private func stopCountdown() {
        countdownTimer?.invalidate()
        countdownTimer = nil
        deadlineDate = nil
    }

    /// 设置进度视图展开状态
    /// - Parameter expanded: 是否展开
    func setProgressExpanded(_ expanded: Bool, animated: Bool = true) {
        guard isProgressExpanded != expanded else { return }

        isProgressExpanded = expanded

        let duration = animated ? 0.3 : 0.0
        UIView.animate(withDuration: duration) {
            self.progressContainerView.isHidden = !expanded
            self.statusTitleButton.isSelected = expanded
            self.layoutIfNeeded()
        }
    }

    deinit {
        stopCountdown()
    }


}
