//
//  ProductOrderTableCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/20.
//

import UIKit
import SnapKit
import Kingfisher


/// 订单列表TableViewCell
class ProductOrderTableCell: UITableViewCell {
    
    // MARK: - 属性
    
    private var orderModel: ProductOrderModel?
    
    // MARK: - UI组件
    
    /// 主容器视图
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
   
    /// 通用区
    private lazy var orderCommonView = OrderCommonView()
   
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = UIColor(hexString: "F3F6F7")
        
        contentView.addSubview(containerView)
        
        // 添加子视图到容器
        containerView.addSubview(orderCommonView)
        setupConstraints()
    }
    
    private func setupConstraints() {
        // 容器视图约束
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview().offset(0)
        }
                
        // 通用约束
        orderCommonView.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalToSuperview()
        }
    }
    
    // MARK: - 配置方法

    func configure(with order: ProductOrderModel, item: OrderItem, isLast: Bool) {
        self.orderModel = order
        orderCommonView.configModel(model: order, item: item)
    }
}
