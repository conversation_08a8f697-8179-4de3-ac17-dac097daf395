//
//  OrderProgressView.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/29.
//

import UIKit
import Combine
import SnapKit
import Then

/// 订单进度视图
class OrderProgressView: BaseView {

    // MARK: - UI Configuration Properties
    
    /// 节点大小
    var nodeSize: CGFloat = 12 {
        didSet { updateLayoutIfNeeded() }
    }
    
    /// 连接线高度
    var lineHeight: CGFloat = 2 {
        didSet { updateLayoutIfNeeded() }
    }
    
    /// 左右边距
    var horizontalMargin: CGFloat = 12 {
        didSet { updateLayoutIfNeeded() }
    }
    
    /// 节点与标签间距
    var nodeLabelSpacing: CGFloat = 6 {
        didSet { updateLayoutIfNeeded() }
    }
    
    /// 标签最大宽度
    var labelMaxWidth: CGFloat = 80 {
        didSet { updateLayoutIfNeeded() }
    }
    
    /// 特殊连线中进度指示器大小
    var progressIndicatorSize: CGFloat = 3 {
        didSet { updateLayoutIfNeeded() }
    }
    
    /// 连接线与节点/指示器的间距
    var lineSpacing: CGFloat = 2 {
        didSet { updateLayoutIfNeeded() }
    }
    
    /// 标签字体大小
    var labelFontSize: CGFloat = 12 {
        didSet { updateLabelFonts() }
    }

    // MARK: - UI Components
    
    /// 主容器视图
    private lazy var containerView = UIView()
    
    /// 进度节点数组
    private var progressNodes: [ProgressNode] = []
    
    /// 连接线数组
    private var connectionLines: [UIView] = []
    
    /// 特殊进度指示器数组（用于显示当前进度到下一步的特殊效果）
    private var progressIndicators: [UIImageView] = []
    
    /// 状态标签数组
    private var statusLabels: [UILabel] = []
    
    // MARK: - Properties
    
    /// 当前步骤
    private var currentStep: Int = 0
    
    /// 状态映射数组
    private var statusMap: [OrderDetailStatus] = []
    
    // MARK: - UI Configuration
    
    override func configUI() {
        super.configUI()
        addSubview(containerView)
    }
    
    override func configLayout() {
        super.configLayout()
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Public Methods
    
    /// 配置进度视图
    /// - Parameters:
    ///   - currentStep: 当前步骤
    ///   - statusMap: 状态映射数组
    func configure(currentStep: Int, statusMap: [OrderDetailStatus]) {
        self.currentStep = currentStep
        self.statusMap = statusMap
        
        // 清除之前的视图
        clearPreviousViews()
        
        // 创建新的进度视图
        createProgressViews()
        
        // 更新进度状态
        updateProgressState()
    }
    
    // MARK: - Private Methods
    
    /// 清除之前的视图
    private func clearPreviousViews() {
        progressNodes.forEach { $0.removeFromSuperview() }
        connectionLines.forEach { $0.removeFromSuperview() }
        statusLabels.forEach { $0.removeFromSuperview() }
        progressIndicators.forEach { $0.removeFromSuperview() }
        
        progressNodes.removeAll()
        connectionLines.removeAll()
        statusLabels.removeAll()
        progressIndicators.removeAll()
    }
    
    /// 当布局需要更新时调用
    private func updateLayoutIfNeeded() {
        if !statusMap.isEmpty && bounds.width > 0 {
            clearPreviousViews()
            createProgressViews()
            updateProgressState()
        }
    }
    
    /// 更新标签字体
    private func updateLabelFonts() {
        statusLabels.forEach { label in
            label.font = UIFont.systemFont(ofSize: labelFontSize, weight: .regular)
        }
    }
    
    /// 创建进度视图
    private func createProgressViews() {
        guard !statusMap.isEmpty else { return }
        
        let nodeCount = statusMap.count
        guard nodeCount > 1 else {
            // 只有一个节点的情况
            createSingleNode()
            return
        }
        
        // 计算布局参数
        let containerWidth = bounds.width > 0 ? bounds.width : kScreenWidth - 48 // 减去左右边距
        let totalNodesWidth = CGFloat(nodeCount) * nodeSize
        let availableSpaceForLines = containerWidth - totalNodesWidth - (horizontalMargin * 2)
        let lineWidth = availableSpaceForLines / CGFloat(nodeCount - 1)
        let totalSpacing = lineWidth + nodeSize
        
        for (index, status) in statusMap.enumerated() {
            // 创建进度节点
            let node = ProgressNode()
            containerView.addSubview(node)
            progressNodes.append(node)
            
            // 创建状态标签
            let label = UILabel().then {
                $0.text = status.value
                $0.font = UIFont.systemFont(ofSize: labelFontSize, weight: .regular)
                $0.textAlignment = .center
                $0.numberOfLines = 1
            }
            containerView.addSubview(label)
            statusLabels.append(label)
            
            // 计算节点位置
            let xOffset = CGFloat(index) * totalSpacing + horizontalMargin
            
            // 设置节点约束
            node.snp.makeConstraints { make in
                make.left.equalToSuperview().offset(xOffset)
                make.top.equalToSuperview()
                make.width.height.equalTo(nodeSize)
            }
            
            // 设置标签约束
            label.snp.makeConstraints { make in
                make.centerX.equalTo(node)
                make.top.equalTo(node.snp.bottom).offset(nodeLabelSpacing)
                make.width.lessThanOrEqualTo(labelMaxWidth)
            }
            
            // 创建连接线（除了最后一个节点）
            if index < nodeCount - 1 {
                createConnectionLine(from: node, 
                                   toIndex: index + 1, 
                                   lineWidth: lineWidth - (lineSpacing * 2),
                                   currentStatus: status)
            }
        }
    }
    
    /// 创建单个节点（特殊情况处理）
    private func createSingleNode() {
        guard let status = statusMap.first else { return }
        
        // 创建进度节点
        let node = ProgressNode()
        containerView.addSubview(node)
        progressNodes.append(node)
        
        // 创建状态标签
        let label = UILabel().then {
            $0.text = status.value
            $0.font = UIFont.systemFont(ofSize: labelFontSize, weight: .regular)
            $0.textAlignment = .center
            $0.numberOfLines = 1
        }
        containerView.addSubview(label)
        statusLabels.append(label)
        
        // 设置节点约束（居中显示）
        node.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
            make.width.height.equalTo(nodeSize)
        }
        
        // 设置标签约束
        label.snp.makeConstraints { make in
            make.centerX.equalTo(node)
            make.top.equalTo(node.snp.bottom).offset(nodeLabelSpacing)
            make.width.lessThanOrEqualTo(labelMaxWidth)
        }
    }
    
    /// 创建连接线
    /// - Parameters:
    ///   - fromNode: 起始节点
    ///   - toIndex: 目标节点索引
    ///   - lineWidth: 连接线宽度
    ///   - currentStatus: 当前状态
    private func createConnectionLine(from fromNode: ProgressNode,
                                    toIndex: Int,
                                    lineWidth: CGFloat,
                                    currentStatus: OrderDetailStatus) {

        // 检查是否需要创建特殊的三段式连线
        if currentStep == currentStatus.sort && toIndex < statusMap.count {
            createSpecialConnectionLine(from: fromNode, lineWidth: lineWidth)
        } else {
            // 创建普通连接线
            let line = UIView()
            containerView.addSubview(line)
            connectionLines.append(line)

            // 设置连接线约束
            line.snp.makeConstraints { make in
                make.centerY.equalTo(fromNode)
                make.left.equalTo(fromNode.snp.right).offset(lineSpacing)
                make.height.equalTo(lineHeight)
                make.width.equalTo(lineWidth)
            }
        }
    }

    /// 创建特殊的三段式连接线
    /// - Parameters:
    ///   - fromNode: 起始节点
    ///   - lineWidth: 总连接线宽度
    private func createSpecialConnectionLine(from fromNode: ProgressNode, lineWidth: CGFloat) {
        // 计算各部分宽度：总宽度 - 指示器宽度 - 4个间距（节点后、指示器前后、指示器后）
        let totalSpacingWidth = lineSpacing * 4
        let availableLineWidth = lineWidth - progressIndicatorSize - totalSpacingWidth
        let firstLineWidth = availableLineWidth / 2
        let secondLineWidth = availableLineWidth - firstLineWidth

        // 第一段连接线（深蓝色实线）
        let firstLine = UIView()
        firstLine.backgroundColor = color_progress_blue_dark
        containerView.addSubview(firstLine)
        connectionLines.append(firstLine)

        firstLine.snp.makeConstraints { make in
            make.centerY.equalTo(fromNode)
            make.left.equalTo(fromNode.snp.right).offset(lineSpacing)
            make.height.equalTo(lineHeight)
            make.width.equalTo(firstLineWidth)
        }

        // 进度指示器（小圆点图片）
        let indicator = UIImageView()
        indicator.image = UIImage(named: "prodcut_order_progress_inprogress")
        indicator.contentMode = .scaleAspectFit
        containerView.addSubview(indicator)
        progressIndicators.append(indicator)

        indicator.snp.makeConstraints { make in
            make.centerY.equalTo(fromNode)
            make.left.equalTo(firstLine.snp.right).offset(lineSpacing)
            make.width.height.equalTo(progressIndicatorSize)
        }

        // 第二段连接线（灰色实线）
        let secondLine = UIView()
        secondLine.backgroundColor = color_progress_gray
        containerView.addSubview(secondLine)
        connectionLines.append(secondLine)

        secondLine.snp.makeConstraints { make in
            make.centerY.equalTo(fromNode)
            make.left.equalTo(indicator.snp.right).offset(lineSpacing)
            make.height.equalTo(lineHeight)
            make.width.equalTo(secondLineWidth)
        }
    }

    /// 更新进度状态
    private func updateProgressState() {
        for (index, status) in statusMap.enumerated() {
            // 判断节点状态
            let isCompleted = currentStep >= status.sort
            let isInProgress = currentStep == status.sort
            let isUpcoming = currentStep < status.sort

            // 更新节点状态
            if index < progressNodes.count {
                progressNodes[index].updateState(
                    isCompleted: isCompleted,
                    isInProgress: isInProgress,
                    isUpcoming: isUpcoming
                )
            }

            // 更新标签颜色
            if index < statusLabels.count {
                if isCompleted {
                    statusLabels[index].textColor = color_2B2C2F
                } else {
                    statusLabels[index].textColor = color_2B2C2F40
                }
            }

            // 更新连接线颜色
            if index < connectionLines.count {
                updateConnectionLine(at: index, currentStatus: status)
            }
        }
    }

    /// 更新连接线状态
    /// - Parameters:
    ///   - index: 连接线索引
    ///   - currentStatus: 当前状态
    private func updateConnectionLine(at index: Int, currentStatus: OrderDetailStatus) {
        // 由于特殊连线可能包含多个线段，我们需要根据实际情况更新
        // 这里的逻辑在创建连接线时已经处理，不需要额外更新
        // 因为特殊连线的颜色在创建时就已经设置好了

        // 对于普通连接线，更新颜色
        if currentStep != currentStatus.sort && index < connectionLines.count {
            let line = connectionLines[index]

            if currentStep >= currentStatus.sort {
                // 已完成的连接线：浅蓝色
                line.backgroundColor = color_progress_blue_light
            } else {
                // 未完成的连接线：灰色
                line.backgroundColor = color_progress_gray
            }
        }
    }

    override func layoutSubviews() {
        super.layoutSubviews()

        // 重新布局时更新进度视图
        if !statusMap.isEmpty && bounds.width > 0 {
            clearPreviousViews()
            createProgressViews()
            updateProgressState()
        }
    }

    // MARK: - Test Methods

    /// 测试进度条功能（仅用于调试）
    /// - Parameter currentStep: 当前步骤（0-3）
    func testProgressBar(currentStep: Int = 2) {
        // 创建测试数据
        let testStatusMap = [
            OrderDetailStatus(sort: 0, value: "已拍下"),
            OrderDetailStatus(sort: 1, value: "已付款"),
            OrderDetailStatus(sort: 2, value: "已发货"),
            OrderDetailStatus(sort: 3, value: "已收货")
        ]

        // 配置进度视图
        configure(currentStep: currentStep, statusMap: testStatusMap)
    }
}
