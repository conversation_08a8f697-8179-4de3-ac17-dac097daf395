//
//  ProductCartBottomView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/19.
//

import UIKit
import SnapKit
import Combine
import CombineCocoa

/// 购物车底部结算栏代理
protocol ProductCartBottomViewDelegate: AnyObject {
    /// 全选状态改变
    func cartBottomView(_ bottomView: ProductCartBottomView, didChangeAllSelection isSelected: Bool)
    
    /// 结算按钮点击
    func cartBottomViewDidTapCheckout(_ bottomView: ProductCartBottomView)
}

/// 购物车底部结算栏
class ProductCartBottomView: BaseView {
    
    // MARK: - 属性
    
    weak var delegate: ProductCartBottomViewDelegate?

    // MARK: - UI组件
    
    /// 全选按钮
    private lazy var selectAllButton = BaseButton().then {
        $0.setImage(UIImage(named:"baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named:"baselist_single_select"), for: .selected)
        $0.setTitle("全选", for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12,weight: .regular)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.spacing = 8
        $0.isUserInteractionEnabled = true
    }
    
    /// 共计标签
    private lazy var totalLabel = UILabel().then {
        $0.text = "共计"
        $0.font = UIFont.systemFont(ofSize: 12,weight: .regular)
        $0.textColor = color_2B2C2F
    }

    /// 邮费标签
    private lazy var postageLabel = UILabel().then {
        $0.text = "邮费¥0"
        $0.font = UIFont.systemFont(ofSize: 10, weight: .regular)
        $0.textColor = color_999999
        $0.isHidden = true // 默认隐藏，当有邮费时显示
    }

    /// 价格标签
    private lazy var priceLabel = UILabel().then {
        $0.text = "¥340"
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = color_red
    }
    
    /// 结算按钮
    private lazy var checkoutButton = UIButton().then {
        $0.setTitle("结算", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.backgroundColor = UIColor(hexString: "FF5A5A")
        $0.layer.cornerRadius = 22
    }
    
    
   
    
    // MARK: - UI设置
    
    override func configUI() {
        backgroundColor = .white
        
        addSubview(selectAllButton)
        addSubview(totalLabel)
        addSubview(postageLabel)
        addSubview(priceLabel)
        addSubview(checkoutButton)
        setupBindings()
        
    }
    
    override func configLayout() {
       
        
        selectAllButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalTo(checkoutButton)
        }
        
     
        checkoutButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.top.equalToSuperview().offset(10)
            make.width.equalTo(112)
            make.height.equalTo(44)
        }
        
        priceLabel.snp.makeConstraints { make in
            make.right.equalTo(checkoutButton.snp.left).offset(-12)
            make.centerY.equalTo(checkoutButton)
        }
        
        totalLabel.snp.makeConstraints { make in
            make.right.equalTo(priceLabel.snp.left).offset(-8)
            make.centerY.equalTo(checkoutButton)
        }
        postageLabel.snp.makeConstraints { make in
            make.right.equalTo(totalLabel.snp.left).offset(-12)
            make.centerY.equalTo(checkoutButton)
        }

    }
    
    // MARK: - 事件绑定
    
    override func setupBindings() {
        // 全选按钮点击
        selectAllButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.selectAllButton.isSelected.toggle()
                self.delegate?.cartBottomView(self, didChangeAllSelection: self.selectAllButton.isSelected)
            }
            .store(in: &cancellables)
        
        // 结算按钮点击
        checkoutButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.cartBottomViewDidTapCheckout(self)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 更新全选状态
    func updateAllSelection(_ isSelected: Bool) {
        selectAllButton.isSelected = isSelected
    }
    
    /// 更新总价
    func updateTotalPrice(_ price: Float) {
        priceLabel.text = "¥\(String(format: "%.0f", price))"
    }

    /// 更新价格信息（包含邮费）
    func updatePriceInfo(totalPrice: Float, postageFee: Float) {
        priceLabel.text = "¥\(String(format: "%.0f", totalPrice))"

        if postageFee > 0 {
            postageLabel.text = "邮费¥\(String(format: "%.0f", postageFee))"
            postageLabel.isHidden = false
        } else {
            postageLabel.isHidden = true
        }
    }
    
    /// 更新结算按钮状态
    func updateCheckoutButton(enabled: Bool, selectedCount: Int) {
        checkoutButton.isEnabled = enabled
        checkoutButton.alpha = enabled ? 1.0 : 0.5
    }
}
