//
//  DetailProductLinkTableCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/2.
//

import UIKit
import SnapKit
import Then
import Kingfisher
protocol DetailCellDelegate: AnyObject {
    func jumpButtonDidClick()
    func likeButtonDidClick(model:CommentModel)
}
class DetailProductLinkTableCell: UITableViewCell {
    weak var delegate: DetailCellDelegate?

    // MARK: - UI组件
    
    private lazy var containerView = UIView().then {
        $0.backgroundColor = UIColor(hexString:"2B2C2F", transparency: 0.04)
        $0.layer.cornerRadius = 12
        $0.clipsToBounds = true
    }
        
    private lazy var productImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.clipsToBounds = true
        $0.layer.cornerRadius = 12
    }
    
    private lazy var productNameLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .medium)
        $0.textColor = UIColor(hexString: "3D3E40")
        $0.numberOfLines = 2
    }
    

    private lazy var buyButton = BaseButton().then {
        $0.setTitle("查看详情", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.backgroundColor = color_blue
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.layer.cornerRadius = 12
    }
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 0, left: 12, bottom: 0, right: 12))
            make.height.equalTo(84)
        }
                
            
        containerView.addSubview(productImageView)
        productImageView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview().inset(12)
            make.width.equalTo(productImageView.snp.height)
        }
        
        containerView.addSubview(productNameLabel)
        productNameLabel.snp.makeConstraints { make in
            make.top.equalTo(productImageView)
            make.left.equalTo(productImageView.snp.right).offset(12)
            make.right.equalToSuperview().offset(-12)
        }
        
        containerView.addSubview(buyButton)
        buyButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview().offset(-12)
            make.width.equalTo(68)
            make.height.equalTo(24)
        }
        
        buyButton.addTarget(self, action: #selector(buyButtonTapped), for: .touchUpInside)
    }
    
    // MARK: - 配置方法
    
    func configure(with product: AdDetailProductInfo) {
        productNameLabel.text = product.name
        
        if let imageUrl = URL(string: product.imageUrl) {
            productImageView.kf.setImage(with: imageUrl, placeholder: UIImage(named: "placeholder"))
        }
    }
    
    // MARK: - 事件处理
    
    @objc private func buyButtonTapped() {
        self.delegate?.jumpButtonDidClick()
    }
    
}

