import UIKit
import SnapKit
import Then
import CombineCocoa
import Combine

protocol ProductSelectHeaderViewDelegate: AnyObject {
    func quantityDidChange(_ quantity: Int)
}

class ProductSelectHeaderView: UIView {
    // MARK: - 属性

    weak var delegate: ProductSelectHeaderViewDelegate?
    private var cancellables = Set<AnyCancellable>()

    /// 当前库存限制
    private var stockLimit: Int = Int.max

    private var currentQuantity: Int = 1 {
        didSet {
            quantityLabel.text = "\(currentQuantity)"
            decreaseButton.isEnabled = currentQuantity > 1
            increaseButton.isEnabled = currentQuantity < stockLimit
            delegate?.quantityDidChange(currentQuantity)
            // 同步更新私有变量
            _currentQuantity = currentQuantity
        }
    }
    
    // MARK: - UI组件
    
    private lazy var productImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.layer.cornerRadius = 8
        $0.layer.masksToBounds = true
        $0.backgroundColor = color_F6F8F9
        $0.image = UIImage(named: "home_product_detail_cart")
    }
    
    private lazy var priceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 20, weight: .regular)
        $0.textColor = UIColor(hexString: "#FF0000")
        $0.text = "¥2223.00"
    }
    
    private lazy var quantityView = UIView().then {
        $0.backgroundColor = .clear
    }
    
    private lazy var decreaseButton = BaseButton().then {
        $0.setImage(UIImage(named: "home_product_detail_cut"), for: .normal)
        $0.setImage(UIImage(named: "home_product_detail_cut_disable"), for: .disabled)
    }
    
    private lazy var quantityLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 13,weight: .medium)
        $0.textColor = color_2B2C2F
        $0.backgroundColor = UIColor(hexString: "#788092", transparency: 0.08)
        $0.textAlignment = .center
        $0.text = "1"
        $0.layer.cornerRadius = 8
        $0.layer.masksToBounds = true
    }
    
    private lazy var increaseButton = BaseButton().then {
        $0.setImage(UIImage(named: "home_product_detail_add"), for: .normal)
    }
    
    private lazy var freeShippingLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textColor = color_red
        $0.backgroundColor = UIColor(hexString: "FF0000", transparency: 0.08)
        $0.textAlignment = .center
        $0.layer.cornerRadius = 4
        $0.layer.masksToBounds = true
        $0.setHorizontalPadding(4)
    }
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupActions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }


    
    // MARK: - UI配置
    
    private func setupUI() {
        backgroundColor = .clear
        
        addSubview(productImageView)
        addSubview(priceLabel)
        addSubview(quantityView)
        addSubview(freeShippingLabel)
        
        quantityView.addSubview(decreaseButton)
        quantityView.addSubview(quantityLabel)
        quantityView.addSubview(increaseButton)
        
        productImageView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(12)
            make.width.equalTo(productImageView.snp.height)
            make.bottom.equalTo(-12)
        }
        
        priceLabel.snp.makeConstraints { make in
            make.left.equalTo(productImageView.snp.right).offset(10)
            make.top.equalTo(productImageView.snp.top)
        }
        
        freeShippingLabel.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalTo(priceLabel)
            make.height.equalTo(19)
        }
        
        quantityView.snp.makeConstraints { make in
            make.left.equalTo(priceLabel)
            make.bottom.equalTo(productImageView.snp.bottom)
            make.width.equalTo(120)
        }
        
        decreaseButton.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(30)
        }
        
        quantityLabel.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(33)
            make.height.equalTo(20)
            make.centerX.equalToSuperview()
        }
        
        increaseButton.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(30)
        }
    }
    
    // MARK: - 事件处理
    
    private func setupActions() {
        decreaseButton.tapPublisher
            .sink { [weak self] _ in
                self?.decreaseQuantity()
            }
            .store(in: &cancellables)
        
        increaseButton.tapPublisher
            .sink { [weak self] _ in
                self?.increaseQuantity()
            }
            .store(in: &cancellables)
    }
    
    private func decreaseQuantity() {
        if _currentQuantity > 1 {
            currentQuantity = _currentQuantity - 1
        }
    }

    private func increaseQuantity() {
        let newQuantity = _currentQuantity + 1
        if newQuantity <= stockLimit {
            currentQuantity = newQuantity
        }
    }
    
    // MARK: - 公共方法
    
    func configure(imageURL: String?, price: String, shipping: String) {
        // 在实际项目中，这里应该使用图片加载库加载imageURL
        if let imageURL = imageURL, !imageURL.isEmpty {
            // 使用图片加载库加载图片，例如Kingfisher
             productImageView.kf.setImage(with: URL(string: imageURL))
        }
        freeShippingLabel.text = shipping
        priceLabel.text = price
    }
    
    func updateQuantity(_ quantity: Int) {
        // 静默更新数量，不触发代理回调
        updateQuantitySilently(quantity)
    }

    /// 静默更新数量，不触发代理回调
    private func updateQuantitySilently(_ quantity: Int) {
        // 直接更新UI，不触发didSet
        quantityLabel.text = "\(quantity)"
        decreaseButton.isEnabled = quantity > 1
        increaseButton.isEnabled = quantity < stockLimit
        // 不调用delegate?.quantityDidChange(quantity)

        // 直接设置私有变量，避免触发didSet
        _currentQuantity = quantity
    }

    /// 更新库存限制
    func updateStockLimit(_ stock: Int) {
        stockLimit = max(0, stock)
        // 更新按钮状态
        increaseButton.isEnabled = _currentQuantity < stockLimit

        // 如果当前数量超过库存，调整到库存限制
        if _currentQuantity > stockLimit {
            updateQuantitySilently(stockLimit)
        }
    }

    // 添加私有变量来存储数量值
    private var _currentQuantity: Int = 1
} 
