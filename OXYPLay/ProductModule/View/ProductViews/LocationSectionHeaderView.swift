//
//  LocationSectionHeaderView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/17.
//

import UIKit

class LocationSectionHeaderView: UITableViewHeaderFooterView {
    
    // MARK: - UI Components
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor(hexString: "2B2C2F", transparency: 0.56)
        return label
    }()
    
    
    // MARK: - Initialization
    
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = .white
        contentView.addSubview(titleLabel)
        setupConstraints()
    }
    
    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
        }
    }
    
    // MARK: - Configuration
    
    func configure(with title: String) {
        titleLabel.text = title
    }
}
