//
//  CouponTableViewCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/18.
//

import UIKit
import SnapKit

class CouponTableViewCell: UITableViewCell {

    // MARK: - 属性
    var couponItem: CouponItemModel? {
        didSet {
            updateUI()
        }
    }

    var isAvailable: Bool = true {
        didSet {
            updateAvailableState()
        }
    }

    // MARK: - UI组件

    /// 优惠券项视图
    private lazy var couponItemView = CouponItemView().then {
        $0.delegate = self
    }

    // MARK: - 初始化

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - UI设置

    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear

        // 添加优惠券项视图
        contentView.addSubview(couponItemView)

        // 设置约束
        couponItemView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview()
        }

    }

    // MARK: - 数据更新

    private func updateUI() {
        guard let couponItem = couponItem else { return }

        // 配置优惠券项视图
        couponItemView.configure(with: couponItem, available: isAvailable)
    }

    private func updateAvailableState() {
        // 通过重新配置来更新可用状态
        updateUI()
    }

    // 设置选中状态
    func setSelected(_ selected: Bool) {
        couponItemView.setSelected(selected)
    }
}

// MARK: - CouponItemViewDelegate

extension CouponTableViewCell: CouponItemViewDelegate {
    /// 优惠券点击事件
    func couponItemViewDidTap(_ view: CouponItemView, coupon: Any) {
        // 这里可以处理点击事件，或者通过其他方式传递给外部
        // 由于原来的Cell没有点击事件处理，这里保持空实现
    }
}
