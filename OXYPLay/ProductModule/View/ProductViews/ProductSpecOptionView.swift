import UIKit

// MARK: - 代理协议
protocol ProductSpecOptionViewDelegate: AnyObject {
    func specOptionDidChange(_ model: ProductDetailModel)
}

class ProductSpecOptionView: UIView {
    // MARK: - 属性

    weak var delegate: ProductSpecOptionViewDelegate?
    private var detailModel: ProductDetailModel?
    
    // MARK: - UI组件
    private lazy var collectionView: UICollectionView = {
        let layout = LeftAlignedCollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 8
        layout.minimumInteritemSpacing = 8
        layout.headerReferenceSize = CGSize(width: 0, height: 42)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.register(
            ProductSpecOptionSectionHeaderView.self, // 自定义 Header 视图
            forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, // 类型为 Header
            withReuseIdentifier: ProductSpecOptionSectionHeaderView.reuseIdentifier
        )
        collectionView.backgroundColor = .clear
        collectionView.showsVerticalScrollIndicator = false
        collectionView.isScrollEnabled = false // 禁用滚动，让高度自适应
        collectionView.register(OptionCell.self, forCellWithReuseIdentifier: OptionCell.reuseIdentifier)
        collectionView.delegate = self
        collectionView.dataSource = self
       
        return collectionView
    }()
    
    /// 更新模型数据
    func updateModel(_ model: ProductDetailModel) {
        detailModel = model
        collectionView.reloadData()
    }

    // MARK: - 初始化
    
    init() {
        super.init(frame: .zero)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }


    
    // MARK: - UI配置
    
    private func setupUI() {
        self.backgroundColor = .white
        self.layer.cornerRadius = 16
        self.masksToBounds = true
        
        addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.left.equalTo(12)
            make.bottom.right.equalTo(-12)
        }
    }
}



// MARK: - UICollectionViewDataSource

extension ProductSpecOptionView: UICollectionViewDataSource {
    func numberOfSections(in collectionView: UICollectionView) -> Int{
        guard let models = detailModel?.variants.data else {
            return 0
        }
        if let selectedModel = models.first(where: { $0.isSelect }) {
            return selectedModel.specs.count + 1
            
        } else {
            return 1
        }
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        guard let models = detailModel?.variants.data else {
            return 0
        }
        if section == 0 {
            return models.count
        }else{
            if let selectedModel = models.filter({ $0.isSelect }).first {
                return selectedModel.specs[section - 1].values.count
            }
             else {
                return 0
            }
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: OptionCell.reuseIdentifier, for: indexPath) as? OptionCell else {
            return UICollectionViewCell()
        }
        guard let models = detailModel?.variants.data else {
            return cell
        }
        if indexPath.section == 0 {
            cell.configure(with: models[indexPath.row].name, isSelected: models[indexPath.row].isSelect,selectable: true)
        }else{
            if let selectedModel = models.filter({ $0.isSelect }).first {
                cell.configure(with:selectedModel.specs[indexPath.section - 1].values[indexPath.row].value, isSelected: selectedModel.specs[indexPath.section - 1].values[indexPath.row].isSelect, selectable: selectedModel.specs[indexPath.section - 1].values[indexPath.row].selectable)
            }
        }
        return cell
    }
    // 返回 Header 视图
    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        if kind == UICollectionView.elementKindSectionHeader {
            let header = collectionView.dequeueReusableSupplementaryView(
                ofKind: kind,
                withReuseIdentifier: ProductSpecOptionSectionHeaderView.reuseIdentifier,
                for: indexPath
            ) as! ProductSpecOptionSectionHeaderView
            
            guard let models = detailModel?.variants.data,let title = detailModel?.variants.title else {
                return header
            }
            if indexPath.section == 0 {
                header.configure(title:title)
                return header
            }else{
                if let selectedModel = models.filter({ $0.isSelect }).first {
                    header.configure(title:selectedModel.specs[indexPath.section - 1].name)
                    return header
                }
                 else {
                     return header
                }
            }
        }
        return UICollectionReusableView() // 默认返回空视图
    }
}

// MARK: - UICollectionViewDelegate

extension ProductSpecOptionView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard var model = detailModel else { return }

        if indexPath.section == 0 {
            // 选择商品变体
            for (index, _) in model.variants.data.enumerated() {
                model.variants.data[index].isSelect = (index == indexPath.row)
            }
        } else {
            // 选择规格选项
            if let selectedModelIndex = model.variants.data.firstIndex(where: { $0.isSelect }) {
                // 检查该选项是否可选
                let isSelectable = model.variants.data[selectedModelIndex].specs[indexPath.section - 1].values[indexPath.row].selectable

                // 如果不可选，则直接返回，不做任何操作
                if !isSelectable {
                    return
                }

                // 先将该规格组的所有选项设为未选中
                for i in 0..<model.variants.data[selectedModelIndex].specs[indexPath.section - 1].values.count {
                    model.variants.data[selectedModelIndex].specs[indexPath.section - 1].values[i].isSelect = false
                }
                // 设置选中的选项
                model.variants.data[selectedModelIndex].specs[indexPath.section - 1].values[indexPath.row].isSelect = true
            }
        }

        // 更新本地模型并通知代理
        detailModel = model
        delegate?.specOptionDidChange(model)

        // 刷新UI
        collectionView.reloadData()
    }
}

// MARK: - UICollectionViewDelegateFlowLayout

extension ProductSpecOptionView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        // 预估每个单元格的宽度
        var option = ""
        guard let models = detailModel?.variants.data,let title = detailModel?.variants.title else {
            return CGSizeZero
        }
        if indexPath.section == 0 {
            option = models[indexPath.row].name
        }else{
            if let selectedModel = models.filter({ $0.isSelect }).first {
                option = selectedModel.specs[indexPath.section - 1].values[indexPath.row].value
            }
        }
        let font = UIFont.systemFont(ofSize: 11,weight: .regular)
        let textWidth = (option as NSString).boundingRect(
            with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: 27),
            options: .usesLineFragmentOrigin,
            attributes: [NSAttributedString.Key.font: font],
            context: nil
        ).width
        
        // 文本宽度 + 左右内边距 + 边框
        let cellWidth = textWidth + 24 > 60 ?  textWidth + 24 : 60
        return CGSize(width: cellWidth, height: 27)
    }
}

// MARK: - OptionCell

class OptionCell: UICollectionViewCell {
    static let reuseIdentifier = "OptionCell"
    
    private lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textAlignment = .center
        $0.numberOfLines = 1
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(titleLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(horizontal: 12, vertical: 8))
        }
        
        contentView.layer.cornerRadius = 8
        contentView.layer.borderWidth = 1
    }
    
    func configure(with title: String, isSelected: Bool,selectable:Bool) {
        titleLabel.text = title

        if selectable == false{
            titleLabel.textColor = UIColor(hexString: "#2B2C2F", transparency: 0.32)
            contentView.backgroundColor = UIColor(hexString: "#2B2C2F", transparency: 0.08)
            contentView.layer.borderColor = UIColor.clear.cgColor
        }else{
            titleLabel.textColor = isSelected ? UIColor(hexString: "#2A72FF") : color_2B2C2F
            contentView.backgroundColor = isSelected ? UIColor(hexString: "#EDF4FF") : .white
            contentView.layer.borderColor = isSelected ? UIColor(hexString: "#2A72FF")!.cgColor : color_2B2C2F24.cgColor
        }
    }
}
// MARK: - LeftAlignedCollectionViewFlowLayout

class LeftAlignedCollectionViewFlowLayout: UICollectionViewFlowLayout {
    override func layoutAttributesForElements(in rect: CGRect) -> [UICollectionViewLayoutAttributes]? {
        guard let originalAttributes = super.layoutAttributesForElements(in: rect) else {
            return nil
        }
        
        // 复制原始布局属性，避免修改原始数据
        let attributes = originalAttributes.map { $0.copy() as! UICollectionViewLayoutAttributes }
        
        // 记录每一行的起始位置
        var leftMargin = sectionInset.left
        var maxY: CGFloat = -1.0
        
        // 对每个单元格进行左对齐处理
        attributes.forEach { layoutAttribute in
            if layoutAttribute.representedElementCategory == .cell {
                // 如果是新的一行，重置左边距
                if layoutAttribute.frame.origin.y >= maxY {
                    leftMargin = sectionInset.left
                }
                
                // 设置x坐标，保持左对齐
                layoutAttribute.frame.origin.x = leftMargin
                
                // 更新左边距为下一个单元格的起始位置
                leftMargin += layoutAttribute.frame.width + minimumInteritemSpacing
                
                // 更新当前行的最大Y值
                maxY = max(layoutAttribute.frame.maxY, maxY)
            }
        }
        
        return attributes
    }
}
class ProductSpecOptionSectionHeaderView: UICollectionReusableView {
    static let reuseIdentifier = "ProductSpecOptionSectionHeaderView"
    
    private lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = color_2B2C2F
    }
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        addSubview(titleLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(20)
            make.left.equalTo(0)
        }
    }
    
    func configure(title: String) {
        titleLabel.text = title
    }
}
