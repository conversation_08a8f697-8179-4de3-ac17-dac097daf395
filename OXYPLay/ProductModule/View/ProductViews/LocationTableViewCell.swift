//
//  LocationTableViewCell.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/17.
//

import UIKit

class LocationTableViewCell: UITableViewCell {
    
    // MARK: - UI Components

    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = color_2B2C2F
        return label
    }()
    private lazy var charLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor(hexString: "2B2C2F", transparency: 0.56)
        return label
    }()
    

    private lazy var checkmarkImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "baselist_right")
        imageView.tintColor = color_blue
        imageView.isHidden = true
        return imageView
    }()
    
    // MARK: - Initialization
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = .white
        selectionStyle = .none
        contentView.addSubview(charLabel)
        contentView.addSubview(nameLabel)
        contentView.addSubview(checkmarkImageView)
        setupConstraints()
    }

    private func setupConstraints() {
        charLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
        }
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(36)
            make.centerY.equalToSuperview()
            make.right.lessThanOrEqualTo(checkmarkImageView.snp.left).offset(-16)
        }
        
        checkmarkImageView.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalToSuperview()
        }
        
    }
    
    // MARK: - Configuration

    func configure(with region: RegionListItemModel, isSelected: Bool,sectionTitle:String) {
        // 设置地区名称
        nameLabel.text = region.name
        charLabel.text = sectionTitle

        // 设置选中状态
        checkmarkImageView.isHidden = !isSelected

        if isSelected {
            nameLabel.textColor = color_blue
        } else {
            nameLabel.textColor = color_2B2C2F
        }
    }
}
