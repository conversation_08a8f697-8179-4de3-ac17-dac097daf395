//
//  LocationSelectionItemView.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/17.
//

import UIKit

class LocationSelectionItemView: UIView {
    
    // MARK: - Properties
    
    var onTap: (() -> Void)?
    
    // MARK: - UI Components
    
    private lazy var dotView: UIButton = {
        let view = UIButton()
        view.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        view.setImage(UIImage(named: "baselist_single_select"), for: .selected)
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = color_2B2C2F
        return label
    }()
    
    private lazy var arrowImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "baselist_arrow")
        imageView.tintColor = color_8D9096
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    lazy var topLine: UIView = {
        let imageView = UIView()
        imageView.isHidden = true
        imageView.backgroundColor = UIColor(hexString: "2A72FF", transparency: 0.2)
        return imageView
    }()
    lazy var bottomLine: UIView = {
        let imageView = UIView()
        imageView.isHidden = true
        imageView.backgroundColor = UIColor(hexString: "2A72FF", transparency: 0.2)
        return imageView
    }()
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupGesture()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        addSubview(dotView)
        addSubview(titleLabel)
        addSubview(arrowImageView)
        addSubview(topLine)
        addSubview(bottomLine)
        addSubview(arrowImageView)
        setupConstraints()
    }
    
    private func setupConstraints() {
        dotView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
        }
        topLine.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.bottom.equalTo(dotView.snp.top).offset(-2)
            make.width.equalTo(1)
            make.centerX.equalTo(dotView)
            
        }
        bottomLine.snp.makeConstraints { make in
            make.bottom.equalTo(0)
            make.top.equalTo(dotView.snp.bottom).offset(2)
            make.width.equalTo(1)
            make.centerX.equalTo(dotView)
            
        }
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(dotView.snp.right).offset(10)
            make.centerY.equalToSuperview()
            make.right.lessThanOrEqualTo(arrowImageView.snp.left).offset(-8)
        }
        
        arrowImageView.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalToSuperview()
        }
    }
    
    private func setupGesture() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap))
        addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Actions
    
    @objc private func handleTap() {
        onTap?()
    }
    
    // MARK: - Configuration
    
    func configure(title: String, isSelected: Bool,type:Int) {
        titleLabel.text = title
        updateSelectionState(isSelected: isSelected,type:type)
    }
    
    func updateSelectionState(isSelected: Bool,type:Int) {
        dotView.isSelected = isSelected
        titleLabel.textColor = isSelected ? color_blue:color_2B2C2F
        if type == 0{
            // 省份：顶部无线，底部根据选中状态显示
            topLine.isHidden = true
            bottomLine.isHidden = !isSelected
        }else if type == 1 {
            // 城市：顶部有线，底部根据选中状态显示
            topLine.isHidden = false
            bottomLine.isHidden = !isSelected
        }else if type == 2 {
            // 区县：顶部有线，底部根据选中状态显示
            topLine.isHidden = false
            bottomLine.isHidden = !isSelected
        }else{
            // 街道：顶部有线，底部无线
            topLine.isHidden = false
            bottomLine.isHidden = true
        }
    }
    
    func updateTitle(_ title: String) {
        titleLabel.text = title
    }
}
