//
//  ProductRecommendCollectionCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/23.
//

import UIKit
import Combine
import Kingfisher

/// 商品推荐列表CollectionViewCell
class ProductRecommendCollectionCell: UICollectionViewCell {
    
    // MARK: - 标识符
    
    static let identifier = "ProductRecommendCollectionCell"
    
    // MARK: - UI组件
    
    private lazy var recommendItemView = DetailRecommendItemView()
    
    // MARK: - 属性
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期
    
    override func prepareForReuse() {
        super.prepareForReuse()
        cancellables.removeAll()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        backgroundColor = .clear
        
        contentView.addSubview(recommendItemView)
        recommendItemView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - 配置方法
    
    /// 配置Cell数据
    /// - Parameter item: 商品推荐项
    func configure(with item: ProductRecommendItem) {
        recommendItemView.configure(with: item)
    }
    
    // MARK: - 静态方法
    
    /// 计算Cell高度
    /// - Parameters:
    ///   - item: 商品推荐项
    ///   - width: Cell宽度
    /// - Returns: Cell高度
    static func calculateHeight(for item: ProductRecommendItem, width: CGFloat) -> CGFloat {
        // 图片高度固定为128
        let imageHeight: CGFloat = 128
        
        // 标题高度计算（最多2行）
        let titleFont = UIFont.systemFont(ofSize: 13, weight: .regular)
        let titleHeight = item.name.heightForLabel(
            font: titleFont,
            width: width,
            maxLines: 2
        )
        
        // 价格和浏览量高度
        let priceHeight: CGFloat = 18
        
        // 间距：图片下方8pt + 标题下方8pt
        let spacing: CGFloat = 16
        
        return imageHeight + titleHeight + priceHeight + spacing
    }
}

