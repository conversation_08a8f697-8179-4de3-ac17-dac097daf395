//
//  OrderAddressView.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/25.
//

import UIKit

/// 订单收货地址视图
class OrderAddressView: BaseView {
    
    // MARK: - UI Components

    private lazy var locationView = OrderLocationItemView()
    private lazy var stackView = UIStackView().then {
        $0.axis = .vertical
        $0.spacing = 0
    }
    private lazy var contentView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
    }
    private lazy var deliveryButton = BaseButton().then {
        $0.setImage(UIImage(named: "prodcut_order_delivery"), for: .normal)
        $0.setTitleColor(UIColor(hexString: "#2B2C2F", transparency: 0.63), for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 11, weight: .regular)
    }
    private lazy var copyButton = BaseButton().then {
        $0.setImage(UIImage(named: "prodcut_order_copy"), for: .normal)
    }
    private lazy var arrowButton = BaseButton().then {
        $0.setImage(UIImage(named: "prodcut_order_right_arrow"), for: .normal)
    }
    private lazy var deliveryContentView = UIView()
    // MARK: - Initialization
    private lazy var model = OrderDetailModel()
    override func configUI() {

        self.addSubview(contentView)
        contentView.addSubview(stackView)
        stackView.addArrangedSubview(deliveryContentView)
        stackView.addArrangedSubview(locationView)
        deliveryContentView.addSubview(deliveryButton)
        deliveryContentView.addSubview(copyButton)
        deliveryContentView.addSubview(arrowButton)
        setupBindings()
    }

    override func setupBindings() {
        copyButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self,!model.logistics.express_no.isEmpty  else {
                    return
                }
                self.copyToClipboard(model.logistics.express_no)
            }
            .store(in: &cancellables)
        // 添加点击事件
        let tapGesture = UITapGestureRecognizer()
        tapGesture.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, model.status <= 2 else { return }
                ///选择退货地址
                if let superview = self.superview {
                    MBProgressHUD.showPrompt("选择地址", in: superview)
                }
               
            }
            .store(in: &cancellables)
        
        self.addGestureRecognizer(tapGesture)
    }
    /// 复制到剪贴板
    private func copyToClipboard(_ text: String) {
        guard !text.isEmpty else { return }

        UIPasteboard.general.string = text

        // 显示复制成功提示
        if let superview = self.superview {
            MBProgressHUD.showPrompt("已复制到剪贴板", in: superview)
        }
    }
    override func configLayout() {
        contentView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.bottom.equalTo(0)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
        stackView.snp.makeConstraints{$0.edges.equalToSuperview()}
        deliveryButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(12)
            make.bottom.equalTo(0)
        }
        copyButton.snp.makeConstraints { make in
            make.centerY.equalTo(deliveryButton)
            make.left.equalTo(deliveryButton.snp.right).offset(8)
        }
        arrowButton.snp.makeConstraints { make in
            make.centerY.equalTo(deliveryButton)
            make.left.greaterThanOrEqualTo(copyButton.snp.right).offset(8)
            make.right.equalTo(-12)
        }
    }
    
    // MARK: - Public Methods
    
    /// 配置地址数据
    /// - Parameter address: 地址数据
    func configure(with model: OrderDetailModel) {
        self.model = model
        locationView.titleLabel.text = "\(model.address.name) 86-\(model.address.phone.maskPhoneNumber)"
        // 设置地区
        locationView.placeholderLabel.text = "\(model.address.province)\(model.address.city)\(model.address.district)\(model.address.street)"

        // 设置详细地址
        locationView.valueLabel.text = model.address.detail.isEmpty ? model.address.full_address : model.address.detail
        deliveryContentView.isHidden = model.logistics.express_company.isEmpty
        locationView.arrowImageView.isHidden = model.status > 2
        if model.logistics.express_no.isEmpty {
            return
        }
        let deliveryButtonTitle = model.logistics.express_company + " " + model.logistics.express_no
        deliveryButton.setTitle(deliveryButtonTitle, for: .normal)
    }
    
}
