//
//  ProductCartSellerHeaderView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/19.
//

import UIKit
import SnapKit
import Combine
import CombineCocoa

/// 购物车卖家头部视图代理
protocol ProductCartSellerHeaderViewDelegate: AnyObject {
    /// 选择状态改变
    func cartSellerHeaderView(_ headerView: ProductCartSellerHeaderView, didChangeSelection isSelected: Bo<PERSON>, seller: CartSeller)
}

/// 购物车卖家头部视图
class ProductCartSellerHeaderView: UITableViewHeaderFooterView {
    
    // MARK: - 属性
    
    weak var delegate: ProductCartSellerHeaderViewDelegate?
    private var cancellables = Set<AnyCancellable>()
    private var seller: CartSeller?
    
    // MARK: - UI组件
    
    /// 选择按钮
    private lazy var selectButton = UIButton().then {
        $0.setImage(UIImage(named:"baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named:"baselist_single_select"), for: .selected)
    }
    
    /// 卖家头像
    private lazy var avatarImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.clipsToBounds = true
        $0.layer.cornerRadius = 10
        $0.backgroundColor = color_F6F8F9
    }
    
    /// 卖家名称
    private lazy var nameLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = color_2B2C2F
    }
    
    /// 容器视图
    private lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 12
        $0.layer.maskedCorners = [.layerMinXMinYCorner,.layerMaxXMinYCorner]
        $0.layer.masksToBounds = true
    }
    
    // MARK: - 初始化
    
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupUI()
        setupBindings()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupBindings()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        cancellables.removeAll()
        setupBindings()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        backgroundView = UIView()
        backgroundView?.backgroundColor = color_F6F8F9
        
        contentView.addSubview(containerView)
        containerView.addSubview(selectButton)
        containerView.addSubview(avatarImageView)
        containerView.addSubview(nameLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview().offset(12)
            make.bottom.equalToSuperview()
        }
        avatarImageView.snp.makeConstraints { make in
            make.left.equalTo(selectButton.snp.right).offset(10)
            make.top.equalToSuperview().offset(12)
            make.width.height.equalTo(20)
            make.bottom.lessThanOrEqualToSuperview().offset(-8) // 使用lessThanOrEqualTo避免冲突
        }
        selectButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalTo(avatarImageView)
            make.width.height.equalTo(14)
        }
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.centerY.equalTo(avatarImageView)
            make.right.equalToSuperview().offset(-12)
        }
    }
    
    // MARK: - 事件绑定
    
    private func setupBindings() {
        // 选择按钮点击
        selectButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let seller = self.seller else { return }
                // 不直接修改数据，而是通过代理通知控制器处理
                self.delegate?.cartSellerHeaderView(self, didChangeSelection: !seller.isSelected, seller: seller)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 配置方法
    
    /// 配置头部视图数据
    func configure(with seller: CartSeller) {
        self.seller = seller
        
        // 设置选中状态
        selectButton.isSelected = seller.isSelected
        
        // 设置卖家信息
        nameLabel.text = seller.seller_name
        
        // 加载卖家头像
        loadAvatar(seller.seller_avatar)
    }
    
    // MARK: - 私有方法
    
    /// 加载卖家头像
    private func loadAvatar(_ urlString: String) {
        if !urlString.isEmpty, let url = URL(string: urlString) {
            avatarImageView.kf.setImage(
                with: url,
                placeholder: UIImage(systemName: "person.circle"),
                options: [
                    .transition(.fade(0.3)),
                    .cacheOriginalImage
                ]
            )
        } else {
            avatarImageView.image = UIImage(systemName: "person.circle")
            avatarImageView.tintColor = color_999999
        }
    }
}
