//
//  ProductCartItemCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/19.
//

import UIKit
import SnapKit
import Combine
import CombineCocoa

/// 购物车商品Cell代理
protocol ProductCartItemCellDelegate: AnyObject {
    /// 选择状态改变
    func cartItemCell(_ cell: ProductCartItemCell, didChangeSelection item: CartItem)
    /// 数量变化
    func cartItemCell(_ cell: ProductCartItemCell, didChangeQuantity quantity: Int, for item: CartItem)
    /// 请求删除商品（当数量为1时点击减号）
    func cartItemCell(_ cell: ProductCartItemCell, didRequestDeleteItem item: CartItem)
}

/// 购物车商品Cell
class ProductCartItemCell: UITableViewCell {
    
    // MARK: - 属性
    
    weak var delegate: ProductCartItemCellDelegate?
    private var cancellables = Set<AnyCancellable>()
    private var item: CartItem?
    
    // MARK: - UI组件
    
    /// 选择按钮
    private lazy var selectButton = UIButton().then {
        $0.setImage(UIImage(named:"baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named:"baselist_single_select"), for: .selected)
    }
    private lazy var cartOrderCommonView = ProductCartOrderCommonView().then {
        $0.delegate = self
    }
   
    /// 容器视图
    private lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.masksToBounds = true
        $0.layer.maskedCorners = [.layerMaxXMaxYCorner,.layerMinXMaxYCorner]

    }
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupBindings()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupBindings()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        cancellables.removeAll()
        setupBindings()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        contentView.backgroundColor =   color_F6F8F9
        contentView.addSubview(containerView)
        containerView.addSubview(selectButton)
        containerView.addSubview(cartOrderCommonView)
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.right.equalTo(0)
            make.top.equalTo(0)
            make.bottom.equalToSuperview()
        }
        selectButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalTo(cartOrderCommonView)
            make.width.height.equalTo(14)
        }

        cartOrderCommonView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.left.equalTo(selectButton.snp.right).offset(10)
            make.right.equalTo(-12)
            make.bottom.equalTo(-12)
        }

    }

    // MARK: - 事件绑定

    private func setupBindings() {
        // 选择按钮点击事件
        selectButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let item = self.item else { return }
                // 通知代理处理选择状态变化
                self.delegate?.cartItemCell(self, didChangeSelection: item)
            }
            .store(in: &cancellables)
    }

    // MARK: - 配置方法

    /// 配置Cell数据
    func configure(with item: CartItem,isLast:Bool) {
        self.item = item
        containerView.layer.cornerRadius = isLast ? 12 : 0

        // 设置选中状态
        selectButton.isSelected = item.isSelected

        // 配置商品信息
        cartOrderCommonView.configure(with: item)
    }

}

// MARK: - ProductCartOrderCommonViewDelegate

extension ProductCartItemCell: ProductCartOrderCommonViewDelegate {

    func cartOrderCommonView(_ view: ProductCartOrderCommonView, didChangeQuantity quantity: Int, for item: CartItem) {
        delegate?.cartItemCell(self, didChangeQuantity: quantity, for: item)
    }

    func cartOrderCommonView(_ view: ProductCartOrderCommonView, didRequestDeleteItem item: CartItem) {
        delegate?.cartItemCell(self, didRequestDeleteItem: item)
    }
}
