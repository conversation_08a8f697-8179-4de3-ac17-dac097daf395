//
//  ProductCartOrderCommonView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/20.
//

import UIKit
import SnapKit
import Combine
import CombineCocoa

/// 购物车商品视图代理
protocol ProductCartOrderCommonViewDelegate: AnyObject {
    /// 数量变化
    func cartOrderCommonView(_ view: ProductCartOrderCommonView, didChangeQuantity quantity: Int, for item: CartItem)
    /// 请求删除商品（当数量为1时点击减号）
    func cartOrderCommonView(_ view: ProductCartOrderCommonView, didRequestDeleteItem item: CartItem)
}

/// 购物车专用的商品视图，基于OrderCommonView扩展
class ProductCartOrderCommonView: BaseView {
    
    // MARK: - 属性
    
    weak var delegate: ProductCartOrderCommonViewDelegate?
    private var item: CartItem?
    
    // MARK: - UI组件
    
    /// 基础商品信息视图
    private lazy var orderCommonView = OrderCommonView()
    
    /// 数量控制容器
    private lazy var quantityContainerView = UIView()
    
    /// 减少按钮
    private lazy var decreaseButton = UIButton().then {
        $0.setImage(UIImage(named: "order_product_detail_cut"), for: .normal)
    }
    
    /// 数量标签
    private lazy var quantityLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 13, weight: .medium)
        $0.textColor = color_2B2C2F
        $0.textAlignment = .center
        $0.backgroundColor = UIColor(hexString: "788092", transparency: 0.08)
        $0.text = "1"
        $0.cornerRadius = 6
        $0.masksToBounds = true
        $0.setHorizontalPadding(7)
        $0.setVerticalPadding(1.75)
    }
    
    /// 增加按钮
    private lazy var increaseButton = UIButton().then {
        $0.setImage(UIImage(named: "order_product_detail_add"), for: .normal)
    }
    
    // MARK: - UI设置
    
    override func configUI() {
        addSubview(orderCommonView)
        orderCommonView.addSubview(quantityContainerView)
        quantityContainerView.addSubview(decreaseButton)
        quantityContainerView.addSubview(quantityLabel)
        quantityContainerView.addSubview(increaseButton)
        setupBindings()
    }
    
    override func configLayout() {
        orderCommonView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(0)
        }
        
        quantityContainerView.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        decreaseButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(4)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        quantityLabel.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(decreaseButton.snp.right).offset(4)
            make.right.equalTo(increaseButton.snp.left).offset(-4)
        }
        
        increaseButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-4)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
    }
    
    // MARK: - 事件绑定
    
    override func setupBindings() {
        // 减少按钮点击
        decreaseButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let item = self.item else { return }

                if item.quantity == 1 {
                    // 数量为1时，提示删除
                    self.delegate?.cartOrderCommonView(self, didRequestDeleteItem: item)
                } else {
                    // 数量大于1时，正常减少
                    let newQuantity = item.quantity - 1
                    self.delegate?.cartOrderCommonView(self, didChangeQuantity: newQuantity, for: item)
                }
            }
            .store(in: &cancellables)

        // 增加按钮点击
        increaseButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let item = self.item else { return }
                let newQuantity = min(item.stock, item.quantity + 1)
                if newQuantity != item.quantity {
                    self.delegate?.cartOrderCommonView(self, didChangeQuantity: newQuantity, for: item)
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 配置方法
    
    /// 配置购物车商品数据
    func configure(with item: CartItem) {
        self.item = item
        
        // 配置基础商品信息
        orderCommonView.configModel(model: item)
        
        // 配置数量
        quantityLabel.text = "\(item.quantity)"
        
        // 更新按钮状态
        // 减少按钮始终可用（数量为1时显示删除提示）
        decreaseButton.isEnabled = true
        decreaseButton.alpha = 1.0

        increaseButton.isEnabled = item.quantity < item.stock
        increaseButton.alpha = item.quantity < item.stock ? 1.0 : 0.5
    }
}
