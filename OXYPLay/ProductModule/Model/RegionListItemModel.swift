//
//  RegionListItemModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/17.
//
struct ProductAddressModel: SmartCodable {
    var default_address: ProductAddressItemModel = ProductAddressItemModel()
    var address_list: [ProductAddressItemModel] = [ProductAddressItemModel]()

    // 新增方法：获取选中的地址项
    func selectedAddress() -> ProductAddressItemModel? {
        // 优先检查原地址是否被选中
        if default_address.isSelect && !default_address.id.isEmpty {
            return default_address
        }

        // 其次查找 address_list 中 isSelect 为 true 的项
        if let selectedItem = address_list.first(where: { $0.isSelect }) {
            return selectedItem
        }

        // 如果都没有选中，返回nil（不自动选择默认地址）
        return nil
    }

    /// 设置默认选择，选中默认地址或第一个地址
    /// - Returns: 设置了默认选项的ProductAddressModel
    mutating func setDefaultSelection() -> ProductAddressModel {
        // 先清除所有选中状态
        default_address.isSelect = false
        for i in 0..<address_list.count {
            address_list[i].isSelect = false
        }

        // 如果有原地址且不为空，优先选中原地址
        if !default_address.id.isEmpty {
            default_address.isSelect = true
        } else if let defaultIndex = address_list.firstIndex(where: { $0.is_default }) {
            // 如果有默认地址，选中默认地址
            address_list[defaultIndex].isSelect = true
        } else if !address_list.isEmpty {
            // 如果没有默认地址，选中第一个地址
            address_list[0].isSelect = true
        }

        return self
    }

    /// 设置默认选择（仅在没有任何选中状态时）
    /// - Returns: 设置了默认选项的ProductAddressModel
    mutating func setDefaultSelectionIfNeeded() -> ProductAddressModel {
        // 检查是否已有选中的地址
        let hasSelectedAddress = default_address.isSelect || address_list.contains { $0.isSelect }

        // 如果没有选中的地址，则设置默认选择
        if !hasSelectedAddress {
            return setDefaultSelection()
        }

        return self
    }
}

// MARK: - 创建地址响应模型
struct CreateAddressResponse: SmartCodable {
    var address_id: Int = 0
}
struct ProductAddressItemModel: SmartCodable {
    // 原字段
    var id: String = ""
    var user_id: String = ""
    var recipient_name: String = ""   // 收件人姓名，对应JSON中的"recipient_name"
    var phone: String = ""
    var detail: String = ""
    var is_default: Bool = false
    var isSelect: Bool = false   // 注意：这个可能是本地使用，不在JSON中
    
    // 新字段
    var city_code: String = ""
    var district_code: String = ""
    var street_code: String = ""
    var created_at: String = ""
    var province: String = ""
    var street: String = ""
    var type: String = ""
    var city: String = ""
    var district: String = ""
    // 注意：JSON中有"recipient_name"，而我们的name就是收件人姓名，所以我们需要将"recipient_name"映射到name
    var postal_code: String = ""
    var province_code: String = ""
    
    // 提供一个计算属性，返回完整的地区字符串（省市区街道）
    var region: String {
        return province + city + district + street
    }
    
}

struct RegionListItemModel: SmartCodable {
    var code: String = ""
    var name: String = ""
    var parent_code: String = ""
    var level: String = ""
    var has_children: Bool = false
    var isSelected: Bool = false
}
