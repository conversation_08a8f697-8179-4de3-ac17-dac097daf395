//
//  ProductRecommendListModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/23.
//

import Foundation
import SmartCodable


/// 商品推荐项模型
struct ProductRecommendItem: SmartCodable {
    /// 商品ID
    var product_id: String = ""
    
    /// 商品名称
    var name: String = ""
    /// 商家id
    var seller_id: String = ""
    
    /// 商品封面图片
    var cover_image: String = ""
    
    /// 售价
    var price: String = ""
    
    /// 原价
    var origin_price: String = ""
    
    /// 浏览量/感兴趣人数
    var view: String = ""
    
    /// 格式化的价格显示
    var formattedPrice: String {
        if price.isEmpty {
            return ""
        }
        return "¥\(price)"
    }
    
    /// 格式化的原价显示
    var formattedOriginPrice: String {
        if origin_price.isEmpty {
            return ""
        }
        return "¥\(origin_price)"
    }
    
    /// 是否显示原价（当原价大于售价时显示）
    var shouldShowOriginPrice: Bool {
        guard let priceValue = Double(price),
              let originPriceValue = Double(origin_price) else {
            return false
        }
        return originPriceValue > priceValue
    }
    
    /// 格式化的浏览量显示
    var formattedView: String {
        if view.isEmpty || view == "0" {
            return "0人感兴趣"
        }
        return view.contains("人感兴趣") ? view : "\(view)人感兴趣"
    }
}

/// 商品推荐列表请求参数
struct ProductRecommendListParams: RequestParametersConvertible {
    /// 广告ID
    var ad_id: String
    
    /// 商品ID
    var product_id: String
    
    /// 每页条数，默认10
    var limit: Int = 10
    
    /// 页码，默认1
    var page: Int = 1
    
    func asParameters() -> [String: Any] {
        return [
            "ad_id": ad_id,
            "product_id": product_id,
            "limit": limit,
            "page": page
        ]
    }
}
