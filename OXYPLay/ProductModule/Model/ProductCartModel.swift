//
//  ProductCartModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/19.
//

import Foundation
import SmartCodable

/// 购物车商品项模型
class CartItem: SmartCodable {
    /// 购物车记录 ID
    var cart_id: String = ""
    
    /// 商品 ID
    var product_id: String = ""
    
    /// 商品名称
    var name: String = ""
    
    /// 商品价格
    var price: Float = 0.0
    
    /// 商品原价（0 表示无原价）
    var origin_price: String = ""
    
    /// 商品规格
    var spec_value_txt: String = ""
    
    /// 邮寄方式
    var postage_type_text: String = ""
    
    /// 邮寄方式1为包邮2为不包邮
    var postage_type:Int = 1
    
    /// 邮费
    var postage_fee: Float = 0.0
    
    /// 商品数量
    var quantity: Int = 1
    
    /// 商品缩略图 URL
    var thumb_image: String = ""
    
    /// 库存数量
    var stock: Int = 0
    
    /// 发货地（可为空）
    var location: String?
    
    /// 商品分类（如：1衣服，3雪具）
    var category: Int = 1
    
    /// 是否选中（本地状态，用于UI显示）
    var isSelected: Bool = false
    
    required init() {}
    
    /// 计算商品总价（不含邮费）
    var totalPrice: Float {
        return price * Float(quantity)
    }

    /// 计算商品总价（含邮费）
    var totalPriceWithPostage: Float {
        let itemTotal = price * Float(quantity)
        // 如果不包邮（postage_type == 2），则加上邮费
        if postage_type == 2 {
            return itemTotal + postage_fee
        }
        return itemTotal
    }

    /// 获取邮费（仅当不包邮时返回邮费，包邮时返回0）
    var actualPostageFee: Float {
        return postage_type == 2 ? postage_fee : 0.0
    }
    
}

/// 购物车卖家分组模型
class CartSeller: SmartCodable {
    /// 卖家 ID
    var seller_id: String = ""
    
    /// 卖家昵称
    var seller_name: String = ""
    
    /// 卖家头像 URL
    var seller_avatar: String = ""
    
    /// 商品列表
    var items: [CartItem] = []
    
    /// 是否选中（本地状态，用于UI显示）
    var isSelected: Bool = false
    
    required init() {}
    
    /// 计算选中商品的总价（不含邮费）
    var selectedTotalPrice: Float {
        return items.filter { $0.isSelected }.reduce(0) { $0 + $1.totalPrice }
    }

    /// 计算选中商品的总价（含邮费）
    var selectedTotalPriceWithPostage: Float {
        return items.filter { $0.isSelected }.reduce(0) { $0 + $1.totalPriceWithPostage }
    }

    /// 计算选中商品的总邮费
    var selectedTotalPostageFee: Float {
        return items.filter { $0.isSelected }.reduce(0) { $0 + $1.actualPostageFee }
    }
    
    /// 计算选中商品的数量
    var selectedItemCount: Int {
        return items.filter { $0.isSelected }.count
    }
    
    /// 是否有选中的商品
    var hasSelectedItems: Bool {
        return items.contains { $0.isSelected }
    }
    
    /// 更新卖家选中状态（根据商品选中状态）
    func updateSelectionState() {
        isSelected = !items.isEmpty && items.allSatisfy { $0.isSelected }
    }
    
    /// 设置所有商品的选中状态
    func setAllItemsSelected(_ selected: Bool) {
        items.forEach { $0.isSelected = selected }
        isSelected = selected
    }
}

/// 购物车列表响应模型
class CartListResponse: SmartCodable {
    /// 卖家分组数据
    var data: [CartSeller] = []
    
    required init() {}
    
    /// 计算所有选中商品的总价（不含邮费）
    var totalSelectedPrice: Float {
        return data.reduce(0) { $0 + $1.selectedTotalPrice }
    }

    /// 计算所有选中商品的总价（含邮费）
    var totalSelectedPriceWithPostage: Float {
        return data.reduce(0) { $0 + $1.selectedTotalPriceWithPostage }
    }

    /// 计算所有选中商品的总邮费
    var totalSelectedPostageFee: Float {
        return data.reduce(0) { $0 + $1.selectedTotalPostageFee }
    }
    
    /// 计算所有选中商品的数量
    var totalSelectedCount: Int {
        return data.reduce(0) { $0 + $1.selectedItemCount }
    }
    
    /// 是否有选中的商品
    var hasSelectedItems: Bool {
        return data.contains { $0.hasSelectedItems }
    }
    
    /// 是否全选
    var isAllSelected: Bool {
        return !data.isEmpty && data.allSatisfy { $0.isSelected }
    }
    
    /// 设置全选状态
    func setAllSelected(_ selected: Bool) {
        data.forEach { $0.setAllItemsSelected(selected) }
    }
    
    /// 获取所有选中的商品
    var selectedItems: [CartItem] {
        return data.flatMap { $0.items.filter { $0.isSelected } }
    }
    
    /// 获取所有选中的购物车ID
    var selectedCartIds: [String] {
        return selectedItems.map { $0.cart_id }
    }
}

/// 购物车操作请求参数
struct CartAddRequest: RequestParametersConvertible {
    let product_id: Int
    let sku_id: Int
    let quantity: Int
    
    func asParameters() -> [String: Any] {
        return [
            "product_id": product_id,
            "sku_id": sku_id,
            "quantity": quantity
        ]
    }
}

/// 购物车数量更新请求参数
struct CartUpdateQuantityRequest: RequestParametersConvertible {
    let id: Int
    let quantity: Int
    
    func asParameters() -> [String: Any] {
        return [
            "id": id,
            "quantity": quantity
        ]
    }
}

/// 购物车删除请求参数
struct CartRemoveRequest: RequestParametersConvertible {
    let ids: [Int]
    
    func asParameters() -> [String: Any] {
        return [
            "ids": ids
        ]
    }
}

/// 空请求参数（用于不需要参数的接口）
struct EmptyRequest: RequestParametersConvertible {
    func asParameters() -> [String: Any] {
        return [:]
    }
}
