//
//  CouponModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/18.
//

struct CouponModel: SmartCodable {
    var available: [CouponItemModel] = []
    var unavailable: [CouponItemModel] = []

    /// 设置默认选择，选中可用优惠券中价格最高的选项
    /// - Returns: 设置了默认选项的CouponModel
    mutating func setDefaultSelection() -> CouponModel {
        // 先清除所有选中状态
        for i in 0..<available.count {
            available[i].isSelect = false
        }
        for i in 0..<unavailable.count {
            unavailable[i].isSelect = false
        }

        // 如果有可用优惠券，选中价格最高的
        if !available.isEmpty {
            // 找到价格最高的优惠券索引
            var maxAmountIndex = 0
            var maxAmount: Float = 0

            for (index, coupon) in available.enumerated() {
                if let amount = Float(coupon.amount), amount > maxAmount {
                    maxAmount = amount
                    maxAmountIndex = index
                }
            }

            // 设置价格最高的优惠券为选中状态
            available[maxAmountIndex].isSelect = true
        }

        return self
    }

    /// 获取当前选中的优惠券价格
    /// - Returns: 当前选中优惠券的价格，如果没有选中则返回"0"
    func getCurrentSelectedPrice() -> String {
        // 在可用优惠券中查找选中的
        for coupon in available {
            if coupon.isSelect {
                return coupon.amount
            }
        }

        // 在不可用优惠券中查找选中的（虽然通常不会选中不可用的）
        for coupon in unavailable {
            if coupon.isSelect {
                return coupon.amount
            }
        }

        return "0"
    }

    /// 获取当前选中的优惠券
    /// - Returns: 当前选中的优惠券，如果没有选中则返回nil
    func getCurrentSelectedCoupon() -> CouponItemModel? {
        // 在可用优惠券中查找选中的
        for coupon in available {
            if coupon.isSelect {
                return coupon
            }
        }

        // 在不可用优惠券中查找选中的
        for coupon in unavailable {
            if coupon.isSelect {
                return coupon
            }
        }

        return nil
    }
}
struct CouponItemModel: SmartCodable {
    var id: String = ""
    var user_id: String = ""
    var title: String = ""
    var amount: String = ""
    var min_spend: String = ""
    var expire_at: String = ""
    var type: String = ""
    var is_used: String = ""
    var created_at: String = ""
    var advertiser_id: String = ""
    var post_id: String = ""
    var order_id: String = ""
    var can_use: Bool = false
    var unavailable_reason: String = ""
    var isSelect:Bool = false
}

