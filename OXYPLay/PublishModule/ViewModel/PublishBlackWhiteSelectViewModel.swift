//
//  PublishBlackWhiteSelectViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/31.
//
class PublishBlackWhiteSelectViewModel: BaseViewModel {
    // MARK: - 属性
    
    /// 用户列表数据
    @Published var userList: [MineAddFriendItemModel] = []


        
    // MARK: - 数据加载
    
    /// 重写刷新数据方法
    override func refreshData() {
        fetchUserList(refresh: true)
    }

    /// 重写加载更多数据方法
    override func loadMoreData() {
        fetchUserList(refresh: false)
    }

    /// 加载用户列表数据
    /// - Parameter refresh: 是否刷新（重置页码）
    func fetchUserList(refresh: Bool = false) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

        // 构建请求参数
        let params = RequestParameters([
            "page": currentPage,
            "limit": pageSize
        ])
        // 使用便捷的分页数据请求方法
        requestPageData(MineService.friendsList(params: params), type: MineAddFriendItemModel.self, isRefresh: refresh) { [weak self] newData, isRefresh in
            guard let self = self else { return }

            if isRefresh {
                self.userList = newData
            } else {
                self.userList.append(contentsOf: newData)
            }
        }
        .sink(
            receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    print("获取列表失败: \(error.localizedDescription)")
                }
            },
            receiveValue: { [weak self] pageResponse in
                guard let self = self else { return }
                print("用户列表加载成功")
            }
        )
        .store(in: &cancellables)
    }   
}

