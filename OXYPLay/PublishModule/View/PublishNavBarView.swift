//
//  PublishNavBarView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/10.
//

protocol PublishNavBarViewDelegate: AnyObject {
    func navBarViewDidTapBack(_ navBarView: PublishNavBarView)
}

class PublishNavBarView: BaseView {
    
    // MARK: - 属性
    
    weak var delegate: PublishNavBarViewDelegate?
    
    // MARK: - UI组件
    
    lazy var backItem = BaseButton().then{
        $0.setImage(UIImage(systemName: "chevron.left")?.withAlwaysOriginalTintColor(.black), for: .normal)
    }
    lazy var titleLabel = UILabel().then{
        $0.text = "发布笔记"
        $0.textColor = color_2B2C2F
        $0.font = .systemFont(ofSize: 16, weight: .medium)
    }
    
    
    override func configUI() {
        self.backgroundColor = .white
        addSubviews([backItem,titleLabel])
        setupBindings()
    }
    
    override func configLayout() {
        backItem.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.bottom.equalTo(-16)
        }
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalTo(backItem)
        }
       
    }
    
    // MARK: - 绑定事件
    
    override func setupBindings() {
        backItem.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.navBarViewDidTapBack(self)
            }
            .store(in: &cancellables)
        
    }
   
}
