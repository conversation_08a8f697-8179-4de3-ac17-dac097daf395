//
//  PublishBlackWhiteSelectController.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/31.
//

class PublishBlackWhiteSelectController:BaseViewController{
    var isBlack = true{
        didSet{
            self.title = isBlack ? "不给谁看" : "只给谁看"
        }
    }
    // MARK: - 属性
    let viewModel = PublishBlackWhiteSelectViewModel()
    let selectCompletedPublisher = PassthroughSubject<[MineAddFriendItemModel], Never>()

    // MARK: - UI组件

   
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.register(PublishBlackWhiteSelectCell.self, forCellReuseIdentifier: "PublishBlackWhiteSelectCell")
        tableView.delegate = self
        tableView.dataSource = self
        tableView.rowHeight = UITableView.automaticDimension
        tableView.estimatedRowHeight = 70
        tableView.backgroundColor = color_F6F8F9
        return tableView
    }()
    lazy var bottomView = PublishBlackWhiteBottomView().then{
        $0.delegate = self
    }

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configLayout()
        setupBindings()
        loadData()
    }

    // MARK: - 配置UI

    override func configUI() {
        super.configUI()
        view.addSubview(tableView)
        view.addSubview(bottomView)
    }

    override func configLayout() {
        super.configLayout()
        // 表格视图
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        bottomView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(100)
        }
    }

    override func setupBindings() {
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: tableView, with: viewModel)

        // 监听用户列表变化
        viewModel.$userList
            .receive(on: DispatchQueue.main)
            .sink { [weak self] userList in
                guard let self = self else { return }
                self.tableView.reloadData()
                self.bottomView.configList(list: userList.filter{$0.isSelect == true})
            }
            .store(in: &cancellables)

    }

    // MARK: - 私有方法

    private func loadData() {
        viewModel.fetchUserList(refresh: true)
    }
}

// MARK: - UITableViewDataSource

extension PublishBlackWhiteSelectController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.userList.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "PublishBlackWhiteSelectCell", for: indexPath) as! PublishBlackWhiteSelectCell
        let userModel = viewModel.userList[indexPath.row]
        cell.userModel = userModel
        return cell
    }
}

// MARK: - UITableViewDelegate

extension PublishBlackWhiteSelectController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let view = UIView()
        let titleLabel = UILabel()
        titleLabel.text = "选择好友和粉丝"
        titleLabel.textColor = UIColor(hexString: "#2B2C2F", transparency: 0.8)
        titleLabel.font = .systemFont(ofSize: 12, weight: .regular)
        view.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
        }
        return view
    }
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 36
    }
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        for(index,_) in viewModel.userList.enumerated(){
            if index == indexPath.row {
                viewModel.userList[index].isSelect = true
            }
        }
    }
}
extension PublishBlackWhiteSelectController:PublishBlackWhiteBottomViewDelegate{
    func delete(_ userid: String) {
        for(index,model) in viewModel.userList.enumerated(){
            if model.id == userid {
                viewModel.userList[index].isSelect = false
            }
        }
    }
    
    func okButtonClick() {
     let ids =   viewModel.userList.filter{$0.isSelect}
        selectCompletedPublisher.send(ids)
        self.navigationController?.popViewController(animated: true)
    }
    
    
}
