//
//  PublishPostTypeSelectController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/31.
//

class PublishPostTypeSelectController: BasePresentController {

    // MARK: - Properties

  
    override var presentationHeight: CGFloat {
        return 450
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "请选择发货方式", bottomTitle: "确定")
        // 添加列表视图
        contentView.addSubview(自定义view)
       
    }

    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        bottomButton.tapPublisher
            .sink { [weak self] _ in
                
                self!.dismiss(animated: true)
            }
            .store(in: &cancellables)
    }

  
}
