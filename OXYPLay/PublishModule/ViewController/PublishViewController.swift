//
//  PublishViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/10.
//

import UIKit
import ZLPhotoBrowser
import Combine
import CombineCocoa
import Photos
import PhotosUI
import AVFoundation

class PublishViewController: BaseViewController {
    
    // MARK: - 属性
    
    private var viewModel = PublishViewModel()
    private var noteTypeSelectItemConfig = ListItemConfig.singleSelect(
        identifier: "publish_find_people",
        title: "找人",
        iconString: "publish_find_people",
        isSelected: false,
        subTitle: "教练/雪搭子/陪滑/学员",
        data: "2")
    
    /// 公开可见配置
    private var visibilitySelectItemConfig = ListItemConfig.singleSelect(
        identifier: "全部可见",
        title: "全部可见",
        iconString: "全部可见",
        isSelected: true,
        data: "1")
    
    // MARK: - UI组件
    
    lazy var navBar = PublishNavBarView().then{
        $0.delegate = self
    }
    
    lazy var listView = BaseListView().then {
        $0.delegate = self
    }
    private lazy var toolBar = BaseTabToolBar().then {
        $0.delegate = self
    }
    
    /// 选中的用户列表（黑白名单）
    var selectedUsersList: [MineAddFriendItemModel] = []
    
    /// 发货方式配置
    private var postageTypeSelectItemConfig = ListItemConfig.singleSelect(
        identifier: "包邮",
        title: "包邮",
        isSelected: true,
        data: "1")
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configureListItems()
        setupBindings()
    }
    
    // MARK: - 数据绑定
    
    override func setupBindings() {
        // 监听上传状态
        viewModel.$uploadState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.handleUploadState(state)
            }
            .store(in: &cancellables)
        
        
        
        // 监听发布状态
        viewModel.$publishState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.handlePublishState(state)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - UI配置
    
    override func configUI(){
        self.fd_prefersNavigationBarHidden = true
        
        // 配置 toolBar
        toolBar.configureLeftMultipleRightFull(leftItems: [
            ToolBarButtonItem(normalImage: UIImage(named: "publish_draft"),
                              selectedImage: UIImage(named: "publish_draft"),
                              title: "存草稿"),
            ToolBarButtonItem(normalImage: UIImage(named: "publish_see"),
                              selectedImage: UIImage(named: "publish_see"),
                              title: "看预览")
        ], rightButtonTitle: "立即发布")
        
        // 添加导航栏
        view.addSubview(navBar)
        navBar.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalNavBarHeight)
        }
        
        // 添加列表视图
        view.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.top.equalTo(navBar.snp.bottom)
            make.left.right.equalToSuperview()
        }
        
        // 添加底部工具栏
        view.addSubview(toolBar)
        toolBar.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.top.equalTo(listView.snp.bottom)
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
    }
    // MARK: - 配置列表项
    
    private func configureListItems() {
        let items: [[ListItemConfig]] = [
            [
                ListItemConfig(type: .image, identifier: "img_urls", isRequired: true, placeholder: "添加图片"),
                ListItemConfig(type: .input, identifier: "title", isRequired: true, placeholder: "添加标题"),
                ListItemConfig(type: .content, identifier: "description", isRequired: true, placeholder: "添加正文")
            ],
            [ListItemConfig(type: .select, identifier: "type",data: noteTypeSelectItemConfig.data,isRequired: true,iconString:"publish_notetype", title:"笔记类型",subTitle: noteTypeSelectItemConfig.title),
             ListItemConfig(type: .select, identifier: "location", isRequired: true, iconString:"publish_location", title:"地点定位")
            ],
            [ListItemConfig(type: .select, identifier: "visibility", data: visibilitySelectItemConfig.data, iconString:"publish_visibility", title:"公开可见", subTitle: visibilitySelectItemConfig.title)],
            
            [ListItemConfig(type: .InputIcon, identifier: "price", iconString:"publish_price", title:"出售价格"),
             ListItemConfig(type: .InputPrice, identifier: "InputPrice"),
             ListItemConfig(type: .select, identifier: "postage_type", data: postageTypeSelectItemConfig.data, iconString:"publish_method", title:"发货方式", subTitle: postageTypeSelectItemConfig.title)]
        ]
        
        // 设置列表项
        listView.setItems(items)
    }
    
    // MARK: - 上传处理方法
    
    /// 处理上传状态变化
    private func handleUploadState(_ state: RequestState) {
        switch state {
        case .idle:
            break
        case .loading:
            // 上传状态在publishNote方法中已经显示
            break
        case .success:
            // 图片上传成功，开始发布帖子
            self.handleImageUploadCompleted()
        case .failure(let message):
            MBProgressHUD.hide(for: self.view, animated: true)
            MBProgressHUD.showError(message, in: self.view)
        }
    }
    
    /// 处理发布状态变化
    private func handlePublishState(_ state: RequestState) {
        switch state {
        case .idle:
            break
        case .loading:
            // 发布状态在handleImageUploadCompleted方法中已经显示
            break
        case .success:
            MBProgressHUD.hide(for: self.view, animated: true)
            MBProgressHUD.showSuccess("发布成功", in: self.view)
            // 延迟返回上一页
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                self.navigationController?.popViewController(animated: true)
            }
        case .failure(let message):
            MBProgressHUD.hide(for: self.view, animated: true)
            MBProgressHUD.showError(message, in: self.view)
        }
    }
    
    
    
    /// 显示错误提示
    private func showErrorAlert(_ message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    /// 更新公开可见显示
    private func updateVisibilityDisplay() {
        let updatedConfig = ListItemConfig(
            type: .select,
            identifier: "visibility",
            data: visibilitySelectItemConfig.data,
            iconString: "publish_visibility",
            title: "公开可见",
            subTitle: visibilitySelectItemConfig.title + (visibilitySelectItemConfig.subTitle.isEmpty ? "" : "：\(visibilitySelectItemConfig.subTitle)")
        )
        listView.updateItem(with: updatedConfig)
    }
    
    /// 更新发货方式显示
    private func updatePostageTypeDisplay() {
        let updatedConfig = ListItemConfig(
            type: .select,
            identifier: "postage_type",
            data: postageTypeSelectItemConfig.data,
            iconString: "publish_method",
            title: "发货方式",
            subTitle: postageTypeSelectItemConfig.title
        )
        listView.updateItem(with: updatedConfig)
    }
    
    // MARK: - 发布笔记
    
    private func publishNote() {
        // 获取表单数据
        let formData = listView.getAllData()
        print(formData)
        guard let images = formData["img_urls"] as? [UIImage], !images.isEmpty else {
            MBProgressHUD.showPrompt("请添加图片", in: view)
            return
        }
        
        // 验证必填字段
        guard let title = formData["title"] as? String, !title.isEmpty else {
            MBProgressHUD.showPrompt("请添加标题", in: view)
            return
        }
        
        guard let description = formData["description"] as? String, !description.isEmpty else {
            MBProgressHUD.showPrompt("请添加正文", in: view)
            return
        }
        guard let type = formData["type"] as? String, !type.isEmpty else {
            MBProgressHUD.showPrompt("请选择笔记类型", in: view)
            return
        }
//        guard let description = formData["location"] as? String, !description.isEmpty else {
//            MBProgressHUD.showPrompt("请选择地点", in: view)
//            return
//        }
        var isPass = true
        if let price = formData["price"] as? String, !price.isEmpty {
            guard let  quantity = formData["quantity"] as? String,quantity.count > 0 else {
                MBProgressHUD.showPrompt("请填写出售数量", in: view)
                isPass = false
                return
            }
        }
        if isPass {
            // 先上传图片
            MBProgressHUD.showLoading(in: view, text: "上传图片中...")
            viewModel.uploadImages(images)
        }
      
    }
    
    /// 处理图片上传完成后的发布逻辑
    private func handleImageUploadCompleted() {
        // 隐藏加载提示
        MBProgressHUD.hide(for: view, animated: true)
        
        // 获取表单数据
        let formData = listView.getAllData()
        
        // 构建发布请求
        var request = CreateServicePostRequest()
        
        // 基础信息
        request.type = Int(noteTypeSelectItemConfig.data as? String ?? "2") ?? 2
        request.title = formData["title"] as? String ?? ""
        request.description = formData["description"] as? String ?? ""
        request.location = formData["location"] as? String ?? "崇礼-富龙"
        
        // 图片信息
        request.img_urls = viewModel.uploadedFileUrls
        
        // 价格信息
        if let price = formData["price"] as? String, !price.isEmpty {
            request.price = price
        }
        if let quantity = formData["quantity"] as? String,let quantity = Int(quantity) {
            request.quantity = quantity
        }
        if let origin_price = formData["origin_price"] as? String, !origin_price.isEmpty {
            request.origin_price = origin_price
        }
        
        // 发货方式
        if let postageType = postageTypeSelectItemConfig.data as? String {
            request.postage_type = Int(postageType)
        }
        
        // 可见性设置
        if let visibility = visibilitySelectItemConfig.data as? String {
            request.visibility = Int(visibility) ?? 1
        }
        
        // 显示发布中提示
        MBProgressHUD.showLoading(in: view, text: "发布中...")
        
        // 发布帖子
        viewModel.publishServicePost(request, selectedUsers: selectedUsersList)
    }
}

// MARK: - BaseListViewDelegate
extension PublishViewController: BaseListViewDelegate {
    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        switch config.identifier.lowercased() {
        case "img_urls":
            print("选择笔记类型")
            break
        case "postage_type":
            let vc = PublishPostTypeSelectController()
            vc.selectListConfig = postageTypeSelectItemConfig
            
            vc.selectCompletedPublisher
                .receive(on: DispatchQueue.main)
                .sink { [weak self] selectedConfig in
                    guard let self = self else { return }
                    self.postageTypeSelectItemConfig = selectedConfig
                    self.updatePostageTypeDisplay()
                }
                .store(in: &cancellables)
            
            self.customPresent(vc, animated: true)
            break
        case "type":
            let vc = PublishNoteTypeSelectController()
            vc.selectListConfig = self.noteTypeSelectItemConfig
            vc.selectCompletedPublisher
                .receive(on: DispatchQueue.main)
                .sink { [weak self] listconfig in
                    guard let self = self else { return }
                    self.noteTypeSelectItemConfig = listconfig
                    let listconfig =  ListItemConfig(type: .select, identifier: "type",data: self.noteTypeSelectItemConfig.data,isRequired: true,iconString:"publish_notetype", title:"笔记类型",subTitle: self.noteTypeSelectItemConfig.title)
                    self.listView.updateItem(with: listconfig)
                }
                .store(in: &cancellables)
            self.customPresent(vc, animated: true)
            break
        case "location":
            
            break
        case "visibility":
            let vc = PublishVisibilitySelectController()
            vc.selectListConfig = visibilitySelectItemConfig
            
            // 监听直接选择完成（全部可见、仅自己可见、仅互关好友可见）
            vc.selectCompletedPublisher
                .receive(on: DispatchQueue.main)
                .sink { [weak self] selectedConfig in
                    guard let self = self else { return }
                    self.visibilitySelectItemConfig = selectedConfig
                    self.updateVisibilityDisplay()
                }
                .store(in: &cancellables)
            
            // 监听需要跳转到黑白名单选择（不给谁看、只给谁看）
            vc.pushBlackWhitePublisher
                .receive(on: DispatchQueue.main)
                .sink { [weak self] selectConfig in
                    guard let self = self else { return }
                    let blackVc = PublishBlackWhiteSelectController()
                    blackVc.isBlack = selectConfig.data as! String == "4" // "4"表示不给谁看，"5"表示只给谁看
                    self.pushVc(blackVc, animated: true)
                    
                    // 监听黑白名单选择完成
                    blackVc.selectCompletedPublisher
                        .receive(on: DispatchQueue.main)
                        .sink { [weak self] userlist in
                            guard let self = self else { return }
                            self.selectedUsersList = userlist
                            
                            // 更新配置并显示
                            var updatedConfig = selectConfig
                            let userNames = userlist.map { $0.nickname }
                            updatedConfig.subTitle = userNames.isEmpty ? "" : userNames.joined(separator: ", ")
                            self.visibilitySelectItemConfig = updatedConfig
                            self.updateVisibilityDisplay()
                        }
                        .store(in: &cancellables)
                }
                .store(in: &cancellables)
            
            self.customPresent(vc, animated: true)
            break
     
      

        default:
            print("点击了: \(config.identifier)")
        }
    }
    
    
    func listViewValidate(_ listView: BaseListView, message: String) {
        MBProgressHUD.showPrompt(message, in: view)
    }
    func listViewMentionUser(_ listView: BaseListView, config: ListItemConfig){
        
    }
    
    
    
}

// MARK: - BaseTabToolBarDelegate

extension PublishViewController: TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickLeftButtonAt index: Int, item: ToolBarButtonItem) {
        switch index {
        case 0:
            // 存草稿
            print("点击了存草稿按钮")
        case 1:
            // 看预览
            print("点击了看预览按钮")
        default:
            break
        }
    }
    
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        // 立即发布
        publishNote()
    }
}

// MARK: - PublishNavBarViewDelegate

extension PublishViewController: PublishNavBarViewDelegate {
    func navBarViewDidTapBack(_ navBarView: PublishNavBarView) {
        self.dismiss(animated: true)
    }
}
