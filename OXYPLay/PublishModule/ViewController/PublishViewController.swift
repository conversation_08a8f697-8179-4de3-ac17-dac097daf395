//
//  PublishViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/10.
//

import UIKit
import ZLPhotoBrowser
import Combine
import CombineCocoa
import Photos
import PhotosUI
import AVFoundation

class PublishViewController: BaseViewController {
    
    // MARK: - 属性
    
    private var viewModel = PublishViewModel()
    private var noteTypeSelectItemConfig = ListItemConfig.singleSelect(
        identifier: "publish_find_people",
        title: "找人",
        iconString: "publish_find_people",
        isSelected: false,
        subTitle: "教练/雪搭子/陪滑/学员",
        data: "2")
    
    // MARK: - UI组件
    
    lazy var navBar = PublishNavBarView().then{
        $0.delegate = self
    }
    
    lazy var listView = BaseListView().then {
        $0.delegate = self
    }
    private lazy var toolBar = BaseTabToolBar().then {
        $0.delegate = self
    }
    var usersList:[MineAddFriendItemModel] = [MineAddFriendItemModel]()
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configureListItems()
        setupBindings()
    }
    
    // MARK: - 数据绑定
    
    override func setupBindings() {
        // 监听上传状态
        viewModel.$uploadState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.handleUploadState(state)
            }
            .store(in: &cancellables)
        
        
        // 监听已上传文件URL
        viewModel.$uploadedFileUrls
            .receive(on: DispatchQueue.main)
            .sink { [weak self] urls in
                self?.handleUploadedFileUrls(urls)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - UI配置
    
    override func configUI(){
        self.fd_prefersNavigationBarHidden = true
        
        // 配置 toolBar
        toolBar.configureLeftMultipleRightFull(leftItems: [
            ToolBarButtonItem(normalImage: UIImage(named: "publish_draft"),
                              selectedImage: UIImage(named: "publish_draft"),
                              title: "存草稿"),
            ToolBarButtonItem(normalImage: UIImage(named: "publish_see"),
                              selectedImage: UIImage(named: "publish_see"),
                              title: "看预览")
        ], rightButtonTitle: "立即发布")
        
        // 添加导航栏
        view.addSubview(navBar)
        navBar.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalNavBarHeight)
        }
        
        // 添加列表视图
        view.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.top.equalTo(navBar.snp.bottom)
            make.left.right.equalToSuperview()
        }
        
        // 添加底部工具栏
        view.addSubview(toolBar)
        toolBar.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.top.equalTo(listView.snp.bottom)
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
    }
    // MARK: - 配置列表项
    
    private func configureListItems() {
        let items: [[ListItemConfig]] = [
            [
                ListItemConfig(type: .image, identifier: "img_urls", isRequired: true, placeholder: "添加图片"),
                ListItemConfig(type: .input, identifier: "title", isRequired: true, placeholder: "添加标题"),
                ListItemConfig(type: .content, identifier: "description", isRequired: true, placeholder: "添加正文")
            ],
            [ListItemConfig(type: .select, identifier: "type",data: noteTypeSelectItemConfig.data,isRequired: true,iconString:"publish_notetype", title:"笔记类型",subTitle: noteTypeSelectItemConfig.title),
             ListItemConfig(type: .select, identifier: "location", isRequired: true, iconString:"publish_location", title:"地点定位")
            ],
            [ListItemConfig(type: .select, identifier: "publish_visibility", iconString:"publish_visibility", title:"公开可见")],
            
            [ListItemConfig(type: .InputIcon, identifier: "price", iconString:"publish_price", title:"出售价格"),
             ListItemConfig(type: .InputPrice, identifier: "InputPrice"),
             ListItemConfig(type: .select, identifier: "postage_type", iconString:"publish_method", title:"发货方式")]
        ]
        
        // 设置列表项
        listView.setItems(items)
    }
    
    // MARK: - 上传处理方法
    
    /// 处理上传状态变化
    private func handleUploadState(_ state: RequestState) {
        switch state {
        case .idle:
            break
        case .loading:
            MBProgressHUD.showLoading(in: self.view, text: "正在上传...")
        case .success:
            MBProgressHUD.hide(for: self.view, animated: true)
            MBProgressHUD.showSuccess("上传成功", in: self.view)
        case .failure(let message):
            MBProgressHUD.hide(for: self.view, animated: true)
            showErrorAlert(message)
        }
    }
    
    
    /// 处理已上传文件URL
    private func handleUploadedFileUrls(_ urls: [String]) {
        print("已上传文件数量: \(urls.count)")
        print("已上传文件URLs: \(urls)")
        // 可以在这里更新UI显示已上传的文件
        // 例如：更新图片预览、显示上传成功的文件列表等
    }
    
    /// 显示错误提示
    private func showErrorAlert(_ message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    // MARK: - 发布笔记
    
    private func publishNote() {
        //        // 验证表单
        //        guard listView.validateAllItems() else {
        //            return
        //        }
        
        // 获取表单数据
        let formData = listView.getAllData()
        guard  let images = formData["img_urls"] as? [UIImage] else {
            return
        }
        viewModel.uploadImages(images)
        print(formData)
        
    }
}

// MARK: - BaseListViewDelegate
extension PublishViewController: BaseListViewDelegate {
    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        switch config.identifier {
        case "img_urls":
            print("选择笔记类型")
        case "type":
            let vc = PublishNoteTypeSelectController()
            vc.selectListConfig = self.noteTypeSelectItemConfig
            vc.selectCompletedPublisher
                .receive(on: DispatchQueue.main)
                .sink { [weak self] listconfig in
                    guard let self = self else { return }
                    self.noteTypeSelectItemConfig = listconfig
                    let listconfig =  ListItemConfig(type: .select, identifier: "type",data: self.noteTypeSelectItemConfig.data,isRequired: true,iconString:"publish_notetype", title:"笔记类型",subTitle: self.noteTypeSelectItemConfig.title)
                    self.listView.updateItem(with: listconfig)
                }
                .store(in: &cancellables)
            self.customPresent(vc, animated: true)
        case "location":
            print("选择地点")
        case "publish_visibility":
            let vc = PublishVisibilitySelectController()
            vc.pushBlackWhitePublisher.receive(on: DispatchQueue.main)
                .sink { [weak self] selectConfig in
                    guard let self = self else { return }
                    let blackVc = PublishBlackWhiteSelectController()
                    blackVc.isBlack = selectConfig.data as! String == "4"
                    self.pushVc(blackVc, animated: true)
                    blackVc.selectCompletedPublisher.receive(on: DispatchQueue.main)
                        .sink { [weak self] userlist in
                            guard let self = self else { return }
                            self.usersList = userlist
                            let vc = PublishVisibilitySelectController()
                            var selectConfig = selectConfig
                            selectConfig.subTitle = self.usersList.map{$0.nickname}.joined(separator: ", ")
                            vc.selectListConfig = selectConfig
                            self.customPresent(vc, animated: true)
                        }
                        .store(in: &cancellables)
                }
                .store(in: &cancellables)
            self.customPresent(vc, animated: true)
        case "postage_type":
            let vc = PublishPostTypeSelectController()
            self.customPresent(vc, animated: true)
        default:
            print("点击了: \(config.identifier)")
        }
    }
    
    
    func listViewValidate(_ listView: BaseListView, message: String) {
        MBProgressHUD.showPrompt(message, in: view)
    }
    func listViewMentionUser(_ listView: BaseListView, config: ListItemConfig){
        
    }
    
    
    
}

// MARK: - BaseTabToolBarDelegate

extension PublishViewController: TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickLeftButtonAt index: Int, item: ToolBarButtonItem) {
        switch index {
        case 0:
            // 存草稿
            print("点击了存草稿按钮")
        case 1:
            // 看预览
            print("点击了看预览按钮")
        default:
            break
        }
    }
    
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        // 立即发布
        publishNote()
    }
}

// MARK: - PublishNavBarViewDelegate

extension PublishViewController: PublishNavBarViewDelegate {
    func navBarViewDidTapBack(_ navBarView: PublishNavBarView) {
        self.dismiss(animated: true)
    }
}
