//
//  PublishNoteModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/15.
//

import UIKit
import SmartCodable

/// 发布笔记模型
struct PublishNoteModel: SmartCodable {
    /// 标题
    var title: String = ""
    
    /// 内容
    var content: String = ""
    
    /// 图片URL数组
    var imageUrls: [String] = []
    
    /// 分类
    var category: String = ""
    
    /// 位置
    var location: String = ""
    
    /// 可见性
    var visibility: String = "全部可见"
    
    /// 价格
    var price: String?
    
    /// 数量
    var quantity: String?
    
    /// 发布时间
    var publishTime: String?
    
    /// 用户ID
    var userId: String?
    
    
}

/// 发布笔记响应模型
struct PublishNoteResponse: SmartCodable {
    /// 是否成功
    var success: Bool = false

    /// 错误信息
    var message: String?

    /// 笔记ID
    var noteId: String?
}

/// 上传文件响应模型
struct UploadFileResponse: SmartCodable {
    /// 上传后的文件URL（CDN域名）
    var url: String = ""

    /// 文件类型：image 或 video
    var type: String = ""

    /// 图片宽度（仅图片时返回）
    var width: Int?

    /// 图片高度（仅图片时返回）
    var height: Int?
}
