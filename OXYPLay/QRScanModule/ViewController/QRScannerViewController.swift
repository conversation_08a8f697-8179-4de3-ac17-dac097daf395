//
//  QRScannerViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/22.
//

import UIKit
import AVFoundation
import EFQRCode
import Combine
import CombineCocoa
import SnapKit
import Then

class QRScannerViewController: BaseViewController {
    
    // MARK: - Properties
    
    private let viewModel: QRScannerViewModel
    private var captureSession: AVCaptureSession?
    private var previewLayer: AVCaptureVideoPreviewLayer?
    private var scannerAnimationTimer: Timer?
    private var videoCaptureDevice: AVCaptureDevice?
    private var pinchGesture: UIPinchGestureRecognizer?
    private var tapGesture: UITapGestureRecognizer?
    
    // MARK: - UI Components
    
    private lazy var scannerContainerView = UIView().then {
        $0.backgroundColor = .clear
    }
    
    private lazy var scannerOverlayView = QRScannerOverlayView().then {
        $0.backgroundColor = .clear
    }
    
    private lazy var scanLineView = UIView().then {
        $0.backgroundColor = UIColor(red: 0.2, green: 0.71, blue: 0.898, alpha: 0.8)
    }
    
    private lazy var torchButton = UIButton().then {
        $0.setImage(UIImage(systemName: "flashlight.off.fill"), for: .normal)
        $0.tintColor = .white
        $0.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        $0.layer.cornerRadius = 25
    }
    
    private lazy var albumButton = UIButton().then {
        $0.setImage(UIImage(systemName: "photo.on.rectangle"), for: .normal)
        $0.tintColor = .white
        $0.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        $0.layer.cornerRadius = 25
    }
    
    private lazy var generateButton = UIButton().then {
        $0.setImage(UIImage(systemName: "qrcode.viewfinder"), for: .normal)
        $0.tintColor = .white
        $0.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        $0.layer.cornerRadius = 25
    }
    
    private lazy var closeButton = UIButton().then {
        $0.setImage(UIImage(systemName: "xmark"), for: .normal)
        $0.tintColor = .white
        $0.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        $0.layer.cornerRadius = 20
    }
    
    private lazy var autoFocusButton = UIButton().then {
        $0.setImage(UIImage(systemName: "viewfinder"), for: .normal)
        $0.tintColor = .white
        $0.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        $0.layer.cornerRadius = 25
    }
    
    private lazy var focusIndicatorView = UIView().then {
        $0.backgroundColor = .clear
        $0.layer.borderColor = UIColor.yellow.cgColor
        $0.layer.borderWidth = 2
        $0.layer.cornerRadius = 40
        $0.isHidden = true
    }
    
    private lazy var tipLabel = UILabel().then {
        $0.text = "将二维码/条码放入框内，即可自动扫描"
        $0.textColor = .white
        $0.font = UIFont.systemFont(ofSize: 14)
        $0.textAlignment = .center
    }
    
    // MARK: - Initialization
    
    override init() {
        self.viewModel = QRScannerViewModel()
        super.init()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupGestures()
        setupCaptureSession()
        setupBindings()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        startScanning()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopScanning()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = .black
        
        view.addSubview(scannerContainerView)
        view.addSubview(scannerOverlayView)
        view.addSubview(tipLabel)
        view.addSubview(torchButton)
        view.addSubview(albumButton)
        view.addSubview(generateButton)
        view.addSubview(closeButton)
        view.addSubview(autoFocusButton)
        view.addSubview(focusIndicatorView)
        
        scannerContainerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        scannerOverlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        tipLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(scannerOverlayView.scanWindowTopConstraint).offset(280)
            make.left.right.equalToSuperview().inset(20)
        }
        
        torchButton.snp.makeConstraints { make in
            make.width.height.equalTo(50)
            make.left.equalToSuperview().offset(40)
            make.bottom.equalToSuperview().offset(-100)
        }
        
        albumButton.snp.makeConstraints { make in
            make.width.height.equalTo(50)
            make.centerX.equalToSuperview()
            make.centerY.equalTo(torchButton)
        }
        
        generateButton.snp.makeConstraints { make in
            make.width.height.equalTo(50)
            make.right.equalToSuperview().offset(-40)
            make.centerY.equalTo(torchButton)
        }
        
        closeButton.snp.makeConstraints { make in
            make.width.height.equalTo(40)
            make.top.equalToSuperview().offset(50)
            make.left.equalToSuperview().offset(20)
        }
        
        autoFocusButton.snp.makeConstraints { make in
            make.width.height.equalTo(40)
            make.top.equalToSuperview().offset(50)
            make.right.equalToSuperview().offset(-20)
        }
        
        focusIndicatorView.snp.makeConstraints { make in
            make.width.height.equalTo(80)
            make.center.equalToSuperview()
        }
        
        // 添加扫描线
        scannerOverlayView.addSubview(scanLineView)
        scanLineView.snp.makeConstraints { make in
            make.left.right.equalTo(scannerOverlayView.scanWindowLeftConstraint).inset(5)
            make.height.equalTo(2)
            make.top.equalTo(scannerOverlayView.scanWindowTopConstraint)
        }
    }
    
    private func setupGestures() {
        // 添加捏合手势用于变焦
        pinchGesture = UIPinchGestureRecognizer(target: self, action: #selector(handlePinchGesture(_:)))
        if let pinchGesture = pinchGesture {
            view.addGestureRecognizer(pinchGesture)
        }
        
        // 添加点击手势用于对焦
        tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTapGesture(_:)))
        if let tapGesture = tapGesture {
            view.addGestureRecognizer(tapGesture)
        }
    }
    
   override func setupBindings() {
        // UI控件事件绑定
        closeButton.tapPublisher
            .sink { [weak self] _ in
                self?.navigationController?.popViewController(animated: true)
            }
            .store(in: &cancellables)
        
        torchButton.tapPublisher
            .sink { [weak self] _ in
                self?.toggleTorch()
            }
            .store(in: &cancellables)
        
        albumButton.tapPublisher
            .sink { [weak self] _ in
                self?.openPhotoLibrary()
            }
            .store(in: &cancellables)
        
        generateButton.tapPublisher
            .sink { [weak self] _ in
                self?.showQRCodeGenerator()
            }
            .store(in: &cancellables)
        
        autoFocusButton.tapPublisher
            .sink { [weak self] _ in
                self?.toggleAutoFocus()
            }
            .store(in: &cancellables)
        
        // ViewModel输出绑定
        viewModel.$scanResult
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] result in
                self?.handleScanResult(result.content)
            }
            .store(in: &cancellables)
        
        viewModel.$error
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] errorMessage in
                self?.showAlert(title: "错误", message: errorMessage)
            }
            .store(in: &cancellables)
        
        viewModel.$isTorchEnabled
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isOn in
                let imageName = isOn ? "flashlight.on.fill" : "flashlight.off.fill"
                self?.torchButton.setImage(UIImage(systemName: imageName), for: .normal)
            }
            .store(in: &cancellables)
            
        viewModel.$shouldAutoFocus
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isAutoFocus in
                let imageName = isAutoFocus ? "viewfinder.circle.fill" : "viewfinder"
                self?.autoFocusButton.setImage(UIImage(systemName: imageName), for: .normal)
            }
            .store(in: &cancellables)
            
        viewModel.$focusPoint
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] point in
                self?.showFocusIndicator(at: point)
            }
            .store(in: &cancellables)
    }
    
    private func setupCaptureSession() {
        captureSession = AVCaptureSession()
        
        guard let videoCaptureDevice = AVCaptureDevice.default(for: .video),
              let videoInput = try? AVCaptureDeviceInput(device: videoCaptureDevice),
              let captureSession = captureSession,
              captureSession.canAddInput(videoInput) else {
            viewModel.error = "无法访问相机"
            showAlert(title: "扫描失败", message: "无法访问相机")
            return
        }
        
        // 保存视频捕获设备引用
        self.videoCaptureDevice = videoCaptureDevice
        
        // 配置相机以支持自动对焦
        do {
            try videoCaptureDevice.lockForConfiguration()
            
            if videoCaptureDevice.isFocusModeSupported(.continuousAutoFocus) {
                videoCaptureDevice.focusMode = .continuousAutoFocus
            }
            
            if videoCaptureDevice.isExposureModeSupported(.continuousAutoExposure) {
                videoCaptureDevice.exposureMode = .continuousAutoExposure
            }
            
            videoCaptureDevice.unlockForConfiguration()
        } catch {
            print("无法配置相机设备: \(error)")
        }
        
        captureSession.addInput(videoInput)
        
        let metadataOutput = AVCaptureMetadataOutput()
        
        if captureSession.canAddOutput(metadataOutput) {
            captureSession.addOutput(metadataOutput)
            
            metadataOutput.setMetadataObjectsDelegate(self, queue: DispatchQueue.main)
            // 使用ViewModel中定义的元数据类型
            metadataOutput.metadataObjectTypes = viewModel.getMetadataObjectTypes()
            
            previewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
            previewLayer?.videoGravity = .resizeAspectFill
            previewLayer?.frame = view.layer.bounds
            
            if let previewLayer = previewLayer {
                scannerContainerView.layer.addSublayer(previewLayer)
            }
            
            // 设置扫描区域
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                guard let self = self else { return }
                self.updateMetadataOutputRectOfInterest()
            }
            
            // 启动扫描动画
            startScanAnimation()
        } else {
            viewModel.error = "无法初始化扫描功能"
            showAlert(title: "扫描失败", message: "无法初始化扫描功能")
        }
    }
    
    private func updateMetadataOutputRectOfInterest() {
        guard let captureSession = captureSession,
              captureSession.isRunning,
              let previewLayer = previewLayer,
              let metadataOutput = captureSession.outputs.first as? AVCaptureMetadataOutput else {
            return
        }
        
        // 获取扫描窗口在视图中的位置
        let scanWindowRect = scannerOverlayView.scanWindowRect
        
        // 将扫描窗口的位置转换为元数据输出可以理解的坐标系
        let rectOfInterest = previewLayer.metadataOutputRectConverted(fromLayerRect: scanWindowRect)
        
        // 设置扫描区域
        metadataOutput.rectOfInterest = rectOfInterest
    }
    
    // MARK: - Actions
    
    private func startScanning() {
        if let captureSession = captureSession, !captureSession.isRunning {
            DispatchQueue.global(qos: .background).async { [weak self] in
                self?.captureSession?.startRunning()
            }
            startScanAnimation()
        }
    }
    
    private func stopScanning() {
        if let captureSession = captureSession, captureSession.isRunning {
            captureSession.stopRunning()
        }
        stopScanAnimation()
    }
    
    private func startScanAnimation() {
        // 停止之前的动画
        stopScanAnimation()
        
        // 重置扫描线位置
        scanLineView.snp.updateConstraints { make in
            make.top.equalTo(scannerOverlayView.scanWindowTopConstraint)
        }
        view.layoutIfNeeded()
        
        // 创建新的定时器
        scannerAnimationTimer = Timer.scheduledTimer(withTimeInterval: 0.01, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            // 获取扫描窗口的高度
            let windowHeight = self.scannerOverlayView.scanWindowHeight
            
            // 获取当前扫描线的位置
            let currentY = self.scanLineView.frame.origin.y
            let scanWindowTop = self.scannerOverlayView.scanWindowRect.origin.y
            
            // 如果扫描线到达底部，重新开始
            if currentY >= scanWindowTop + windowHeight - 2 {
                self.scanLineView.snp.updateConstraints { make in
                    make.top.equalTo(self.scannerOverlayView.scanWindowTopConstraint)
                }
                UIView.animate(withDuration: 0.1) {
                    self.view.layoutIfNeeded()
                }
            } else {
                // 否则，向下移动
                self.scanLineView.snp.updateConstraints { make in
                    make.top.equalTo(self.scannerOverlayView.scanWindowTopConstraint).offset(currentY - scanWindowTop + 2)
                }
                UIView.animate(withDuration: 0.1) {
                    self.view.layoutIfNeeded()
                }
            }
        }
        
        // 添加到主运行循环
        RunLoop.main.add(scannerAnimationTimer!, forMode: .common)
    }
    
    private func stopScanAnimation() {
        scannerAnimationTimer?.invalidate()
        scannerAnimationTimer = nil
    }
    
    private func toggleTorch() {
        guard let device = AVCaptureDevice.default(for: .video) else { return }
        
        if device.hasTorch {
            do {
                try device.lockForConfiguration()
                
                let newTorchStatus = viewModel.toggleTorch()
                device.torchMode = newTorchStatus ? .on : .off
                
                device.unlockForConfiguration()
            } catch {
                viewModel.error = "无法使用闪光灯: \(error.localizedDescription)"
                print("Torch could not be used: \(error)")
            }
        }
    }
    
    private func toggleAutoFocus() {
        guard let device = videoCaptureDevice else { return }
        
        let isAutoFocus = viewModel.toggleAutoFocus()
        
        do {
            try device.lockForConfiguration()
            
            if isAutoFocus {
                if device.isFocusModeSupported(.continuousAutoFocus) {
                    device.focusMode = .continuousAutoFocus
                }
            } else {
                if device.isFocusModeSupported(.locked) {
                    device.focusMode = .locked
                }
            }
            
            device.unlockForConfiguration()
        } catch {
            viewModel.error = "无法切换自动对焦模式: \(error.localizedDescription)"
            print("Could not set focus mode: \(error)")
        }
    }
    
    private func focusAtPoint(_ point: CGPoint) {
        guard let device = videoCaptureDevice,
              let previewLayer = previewLayer else { return }
        
        // 将点击位置转换为设备坐标系统（0,0 - 1,1）
        let devicePoint = previewLayer.captureDevicePointConverted(fromLayerPoint: point)
        
        do {
            try device.lockForConfiguration()
            
            // 设置对焦点
            if device.isFocusPointOfInterestSupported {
                device.focusPointOfInterest = devicePoint
            }
            
            // 设置曝光点
            if device.isExposurePointOfInterestSupported {
                device.exposurePointOfInterest = devicePoint
            }
            
            // 设置对焦模式
            if device.isFocusModeSupported(.autoFocus) {
                device.focusMode = .autoFocus
            }
            
            // 设置曝光模式
            if device.isExposureModeSupported(.autoExpose) {
                device.exposureMode = .autoExpose
            }
            
            device.unlockForConfiguration()
            
            // 通知ViewModel对焦点已更改
            viewModel.setFocusPoint(point)
        } catch {
            print("无法设置对焦点: \(error)")
        }
    }
    
    private func showFocusIndicator(at point: CGPoint) {
        focusIndicatorView.center = point
        focusIndicatorView.isHidden = false
        focusIndicatorView.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        focusIndicatorView.alpha = 1.0
        
        UIView.animate(withDuration: 0.3, animations: {
            self.focusIndicatorView.transform = CGAffineTransform.identity
        }) { _ in
            UIView.animate(withDuration: 0.5, delay: 0.5, options: [], animations: {
                self.focusIndicatorView.alpha = 0
            }) { _ in
                self.focusIndicatorView.isHidden = true
            }
        }
    }
    
    private func openPhotoLibrary() {
        let imagePicker = UIImagePickerController()
        imagePicker.delegate = self
        imagePicker.sourceType = .photoLibrary
        present(imagePicker, animated: true)
    }
    
    private func showQRCodeGenerator() {
        // 弹出输入框
        let alert = UIAlertController(title: "生成二维码", message: "请输入要生成二维码的内容", preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.placeholder = "请输入文本内容"
        }
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        alert.addAction(UIAlertAction(title: "生成", style: .default) { [weak self] _ in
            guard let text = alert.textFields?.first?.text, !text.isEmpty else { return }
            
            let generatorVC = QRGeneratorViewController(content: text)
            self?.navigationController?.pushViewController(generatorVC, animated: true)
        })
        
        alert.addAction(UIAlertAction(title: "生成并保存", style: .default) { [weak self] _ in
            guard let text = alert.textFields?.first?.text, !text.isEmpty,
                  let strongSelf = self else { return }
            
            // 使用ViewModel生成并保存二维码
            let config = QRGeneratorConfig(content: text)
            if let image = strongSelf.viewModel.generateQRCode(with: config) {
                strongSelf.viewModel.saveImageToAlbum(image)
                strongSelf.showAlert(title: "保存成功", message: "二维码已保存到相册")
            } else {
                strongSelf.showAlert(title: "保存失败", message: "二维码生成失败")
            }
        })
        
        present(alert, animated: true)
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    private func handleScanResult(_ result: String) {
        // 停止扫描
        stopScanning()
        
        // 播放提示音
        AudioServicesPlaySystemSound(SystemSoundID(kSystemSoundID_Vibrate))
        
        // 显示扫描结果
        let alert = UIAlertController(title: "扫描结果", message: result, preferredStyle: .alert)
        
        alert.addAction(UIAlertAction(title: "复制", style: .default) { _ in
            UIPasteboard.general.string = result
            self.navigationController?.popViewController(animated: true)
        })
        
        alert.addAction(UIAlertAction(title: "继续扫描", style: .default) { [weak self] _ in
            self?.startScanning()
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel) { [weak self] _ in
            self?.navigationController?.popViewController(animated: true)
        })
        
        present(alert, animated: true)
    }
    
    // MARK: - Gesture Handlers
    
    @objc private func handlePinchGesture(_ gesture: UIPinchGestureRecognizer) {
        guard let device = videoCaptureDevice else { return }
        
        if gesture.state == .changed {
            // 获取设备的最大变焦因子
            let maxZoomFactor = min(device.activeFormat.videoMaxZoomFactor, 10.0)
            
            // 使用ViewModel更新变焦因子
            let newZoomFactor = viewModel.updateZoomFactorWithGesture(gesture.scale, maxZoomFactor: maxZoomFactor)
            
            // 应用到相机设备
            do {
                try device.lockForConfiguration()
                device.videoZoomFactor = newZoomFactor
                device.unlockForConfiguration()
            } catch {
                print("无法设置变焦: \(error)")
            }
            
            // 重置手势的比例
            gesture.scale = 1.0
        }
    }
    
    @objc private func handleTapGesture(_ gesture: UITapGestureRecognizer) {
        let point = gesture.location(in: view)
        
        // 检查点击位置是否在扫描容器内
        if scannerContainerView.frame.contains(point) {
            focusAtPoint(point)
        }
    }
}

// MARK: - AVCaptureMetadataOutputObjectsDelegate
extension QRScannerViewController: AVCaptureMetadataOutputObjectsDelegate {
    func metadataOutput(_ output: AVCaptureMetadataOutput, didOutput metadataObjects: [AVMetadataObject], from connection: AVCaptureConnection) {
        
        if let metadataObject = metadataObjects.first as? AVMetadataMachineReadableCodeObject,
           let stringValue = metadataObject.stringValue {
            
            // 使用ViewModel处理扫描结果
            viewModel.handleScanResult(stringValue)
        }
    }
}

// MARK: - UIImagePickerControllerDelegate
extension QRScannerViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)
        
        guard let image = info[.originalImage] as? UIImage else { return }
        
        // 使用ViewModel识别图片中的二维码
        let codes = viewModel.recognizeQRCode(from: image)
        
        if !codes.isEmpty {
            // 处理识别到的第一个二维码
            viewModel.handleScanResult(codes[0])
        } else {
            viewModel.error = "未在图片中找到二维码"
            showAlert(title: "识别失败", message: "未在图片中找到二维码")
        }
    }
}

// MARK: - Static Methods
extension QRScannerViewController {
    /// 直接生成二维码
    /// - Parameters:
    ///   - content: 二维码内容
    ///   - viewController: 当前视图控制器
    static func generateQRCode(with content: String, from viewController: UIViewController) {
        let generatorVC = QRGeneratorViewController(content: content)
        viewController.navigationController?.pushViewController(generatorVC, animated: true)
    }
    
    /// 生成二维码并保存到相册
    /// - Parameters:
    ///   - content: 二维码内容
    ///   - viewController: 当前视图控制器
    static func generateAndSaveQRCode(with content: String, from viewController: UIViewController) {
        // 创建临时ViewModel并使用其方法生成和保存二维码
        let tempViewModel = QRScannerViewModel()
        let config = QRGeneratorConfig(content: content)
        
        if let image = tempViewModel.generateQRCode(with: config) {
            tempViewModel.saveImageToAlbum(image)
            
            // 显示保存成功提示
            let alert = UIAlertController(title: "保存成功", message: "二维码已保存到相册", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            viewController.present(alert, animated: true)
        } else {
            // 显示生成失败提示
            let alert = UIAlertController(title: "保存失败", message: "二维码生成失败", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            viewController.present(alert, animated: true)
        }
    }
} 

