//
//  QRGeneratorViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/22.
//

import UIKit
import EFQRCode
import SnapKit
import Then
import Combine
import CombineCocoa

class QRGeneratorViewController: BaseViewController {
    
    // MARK: - Properties
    
    private let viewModel: QRGeneratorViewModel
    
    // MARK: - UI Components
    
    private lazy var contentView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.layer.shadowColor = UIColor.black.cgColor
        $0.layer.shadowOffset = CGSize(width: 0, height: 2)
        $0.layer.shadowOpacity = 0.1
        $0.layer.shadowRadius = 8
    }
    
    private lazy var qrImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFit
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 8
        $0.layer.masksToBounds = true
    }
    
    private lazy var titleLabel = UILabel().then {
        $0.text = "二维码"
        $0.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        $0.textAlignment = .center
    }
    
    private lazy var contentLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14)
        $0.textAlignment = .center
        $0.numberOfLines = 2
    }
    
    private lazy var saveButton = UIButton().then {
        $0.setTitle("保存到相册", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.backgroundColor = color_blue
        $0.layer.cornerRadius = 22
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
    }
    
    private lazy var shareButton = UIButton().then {
        $0.setTitle("分享", for: .normal)
        $0.setTitleColor(color_blue, for: .normal)
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 22
        $0.layer.borderWidth = 1
        $0.layer.borderColor = color_blue.cgColor
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
    }
    
    private lazy var closeButton = UIButton().then {
        $0.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
    }
    
    private lazy var navBarView = UIView().then {
        $0.backgroundColor = .white
    }
    
    private lazy var navTitleLabel = UILabel().then {
        $0.text = "生成二维码"
        $0.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        $0.textColor = color_3D3E40
        $0.textAlignment = .center
    }
    
    private lazy var backButton = UIButton().then {
        $0.setImage(UIImage(systemName: "chevron.left"), for: .normal)
        $0.tintColor = color_3D3E40
    }
    
    // MARK: - Initialization
    
    init(content: String) {
        self.viewModel = QRGeneratorViewModel(content: content)
        super.init()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = color_F6F8F9
        
        view.addSubview(navBarView)
        navBarView.addSubview(navTitleLabel)
        navBarView.addSubview(backButton)
        
        view.addSubview(contentView)
        contentView.addSubview(qrImageView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(contentLabel)
        contentView.addSubview(closeButton)
        
        view.addSubview(saveButton)
        view.addSubview(shareButton)
        
        navBarView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalNavBarHeight)
        }
        
        navTitleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-10)
        }
        
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalTo(navTitleLabel)
            make.width.height.equalTo(30)
        }
        
        contentView.snp.makeConstraints { make in
            make.top.equalTo(navBarView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(contentView.snp.width).multipliedBy(1.2)
        }
        
        closeButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.right.equalToSuperview().offset(-10)
            make.width.height.equalTo(30)
        }
        
        qrImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(40)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(contentView.snp.width).multipliedBy(0.6)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(qrImageView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(20)
        }
        
        contentLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(10)
            make.left.right.equalToSuperview().inset(20)
        }
        
        saveButton.snp.makeConstraints { make in
            make.top.equalTo(contentView.snp.bottom).offset(30)
            make.left.equalToSuperview().offset(20)
            make.right.equalTo(view.snp.centerX).offset(-10)
            make.height.equalTo(44)
        }
        
        shareButton.snp.makeConstraints { make in
            make.top.equalTo(contentView.snp.bottom).offset(30)
            make.left.equalTo(view.snp.centerX).offset(10)
            make.right.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
    }
    
   override func setupBindings() {
        // UI事件绑定
        backButton.tapPublisher
            .sink { [weak self] _ in
                self?.navigationController?.popViewController(animated: true)
            }
            .store(in: &cancellables)
        
        closeButton.tapPublisher
            .sink { [weak self] _ in
                self?.navigationController?.popViewController(animated: true)
            }
            .store(in: &cancellables)
        
        saveButton.tapPublisher
            .sink { [weak self] _ in
                self?.viewModel.saveQRCodeToAlbum()
            }
            .store(in: &cancellables)
        
        shareButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self,
                      let shareData = self.viewModel.shareQRCode() else { return }
                
                let activityViewController = UIActivityViewController(
                    activityItems: [shareData.0, shareData.1], 
                    applicationActivities: nil
                )
                self.present(activityViewController, animated: true)
            }
            .store(in: &cancellables)
            
        // ViewModel数据绑定
        viewModel.$content
            .receive(on: DispatchQueue.main)
            .sink { [weak self] content in
                self?.contentLabel.text = content
            }
            .store(in: &cancellables)
            
        viewModel.$qrImage
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] image in
                self?.qrImageView.image = image
            }
            .store(in: &cancellables)
            
        viewModel.$saveStatus
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] status in
                self?.showAlert(title: status.success ? "保存成功" : "保存失败", 
                               message: status.message)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Helper Methods
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
} 
