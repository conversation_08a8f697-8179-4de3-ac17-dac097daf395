//
//  QRScannerOverlayView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/22.
//

import UIKit
import SnapKit

class QRScannerOverlayView: UIView {
    
    // MARK: - Properties
    
    private let cornerLength: CGFloat = 20
    private let cornerWidth: CGFloat = 4
    private let scanWindowSize: CGFloat = 250
    private let scanWindowCornerRadius: CGFloat = 12
    
    // 扫描窗口位置约束
    var scanWindowTopConstraint: CGFloat {
        return (UIScreen.main.bounds.height - scanWindowSize) / 2 - 30
    }
    
    var scanWindowLeftConstraint: CGFloat {
        return (UIScreen.main.bounds.width - scanWindowSize) / 2
    }
    
    var scanWindowHeight: CGFloat {
        return scanWindowSize
    }
    
    var scanWindowRect: CGRect {
        return CGRect(
            x: scanWindowLeftConstraint,
            y: scanWindowTopConstraint,
            width: scanWindowSize,
            height: scanWindowSize
        )
    }
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .clear
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Drawing
    
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        
        guard let context = UIGraphicsGetCurrentContext() else { return }
        
        // 设置遮罩颜色（半透明黑色）
        let overlayColor = UIColor(white: 0, alpha: 0.6)
        context.setFillColor(overlayColor.cgColor)
        
        // 绘制整个视图的遮罩
        context.fill(rect)
        
        // 计算扫描窗口的位置
        let scanRect = scanWindowRect
        
        // 清除扫描窗口区域（使其透明）
        context.setBlendMode(.clear)
        
        // 创建圆角矩形路径
        let path = UIBezierPath(roundedRect: scanRect, cornerRadius: scanWindowCornerRadius)
        path.fill()
        
        // 重置混合模式
        context.setBlendMode(.normal)
        
        // 绘制四个角
        drawCorners(in: context, rect: scanRect)
    }
    
    private func drawCorners(in context: CGContext, rect: CGRect) {
        // 设置描边颜色（亮蓝色）
        let cornerColor = UIColor(red: 0.2, green: 0.71, blue: 0.898, alpha: 1.0)
        context.setStrokeColor(cornerColor.cgColor)
        context.setLineWidth(cornerWidth)
        
        // 左上角
        context.move(to: CGPoint(x: rect.minX, y: rect.minY + cornerLength))
        context.addLine(to: CGPoint(x: rect.minX, y: rect.minY))
        context.addLine(to: CGPoint(x: rect.minX + cornerLength, y: rect.minY))
        
        // 右上角
        context.move(to: CGPoint(x: rect.maxX - cornerLength, y: rect.minY))
        context.addLine(to: CGPoint(x: rect.maxX, y: rect.minY))
        context.addLine(to: CGPoint(x: rect.maxX, y: rect.minY + cornerLength))
        
        // 右下角
        context.move(to: CGPoint(x: rect.maxX, y: rect.maxY - cornerLength))
        context.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        context.addLine(to: CGPoint(x: rect.maxX - cornerLength, y: rect.maxY))
        
        // 左下角
        context.move(to: CGPoint(x: rect.minX + cornerLength, y: rect.maxY))
        context.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        context.addLine(to: CGPoint(x: rect.minX, y: rect.maxY - cornerLength))
        
        context.strokePath()
    }
} 