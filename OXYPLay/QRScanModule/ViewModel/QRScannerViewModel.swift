//
//  QRScannerViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/22.
//

import Foundation
import UIKit
import AVFoundation
import EFQRCode
import Combine

class QRScannerViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    @Published var scanResult: QRScanResult?
    @Published var error: String?
    @Published var zoomFactor: CGFloat = 1.0
    @Published var isTorchEnabled: Bool = false
    @Published var isAutoZoomEnabled: Bool = true
    @Published var focusPoint: CGPoint?
    @Published var shouldAutoFocus: Bool = true
    
    // 相机配置
    private var cameraConfig = QRScannerCameraConfig()
    
    // MARK: - Public Methods
    
    // 处理扫描结果
    func handleScanResult(_ content: String, type: String = "QR") {
        let result = QRScanResult(content: content, type: type)
        scanResult = result
    }
    
    // 设置变焦因子
    func setZoomFactor(_ factor: CGFloat, maxZoomFactor: CGFloat) -> CGFloat {
        // 确保变焦因子在有效范围内
        let minZoom: CGFloat = 1.0
        let clampedFactor = min(max(factor, minZoom), maxZoomFactor)
        
        cameraConfig.currentZoomFactor = clampedFactor
        zoomFactor = clampedFactor
        
        return clampedFactor
    }
    
    // 根据手势更新变焦因子
    func updateZoomFactorWithGesture(_ scale: CGFloat, maxZoomFactor: CGFloat) -> CGFloat {
        let newFactor = cameraConfig.currentZoomFactor * scale
        return setZoomFactor(newFactor, maxZoomFactor: maxZoomFactor)
    }
    
    // 切换手电筒状态
    func toggleTorch() -> Bool {
        isTorchEnabled.toggle()
        cameraConfig.isTorchEnabled = isTorchEnabled
        return isTorchEnabled
    }
    
    // 切换自动变焦状态
    func toggleAutoZoom() -> Bool {
        isAutoZoomEnabled.toggle()
        cameraConfig.isAutoZoomEnabled = isAutoZoomEnabled
        return isAutoZoomEnabled
    }
    
    // 切换自动对焦状态
    func toggleAutoFocus() -> Bool {
        shouldAutoFocus.toggle()
        return shouldAutoFocus
    }
    
    // 设置对焦点
    func setFocusPoint(_ point: CGPoint) {
        focusPoint = point
        cameraConfig.focusPoint = point
    }
    
    // 获取元数据对象类型
    func getMetadataObjectTypes() -> [AVMetadataObject.ObjectType] {
        return cameraConfig.metadataObjectTypes
    }
    
    // 计算智能变焦因子
    func calculateIntelligentZoomFactor(for barcodeRect: CGRect, in viewSize: CGSize, currentZoomFactor: CGFloat, maxZoomFactor: CGFloat) -> CGFloat? {
        // 计算二维码占据视图的比例
        let barcodeSize = max(barcodeRect.width, barcodeRect.height)
        let viewDimension = min(viewSize.width, viewSize.height)
        let sizeRatio = barcodeSize / viewDimension
        
        // 根据二维码大小调整变焦
        if sizeRatio < 0.3 && currentZoomFactor < maxZoomFactor {
            // 逐步放大，避免突然变化
            return min(currentZoomFactor * 1.2, maxZoomFactor)
        } else if sizeRatio > 0.7 && currentZoomFactor > 1.0 {
            // 逐步缩小
            return max(currentZoomFactor * 0.8, 1.0)
        }
        
        return nil
    }
    
    // 生成二维码图片
    func generateQRCode(with config: QRGeneratorConfig) -> UIImage? {
        guard let cgImage = config.iconImage?.cgImage else { return nil }
        
        let generator = try? EFQRCode.Generator(config.content, style: .image(
            params: .init(image: .init(image: .static(image: cgImage), allowTransparent: true)))
        )
        
        if let cgImage = try? generator?.toImage(width: CGFloat(config.size.width)).cgImage {
            return UIImage(cgImage: cgImage)
        }
        
        return nil
    }
    
    // 保存图片到相册
    func saveImageToAlbum(_ image: UIImage) {
        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
    }
    
    // 从图片中识别二维码
    func recognizeQRCode(from image: UIImage) -> [String] {
        guard let cgImage = image.cgImage else { return [] }
        
        let codes = EFQRCode.Recognizer(image: cgImage).recognize()
        return codes
    }
} 
