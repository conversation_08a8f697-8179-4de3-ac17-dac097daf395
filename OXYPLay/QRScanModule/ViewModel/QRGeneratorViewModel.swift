//
//  QRGeneratorViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/22.
//

import Foundation
import UIKit
import EFQRCode
import Combine

class QRGeneratorViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    @Published var content: String
    @Published var qrImage: UIImage?
    @Published var saveStatus: (success: Bool, message: String)?
    
    // 配置
    private var generatorConfig: QRGeneratorConfig
    
    // MARK: - Initialization
    
    init(content: String) {
        self.content = content
        self.generatorConfig = QRGeneratorConfig(content: content)
        super.init()
        
        generateQRCode()
    }
    
    // MARK: - Public Methods
    
    // 生成二维码
    func generateQRCode() {
        generatorConfig = QRGeneratorConfig(content: content)
        
        guard let cgimage = generatorConfig.iconImage?.cgImage else {
            qrImage = nil
            return
        }
        
        let generator = try? EFQRCode.Generator(content, style: .image(
            params: .init(image: .init(image: .static(image: cgimage), allowTransparent: true)))
        )
        
        if let cgImage = try? generator?.toImage(width: CGFloat(generatorConfig.size.width)).cgImage {
            qrImage = UIImage(cgImage: cgImage)
        } else {
            qrImage = nil
        }
    }
    
    // 保存二维码到相册
    func saveQRCodeToAlbum() {
        guard let qrImage = qrImage else {
            saveStatus = (false, "二维码生成失败")
            return
        }
        
        UIImageWriteToSavedPhotosAlbum(qrImage, self, #selector(image(_:didFinishSavingWithError:contextInfo:)), nil)
    }
    
    // 分享二维码
    func shareQRCode() -> (UIImage, String)? {
        guard let qrImage = qrImage else {
            return nil
        }
        
        return (qrImage, content)
    }
    
    // 更新二维码内容
    func updateContent(_ newContent: String) {
        content = newContent
        generateQRCode()
    }
    
    // MARK: - Private Methods
    
    @objc private func image(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if let error = error {
            saveStatus = (false, error.localizedDescription)
        } else {
            saveStatus = (true, "二维码已保存到相册")
        }
    }
} 
