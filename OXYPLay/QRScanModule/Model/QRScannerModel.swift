//
//  QRScannerModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/22.
//

import Foundation
import UIKit
import AVFoundation

// 扫描结果模型
struct QRScanResult {
    let content: String
    let type: String
    let timestamp: Date
    
    init(content: String, type: String = "QR") {
        self.content = content
        self.type = type
        self.timestamp = Date()
    }
}

// 相机配置模型
struct QRScannerCameraConfig {
    var isAutoZoomEnabled: Bool = true
    var currentZoomFactor: CGFloat = 1.0
    var isTorchEnabled: Bool = false
    var isAutoFocusEnabled: Bool = true
    var focusPoint: CGPoint?
    var metadataObjectTypes: [AVMetadataObject.ObjectType] = [
        .qr,
        .ean13,
        .ean8,
        .code128,
        .code39,
        .code93
    ]
}

// 二维码生成配置模型
struct QRGeneratorConfig {
    let content: String
    let size: CGSize
    let iconImage: UIImage?
    
    init(content: String, size: CGSize = CGSize(width: 300, height: 300), iconImage: UIImage? = UIImage(named: "qrIcon")) {
        self.content = content
        self.size = size
        self.iconImage = iconImage
    }
} 