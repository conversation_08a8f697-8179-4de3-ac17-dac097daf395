import UIKit
import Alamofire

// MARK: - 微信API数据结构

/// 微信Access Token响应模型
struct WechatAccessTokenResponse: Codable {
    let accessToken: String
    let expiresIn: Int
    let refreshToken: String
    let openid: String
    let scope: String
    let unionid: String?

    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case expiresIn = "expires_in"
        case refreshToken = "refresh_token"
        case openid
        case scope
        case unionid
    }
}

/// 微信用户信息响应模型
struct WechatUserInfoResponse: Codable {
    let openid: String
    let nickname: String
    let sex: Int
    let province: String
    let city: String
    let country: String
    let headimgurl: String
    let privilege: [String]
    let unionid: String?
}

/// 微信支付数据结构
struct WechatPaymentData {
    let partnerId: String
    let prepayId: String
    let nonceStr: String
    let timeStamp: String
    let package: String
    let sign: String
}

class WechatManager: NSObject {
    static let shared = WechatManager()

    // 微信AppID和AppSecret
    private let wxAppID = "wx60d36f0847db422c"
    private let wxAppSecret = "5758b4e5dedabcd34d5bfe7a1a65f31d"
    private let wxUniversalLink = "https://your-domain.com/app/"  // 需要替换为您的Universal Link

    // 微信API地址
    private let accessTokenURL = "https://api.weixin.qq.com/sns/oauth2/access_token"
    private let userInfoURL = "https://api.weixin.qq.com/sns/userinfo"

    // 微信登录和分享回调
    private var wechatLoginCompletion: ((BaseResp) -> Void)?
    private var wechatShareCompletion: ((BaseResp) -> Void)?
    private var wechatPaymentCompletion: ((BaseResp) -> Void)?

    private override init() {
        super.init()
    }
    // 初始化微信SDK
    func setupWechatSDK() {
        // 注册微信SDK
        WXApi.registerApp(wxAppID, universalLink: wxUniversalLink)

    }

    // MARK: - 微信登录

    /// 微信登录
    /// - Parameter completion: 登录结果回调，返回用户信息和错误信息
    func login(completion: @escaping (_ userInfo: [String: Any]?, _ error: Error?) -> Void) {
        // 检查微信是否安装
        guard WXApi.isWXAppInstalled() else {
            completion(nil, NSError(domain: "com.oxyplay.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "未安装微信客户端"]))
            return
        }

        // 保存回调
        wechatLoginCompletion = { [weak self] resp in
            if resp.errCode == WXSuccess.rawValue {
                // 登录成功，获取授权码
                if let authResp = resp as? SendAuthResp,
                   let code = authResp.code {
                    // 使用授权码获取access_token和用户信息
                    self?.getAccessToken(code: code, completion: completion)
                } else {
                    completion(nil, NSError(domain: "com.oxyplay.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取授权码失败"]))
                }
            } else {
                // 登录失败
                let errorMsg = resp.errStr ?? "微信登录失败"
                completion(nil, NSError(domain: "com.oxyplay.error", code: Int(resp.errCode), userInfo: [NSLocalizedDescriptionKey: errorMsg]))
            }
        }

        // 构建登录请求
        let req = SendAuthReq()
        req.scope = "snsapi_userinfo"
        req.state = "wechat_login_\(Int(Date().timeIntervalSince1970))"

        // 发送登录请求
        WXApi.send(req, completion: { success in
            if !success {
                completion(nil, NSError(domain: "com.oxyplay.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "微信登录请求发送失败"]))
            }
        })
    }

    /// 通过授权码获取access_token
    /// - Parameters:
    ///   - code: 微信授权码
    ///   - completion: 完成回调，返回用户信息
    private func getAccessToken(code: String, completion: @escaping (_ userInfo: [String: Any]?, _ error: Error?) -> Void) {
        let parameters: [String: Any] = [
            "appid": wxAppID,
            "secret": wxAppSecret,
            "code": code,
            "grant_type": "authorization_code"
        ]

        print("开始获取微信access_token，code: \(code)")

        AF.request(accessTokenURL, method: .get, parameters: parameters)
            .validate()
            .responseDecodable(of: WechatAccessTokenResponse.self) { [weak self] response in
                switch response.result {
                case .success(let tokenResponse):
                    print("获取access_token成功: \(tokenResponse)")

                    // 无论是否有unionid，都获取完整的用户信息
                    self?.getUserInfo(accessToken: tokenResponse.accessToken, openid: tokenResponse.openid, tokenResponse: tokenResponse, completion: completion)

                case .failure(let error):
                    print("获取access_token失败: \(error)")
                    completion(nil, NSError(domain: "com.oxyplay.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取微信access_token失败: \(error.localizedDescription)"]))
                }
            }
    }

    /// 通过access_token获取用户信息
    /// - Parameters:
    ///   - accessToken: 访问令牌
    ///   - openid: 用户openid
    ///   - tokenResponse: token响应信息
    ///   - completion: 完成回调，返回用户信息
    private func getUserInfo(accessToken: String, openid: String, tokenResponse: WechatAccessTokenResponse, completion: @escaping (_ userInfo: [String: Any]?, _ error: Error?) -> Void) {
        let parameters: [String: Any] = [
            "access_token": accessToken,
            "openid": openid,
            "lang": "zh_CN"
        ]

        print("开始获取微信用户信息，openid: \(openid)")

        AF.request(userInfoURL, method: .get, parameters: parameters)
            .validate()
            .responseDecodable(of: WechatUserInfoResponse.self) { response in
                switch response.result {
                case .success(let userInfo):
                    print("获取用户信息成功: \(userInfo)")

                    // 构建完整的用户信息字典
                    var userInfoDict: [String: Any] = [:]

                    // 基本信息
                    userInfoDict["openid"] = userInfo.openid
                    userInfoDict["nickname"] = userInfo.nickname
                    userInfoDict["sex"] = userInfo.sex
                    userInfoDict["province"] = userInfo.province
                    userInfoDict["city"] = userInfo.city
                    userInfoDict["country"] = userInfo.country
                    userInfoDict["headimgurl"] = userInfo.headimgurl
                    userInfoDict["privilege"] = userInfo.privilege

                    // unionid优先使用用户信息中的，如果没有则使用token响应中的
                    if let unionid = userInfo.unionid, !unionid.isEmpty {
                        userInfoDict["unionid"] = unionid
                    } else if let unionid = tokenResponse.unionid, !unionid.isEmpty {
                        userInfoDict["unionid"] = unionid
                    } else {
                        // 如果都没有unionid，使用openid作为唯一标识
                        userInfoDict["unionid"] = userInfo.openid
                    }

                    // token信息
                    userInfoDict["access_token"] = tokenResponse.accessToken
                    userInfoDict["refresh_token"] = tokenResponse.refreshToken
                    userInfoDict["expires_in"] = tokenResponse.expiresIn
                    userInfoDict["scope"] = tokenResponse.scope

                    completion(userInfoDict, nil)

                case .failure(let error):
                    print("获取用户信息失败: \(error)")
                    completion(nil, NSError(domain: "com.oxyplay.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取微信用户信息失败: \(error.localizedDescription)"]))
                }
            }
    }

    // MARK: - 微信分享

    /// 分享文本到微信
    /// - Parameters:
    ///   - text: 分享的文本内容
    ///   - scene: 分享场景（0=会话，1=朋友圈，2=收藏）
    ///   - completion: 分享结果回调
    func shareText(text: String, scene: Int = 0, completion: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        wechatShareCompletion = { resp in
            if resp.errCode == WXSuccess.rawValue {
                completion(true, nil)
            } else {
                let errorMsg = resp.errStr ?? "分享失败"
                completion(false, NSError(domain: "com.oxyplay.error", code: Int(resp.errCode), userInfo: [NSLocalizedDescriptionKey: errorMsg]))
            }
        }

        let message = WXMediaMessage()
        message.title = text
        message.description = text

        let textObject = WXTextObject()
        textObject.contentText = text
        message.mediaObject = textObject

        let req = SendMessageToWXReq()
        req.bText = true
        req.message = message
        req.scene = Int32(scene)

        WXApi.send(req, completion: { success in
            if !success {
                completion(false, NSError(domain: "com.oxyplay.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "分享请求发送失败"]))
            }
        })
    }

    /// 分享网页链接到微信
    /// - Parameters:
    ///   - title: 分享标题
    ///   - description: 分享描述
    ///   - thumbImage: 缩略图
    ///   - webpageUrl: 网页链接
    ///   - scene: 分享场景（0=会话，1=朋友圈，2=收藏）
    ///   - completion: 分享结果回调
    func shareWebPage(title: String, description: String, thumbImage: UIImage?, webpageUrl: String, scene: Int = 0, completion: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        wechatShareCompletion = { resp in
            if resp.errCode == WXSuccess.rawValue {
                completion(true, nil)
            } else {
                let errorMsg = resp.errStr ?? "分享失败"
                completion(false, NSError(domain: "com.oxyplay.error", code: Int(resp.errCode), userInfo: [NSLocalizedDescriptionKey: errorMsg]))
            }
        }

        let message = WXMediaMessage()
        message.title = title
        message.description = description

        // 设置缩略图
        if let thumbImage = thumbImage,
           let thumbData = thumbImage.jpegData(compressionQuality: 0.8) {
            message.thumbData = thumbData
        }

        let webObject = WXWebpageObject()
        webObject.webpageUrl = webpageUrl
        message.mediaObject = webObject

        let req = SendMessageToWXReq()
        req.bText = false
        req.message = message
        req.scene = Int32(scene)

        WXApi.send(req, completion: { success in
            if !success {
                completion(false, NSError(domain: "com.oxyplay.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "分享请求发送失败"]))
            }
        })
    }

    /// 分享图片到微信
    /// - Parameters:
    ///   - image: 图片
    ///   - thumbImage: 缩略图
    ///   - scene: 分享场景（0=会话，1=朋友圈，2=收藏）
    ///   - completion: 分享结果回调
    func shareImage(image: UIImage, thumbImage: UIImage? = nil, scene: Int = 0, completion: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        wechatShareCompletion = { resp in
            if resp.errCode == WXSuccess.rawValue {
                completion(true, nil)
            } else {
                let errorMsg = resp.errStr ?? "分享失败"
                completion(false, NSError(domain: "com.oxyplay.error", code: Int(resp.errCode), userInfo: [NSLocalizedDescriptionKey: errorMsg]))
            }
        }

        let message = WXMediaMessage()
        message.title = "图片分享"
        message.description = "来自OXYPlay的图片分享"

        // 设置缩略图
        let thumb = thumbImage ?? image.thumbnail(maxSize: 150)
        if let thumbData = thumb?.jpegData(compressionQuality: 0.8) {
            message.thumbData = thumbData
        }

        let imageObject = WXImageObject()
        if let imageData = image.jpegData(compressionQuality: 0.9) {
            imageObject.imageData = imageData
        }
        message.mediaObject = imageObject

        let req = SendMessageToWXReq()
        req.bText = false
        req.message = message
        req.scene = Int32(scene)

        WXApi.send(req, completion: { success in
            if !success {
                completion(false, NSError(domain: "com.oxyplay.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "分享请求发送失败"]))
            }
        })
    }

    // MARK: - 微信支付

    /// 微信支付
    /// - Parameters:
    ///   - payParams: 微信支付参数JSON字符串
    ///   - completion: 支付结果回调
    func wechatPay(payParams: String, completion: @escaping (_ success: Bool, _ tradeNo: String?, _ error: Error?) -> Void) {
        // 检查微信是否安装
        guard WXApi.isWXAppInstalled() else {
            completion(false, nil, NSError(domain: "com.oxyplay.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "未安装微信客户端"]))
            return
        }

        // 解析微信支付参数
        guard let paymentData = parseWechatPayParams(payParams) else {
            completion(false, nil, NSError(domain: "com.oxyplay.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "微信支付参数解析失败"]))
            return
        }

        wechatPaymentCompletion = { resp in
            if let payResp = resp as? PayResp {
                switch payResp.errCode {
                case WXSuccess.rawValue: // 支付成功
                    let tradeNo = payResp.returnKey ?? "wechat_\(Int(Date().timeIntervalSince1970))"
                    completion(true, tradeNo, nil)
                case WXErrCodeUserCancel.rawValue: // 用户取消
                    completion(false, nil, NSError(domain: "com.oxyplay.error", code: Int(payResp.errCode), userInfo: [NSLocalizedDescriptionKey: "用户取消支付"]))
                default: // 其他错误
                    let errorMsg = payResp.errStr ?? "微信支付失败"
                    completion(false, nil, NSError(domain: "com.oxyplay.error", code: Int(payResp.errCode), userInfo: [NSLocalizedDescriptionKey: errorMsg]))
                }
            }
        }

        // 构建微信支付请求
        let payReq = PayReq()
        payReq.partnerId = paymentData.partnerId
        payReq.prepayId = paymentData.prepayId
        payReq.nonceStr = paymentData.nonceStr
        payReq.timeStamp = UInt32(paymentData.timeStamp) ?? 0
        payReq.package = paymentData.package
        payReq.sign = paymentData.sign

        // 调用微信支付
        WXApi.send(payReq, completion: { success in
            if !success {
                completion(false, nil, NSError(domain: "com.oxyplay.error", code: -1, userInfo: [NSLocalizedDescriptionKey: "微信支付调用失败"]))
            }
        })
    }

    /// 解析微信支付参数
    /// - Parameter payParams: 微信支付参数JSON字符串
    /// - Returns: 解析后的支付参数
    private func parseWechatPayParams(_ payParams: String) -> WechatPaymentData? {
        guard let data = payParams.data(using: .utf8) else {
            print("微信支付参数转换Data失败")
            return nil
        }

        do {
            let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]
            guard let json = json else {
                print("微信支付参数JSON解析失败")
                return nil
            }

            return WechatPaymentData(
                partnerId: json["partnerid"] as? String ?? "",
                prepayId: json["prepayid"] as? String ?? "",
                nonceStr: json["noncestr"] as? String ?? "",
                timeStamp: json["timestamp"] as? String ?? "",
                package: json["package"] as? String ?? "",
                sign: json["sign"] as? String ?? ""
            )
        } catch {
            print("微信支付参数解析异常: \(error)")
            return nil
        }
    }

    /// 检查微信是否安装
    /// - Returns: 是否安装微信
    func isWechatInstalled() -> Bool {
        return WXApi.isWXAppInstalled()
    }

    /// 检查微信版本是否支持OpenAPI
    /// - Returns: 是否支持
    func isWechatSupportApi() -> Bool {
        return WXApi.isWXAppSupport()
    }
}

// MARK: - WXApiDelegate
extension WechatManager: WXApiDelegate {

    func onReq(_ req: BaseReq) {
        print("微信请求: \(req)")
    }

    func onResp(_ resp: BaseResp) {
        print("微信响应: \(resp), errCode: \(resp.errCode), errStr: \(resp.errStr ?? "无")")

        if resp.isKind(of: SendAuthResp.self) {
            // 登录响应
            wechatLoginCompletion?(resp)
            wechatLoginCompletion = nil
        } else if resp.isKind(of: SendMessageToWXResp.self) {
            // 分享响应
            wechatShareCompletion?(resp)
            wechatShareCompletion = nil
        } else if resp.isKind(of: PayResp.self) {
            // 支付响应
            wechatPaymentCompletion?(resp)
            wechatPaymentCompletion = nil
        }
    }
}
