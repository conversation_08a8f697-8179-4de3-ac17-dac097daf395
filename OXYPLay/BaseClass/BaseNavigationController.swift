//
//  BaseNavigationController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/28.
//

import UIKit

class BaseNavigationController: UINavigationController {
    
    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupNavigationBarAppearance() // 全局导航栏样式
        interactivePopGestureRecognizer?.delegate = self // 启用全屏侧滑返回
    }
    
    // MARK: - 核心功能
    // 1. 隐藏TabBar（非根控制器时）
    override func pushViewController(_ viewController: UIViewController, animated: Bool) {
        if viewControllers.count > 0 {
            viewController.hidesBottomBarWhenPushed = true
            addCustomBackButton(for: viewController) // 添加自定义返回按钮
        }
        super.pushViewController(viewController, animated: animated)
    }
    
    // 2. 全局导航栏样式配置
    private func setupNavigationBarAppearance() {
        // 统一背景样式
        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = .white
        appearance.shadowColor = .clear
        
        // 统一标题样式
        appearance.titleTextAttributes = [
            .font: UIFont.systemFont(ofSize: 16, weight: .medium),
            .foregroundColor: UIColor.label
        ]
        
        // 应用配置
        navigationBar.standardAppearance = appearance
        navigationBar.scrollEdgeAppearance = appearance
        navigationBar.tintColor = .black // 统一按钮颜色
    }
    
    // 3. 自定义返回按钮
    private func addCustomBackButton(for vc: UIViewController) {
        let backButton = UIButton(type: .system)
        backButton.setImage(UIImage(systemName: "chevron.backward"), for: .normal)
        backButton.addTarget(self, action: #selector(handleBackAction), for: .touchUpInside)
        
        vc.navigationItem.leftBarButtonItem = UIBarButtonItem(customView: backButton)
    }
    
    @objc private func handleBackAction() {
        popViewController(animated: true)
    }
    
    // 4. 状态栏样式控制
    override var childForStatusBarStyle: UIViewController? {
        return topViewController // 由当前控制器决定状态栏样式
    }
}

// MARK: - 侧滑手势代理 (实现全屏返回)
extension BaseNavigationController: UIGestureRecognizerDelegate {
    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        return viewControllers.count > 1 // 根控制器禁用侧滑
    }
}
