//
//  BaseViewModel+Refresh.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/21.
//

import Foundation
import Combine
import UIKit

// MARK: - BaseViewModel刷新扩展

extension BaseViewModel {
    
    /// 绑定刷新状态到ScrollView
    /// - Parameter scrollView: 需要绑定的ScrollView
    /// - Returns: 订阅对象，需要存储到cancellables中
    func bindRefreshState(to scrollView: UIScrollView) -> AnyCancellable {
        return $refreshState
            .receive(on: DispatchQueue.main)
            .sink { [weak self, weak scrollView] state in
                guard let scrollView = scrollView, let self = self else { return }

                switch state {
                case .idle:
                    break

                case .headerRefreshing:
                    // 下拉刷新中，不需要手动处理，MJRefresh会自动显示
                    break

                case .footerLoading:
                    // 上拉加载中，不需要手动处理，MJRefresh会自动显示
                    break

                case .refreshSuccess:
                    // 刷新成功，结束头部刷新
                    scrollView.mj_header?.endRefreshing()

                case .loadMoreSuccess:
                    // 加载更多成功，结束底部刷新，还有更多数据
                    scrollView.mj_footer?.endRefreshing()

                case .refreshFailure(let message):
                    // 刷新失败，结束头部刷新并显示错误
                    scrollView.mj_header?.endRefreshing()
                    print("刷新失败: \(message)")

                case .loadMoreFailure(let message):
                    // 加载更多失败，结束底部刷新
                    scrollView.mj_footer?.endRefreshing()
                    print("加载更多失败: \(message)")

                case .noMoreData:
                    // 没有更多数据，结束底部刷新并显示无更多数据状态
                    scrollView.mj_footer?.endRefreshingWithNoMoreData()
                }
            }
    }
    
    /// 为ScrollView设置刷新控件并绑定ViewModel
    /// - Parameters:
    ///   - scrollView: 需要设置刷新的ScrollView
    ///   - enableHeader: 是否启用下拉刷新，默认true
    ///   - enableFooter: 是否启用上拉加载，默认true
    ///   - headerConfig: 头部刷新配置
    ///   - footerConfig: 底部刷新配置
    /// - Returns: 订阅对象，需要存储到cancellables中
    func setupRefresh(
        for scrollView: UIScrollView,
        enableHeader: Bool = true,
        enableFooter: Bool = true,
        headerConfig: GIFRefreshConfig? = nil,
        footerConfig: GIFRefreshConfig? = nil
    ) -> AnyCancellable {
        
        // 设置刷新控件
        if enableHeader {
            let header = GIFRefreshHeader.create(withHandler: { [weak self] in
                self?.refreshData()
            }, config: headerConfig ?? RefreshImageHelper.createConfig(withStyle: .modern))
            scrollView.mj_header = header
        }

        if enableFooter {
            let footer = GIFRefreshFooter.create(withHandler: { [weak self] in
                self?.loadMoreData()
            }, config: footerConfig ?? RefreshImageHelper.createConfig(withStyle: .modern))
            scrollView.mj_footer = footer
        }
        
        // 绑定刷新状态
        return bindRefreshState(to: scrollView)
    }
}



// MARK: - 刷新状态便捷属性

extension RefreshState {
    
    /// 是否正在刷新中
    var isRefreshing: Bool {
        switch self {
        case .headerRefreshing, .footerLoading:
            return true
        default:
            return false
        }
    }
    
    /// 是否为成功状态
    var isSuccess: Bool {
        switch self {
        case .refreshSuccess, .loadMoreSuccess:
            return true
        default:
            return false
        }
    }
    
    /// 是否为失败状态
    var isFailure: Bool {
        switch self {
        case .refreshFailure, .loadMoreFailure:
            return true
        default:
            return false
        }
    }
    
    /// 获取错误信息
    var errorMessage: String? {
        switch self {
        case .refreshFailure(let message), .loadMoreFailure(let message):
            return message
        default:
            return nil
        }
    }
}
