//
//  KeyboardManager.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/30.
//

import UIKit
import Combine

/// 键盘管理器 - 自动处理键盘遮挡问题
class KeyboardManager: NSObject {

    // MARK: - 单例
    static let shared = KeyboardManager()

    // MARK: - 属性

    /// 是否启用键盘管理
    var isEnabled: Bool = true

    /// 键盘与输入框的最小距离
    var keyboardDistance: CGFloat = 15

    /// 是否允许点击空白区域收起键盘
    var resignOnTouchOutside: Bool = true

    /// 是否启用自动工具栏
    var enableAutoToolbar: Bool = true

    // MARK: - 私有属性

    /// 当前活跃的文本输入视图
    private weak var activeTextInputView: UIView?

    /// 当前键盘高度
    private var keyboardHeight: CGFloat = 0

    /// 是否是第一次弹出键盘
    private var isFirstKeyboardShow: Bool = true

    /// 原始状态记录
    private var originalContentOffset: CGPoint = .zero
    private var originalContentInset: UIEdgeInsets = .zero
    private var originalScrollIndicatorInsets: UIEdgeInsets = .zero

    /// 订阅集合
    private var cancellables = Set<AnyCancellable>()

    /// 当前处理的ScrollView
    private weak var currentScrollView: UIScrollView?

    /// 当前处理的ViewController
    private weak var currentViewController: UIViewController?

    /// 是否已经调整过视图
    private var hasAdjustedView: Bool = false

    /// 当前调整的动画是否正在进行
    private var isAnimating: Bool = false

    // MARK: - 初始化

    private override init() {
        super.init()
        setupNotifications()
    }

    deinit {
        stopObserving()
    }

    // MARK: - 公共方法

    /// 手动收起键盘
    func resignFirstResponder() {
        activeTextInputView?.resignFirstResponder()
    }

    /// 强制恢复视图到原始状态
    func forceRestoreViews() {
        print("KeyboardManager: Force restoring views - hasAdjustedView: \(hasAdjustedView)")

        // 立即恢复，不使用动画
        if let scrollView = currentScrollView {
            // 恢复到真正的原始状态
            scrollView.contentInset = originalContentInset
            scrollView.scrollIndicatorInsets = originalScrollIndicatorInsets
            scrollView.contentOffset = originalContentOffset

            print("KeyboardManager: Force restored ScrollView - offset: \(scrollView.contentOffset), inset: \(scrollView.contentInset)")
        } else if let viewController = currentViewController {
            // 恢复ViewController的transform到identity
            viewController.view.transform = .identity
            print("KeyboardManager: Force restored ViewController transform to identity")
        }

        // 清理状态
        cleanupState()
    }

    /// 检查是否有视图需要恢复
    func hasViewsToRestore() -> Bool {
        return hasAdjustedView && (currentScrollView != nil || currentViewController != nil)
    }

    // MARK: - 私有方法

    /// 停止监听键盘事件
    private func stopObserving() {
        cancellables.removeAll()
    }

    /// 设置通知监听
    private func setupNotifications() {
        // 清除之前的订阅
        cancellables.removeAll()

        // 键盘将要显示
        NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)
            .sink { [weak self] notification in
                self?.keyboardWillShow(notification)
            }
            .store(in: &cancellables)

        // 键盘将要隐藏
        NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)
            .sink { [weak self] notification in
                self?.keyboardWillHide(notification)
            }
            .store(in: &cancellables)

        // 文本框开始编辑
        NotificationCenter.default.publisher(for: UITextField.textDidBeginEditingNotification)
            .sink { [weak self] notification in
                self?.textInputDidBeginEditing(notification)
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: UITextView.textDidBeginEditingNotification)
            .sink { [weak self] notification in
                self?.textInputDidBeginEditing(notification)
            }
            .store(in: &cancellables)

        // 文本框结束编辑
        NotificationCenter.default.publisher(for: UITextField.textDidEndEditingNotification)
            .sink { [weak self] notification in
                self?.textInputDidEndEditing(notification)
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: UITextView.textDidEndEditingNotification)
            .sink { [weak self] notification in
                self?.textInputDidEndEditing(notification)
            }
            .store(in: &cancellables)

        // 应用生命周期通知
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                self?.isFirstKeyboardShow = true
                // 应用进入后台时强制恢复视图
                self?.forceRestoreViews()
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                self?.isFirstKeyboardShow = true
                // 应用回到前台时重置状态
                self?.cleanupState()
            }
            .store(in: &cancellables)

        // 监听应用即将失去焦点
        NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)
            .sink { [weak self] _ in
                // 应用失去焦点时强制恢复视图
                if self?.hasViewsToRestore() == true {
                    self?.forceRestoreViews()
                }
            }
            .store(in: &cancellables)
    }

    /// 键盘将要显示
    private func keyboardWillShow(_ notification: Notification) {
        guard isEnabled,
              let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect,
              let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double,
              let curve = notification.userInfo?[UIResponder.keyboardAnimationCurveUserInfoKey] as? UInt else {
            return
        }

        keyboardHeight = keyboardFrame.height

        guard let activeView = activeTextInputView else { return }

        // 查找容器
        findContainers(for: activeView)

        // 第一次弹出键盘需要延迟处理，确保布局完成
        let delay: TimeInterval = isFirstKeyboardShow ? 0.1 : 0.0
        if isFirstKeyboardShow {
            isFirstKeyboardShow = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + delay) { [weak self] in
            self?.adjustForKeyboard(duration: duration, curve: curve)
        }
    }

    /// 键盘将要隐藏
    private func keyboardWillHide(_ notification: Notification) {
        guard isEnabled else { return }

        let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double ?? 0.25
        let curve = notification.userInfo?[UIResponder.keyboardAnimationCurveUserInfoKey] as? UInt ?? UInt(UIView.AnimationCurve.easeInOut.rawValue)

        print("KeyboardManager: Keyboard will hide - duration: \(duration), hasAdjustedView: \(hasAdjustedView)")

        keyboardHeight = 0

        // 确保恢复视图，即使没有获取到动画参数
        restoreViews(duration: duration, curve: curve)
    }

    /// 文本输入开始编辑
    private func textInputDidBeginEditing(_ notification: Notification) {
        guard isEnabled, let textInput = notification.object as? UIView else { return }

        // 如果是同一个输入框，不需要重新处理
        if activeTextInputView == textInput {
            return
        }

        // 如果之前有活跃的输入框，先清理状态
        if activeTextInputView != nil && activeTextInputView != textInput {
            cleanupPreviousState()
        }

        activeTextInputView = textInput

        // 添加工具栏
        if enableAutoToolbar {
            addToolbar(to: textInput)
        }

        // 添加点击手势
        if resignOnTouchOutside {
            addTapGesture()
        }

        print("KeyboardManager: Text input began editing - \(type(of: textInput))")
    }

    /// 文本输入结束编辑
    private func textInputDidEndEditing(_ notification: Notification) {
        guard isEnabled else { return }

        removeTapGesture()

        if activeTextInputView == notification.object as? UIView {
            activeTextInputView = nil
            print("KeyboardManager: Text input ended editing")

            // 延迟检查是否需要恢复视图（防止键盘隐藏通知丢失）
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                guard let self = self else { return }

                // 只有在键盘高度为0、视图还没有恢复、且没有正在动画时才强制恢复
                if self.keyboardHeight == 0 && self.hasViewsToRestore() && !self.isAnimating {
                    print("KeyboardManager: Keyboard height is 0 but views not restored, forcing restore")
                    self.forceRestoreViews()
                }
            }
        }
    }

    // MARK: - 核心调整方法

    /// 查找容器
    private func findContainers(for textInputView: UIView) {
        // 只有在没有当前容器或容器发生变化时才重新查找
        let needsNewSearch = currentScrollView == nil || currentViewController == nil

        if needsNewSearch {
            // 重置状态
            currentScrollView = nil
            currentViewController = nil
        }

        // 查找ViewController
        var responder: UIResponder? = textInputView
        while responder != nil {
            if let viewController = responder as? UIViewController {
                // 只有在ViewController发生变化时才更新
                if currentViewController != viewController {
                    currentViewController = viewController
                    // 对于ViewController，我们不需要保存特殊的原始状态，因为transform默认是identity
                    print("KeyboardManager: Found ViewController - \(type(of: viewController))")
                }
                break
            }
            responder = responder?.next
        }

        // 查找ScrollView（查找最近的可滚动的ScrollView）
        var superview = textInputView.superview
        while superview != nil {
            if let scrollView = superview as? UIScrollView {
                // 检查ScrollView是否可以滚动
                if scrollView.isScrollEnabled {
                    // 只有在ScrollView发生变化时才保存原始状态
                    if currentScrollView != scrollView {
                        currentScrollView = scrollView
                        // 保存真正的原始状态（未被键盘调整过的状态）
                        if !hasAdjustedView {
                            originalContentInset = scrollView.contentInset
                            originalContentOffset = scrollView.contentOffset
                            originalScrollIndicatorInsets = scrollView.scrollIndicatorInsets
                            print("KeyboardManager: Saved original state - offset: \(originalContentOffset), inset: \(originalContentInset)")
                        }
                        print("KeyboardManager: Found ScrollView - contentSize: \(scrollView.contentSize), bounds: \(scrollView.bounds)")
                    }
                    break
                }
            }
            superview = superview?.superview
        }

        print("KeyboardManager: Container found - ScrollView: \(currentScrollView != nil), ViewController: \(currentViewController != nil)")
    }

    /// 调整视图以避免键盘遮挡
    private func adjustForKeyboard(duration: Double, curve: UInt) {
        guard let activeView = activeTextInputView,
              let window = activeView.window else { return }

        // 强制布局更新，确保获取正确的frame
        window.layoutIfNeeded()
        activeView.superview?.layoutIfNeeded()
        activeView.layoutIfNeeded()

        // 计算输入框在屏幕中的位置
        let textInputFrame = activeView.convert(activeView.bounds, to: window)
        let keyboardTop = window.bounds.height - keyboardHeight
        let textInputBottom = textInputFrame.maxY

        print("KeyboardManager: textInputBottom: \(textInputBottom), keyboardTop: \(keyboardTop), overlap needed: \(textInputBottom + keyboardDistance > keyboardTop)")

        // 检查是否需要调整
        guard textInputBottom + keyboardDistance > keyboardTop else { return }

        let overlap = textInputBottom + keyboardDistance - keyboardTop

        if let scrollView = currentScrollView {
            adjustScrollView(scrollView, overlap: overlap, duration: duration, curve: curve)
        } else if let viewController = currentViewController {
            adjustViewController(viewController, overlap: overlap, duration: duration, curve: curve)
        }
    }

    /// 调整ScrollView
    private func adjustScrollView(_ scrollView: UIScrollView, overlap: CGFloat, duration: Double, curve: UInt) {
        guard let activeView = activeTextInputView,
              let window = scrollView.window,
              !isAnimating else { return }

        isAnimating = true

        // 强制布局更新
        scrollView.layoutIfNeeded()

        // 计算ScrollView在屏幕中的位置
        let scrollViewFrame = scrollView.convert(scrollView.bounds, to: window)
        let keyboardTop = window.bounds.height - keyboardHeight

        // 计算键盘与ScrollView的重叠部分
        let scrollViewBottom = scrollViewFrame.maxY
        let keyboardOverlap = max(0, scrollViewBottom - keyboardTop)

        // 只有当键盘确实遮挡了ScrollView时才调整
        guard keyboardOverlap > 0 else {
            isAnimating = false
            return
        }

        // 调整contentInset
        var newContentInset = originalContentInset
        newContentInset.bottom = originalContentInset.bottom + keyboardOverlap

        // 计算输入框在ScrollView中的位置
        let textInputFrame = activeView.convert(activeView.bounds, to: scrollView)

        // 计算可见区域高度（减去键盘遮挡部分）
        let visibleHeight = scrollView.bounds.height - keyboardOverlap

        // 计算输入框底部相对于ScrollView顶部的位置
        let textInputBottomInScrollView = textInputFrame.maxY

        // 计算当前可见区域的底部位置
        let currentVisibleBottom = scrollView.contentOffset.y + visibleHeight

        // 如果输入框底部超出了可见区域，需要滚动
        var newOffsetY = scrollView.contentOffset.y
        if textInputBottomInScrollView + keyboardDistance > currentVisibleBottom {
            newOffsetY = textInputBottomInScrollView + keyboardDistance - visibleHeight
        }

        // 限制在有效范围内
        let maxOffsetY = max(0, scrollView.contentSize.height - scrollView.bounds.height + newContentInset.bottom)
        newOffsetY = max(0, min(newOffsetY, maxOffsetY))

        print("KeyboardManager: ScrollView adjustment - keyboardOverlap: \(keyboardOverlap), textInputFrame: \(textInputFrame), newOffsetY: \(newOffsetY), visibleHeight: \(visibleHeight)")

        // 执行动画
        UIView.animate(withDuration: duration,
                      delay: 0,
                      options: UIView.AnimationOptions(rawValue: curve << 16)) { [weak self] in
            guard let self = self else { return }
            scrollView.contentInset = newContentInset
            scrollView.scrollIndicatorInsets = newContentInset
            scrollView.contentOffset = CGPoint(x: scrollView.contentOffset.x, y: newOffsetY)
        } completion: { [weak self] _ in
            self?.isAnimating = false
            self?.hasAdjustedView = true
        }
    }

    /// 调整ViewController
    private func adjustViewController(_ viewController: UIViewController, overlap: CGFloat, duration: Double, curve: UInt) {
        guard let activeView = activeTextInputView,
              let window = activeView.window,
              !isAnimating else { return }

        isAnimating = true

        let textInputFrame = activeView.convert(activeView.bounds, to: window)
        let keyboardTop = window.bounds.height - keyboardHeight
        let textInputBottom = textInputFrame.maxY + keyboardDistance

        // 只有当输入框被键盘遮挡时才调整
        if textInputBottom > keyboardTop {
            let moveDistance = textInputBottom - keyboardTop
            let maxUpwardMovement = textInputFrame.minY - viewController.view.safeAreaInsets.top - 20
            let actualMovement = min(moveDistance, maxUpwardMovement)

            print("KeyboardManager: ViewController adjustment - moveDistance: \(moveDistance), actualMovement: \(actualMovement), textInputBottom: \(textInputBottom), keyboardTop: \(keyboardTop)")

            UIView.animate(withDuration: duration,
                          delay: 0,
                          options: UIView.AnimationOptions(rawValue: curve << 16)) {
                viewController.view.transform = CGAffineTransform(translationX: 0, y: -actualMovement)
            } completion: { [weak self] _ in
                self?.isAnimating = false
                self?.hasAdjustedView = true
            }
        } else {
            print("KeyboardManager: ViewController no adjustment needed - textInputBottom: \(textInputBottom), keyboardTop: \(keyboardTop)")
            isAnimating = false
        }
    }

    /// 恢复视图位置
    private func restoreViews(duration: Double, curve: UInt) {
        print("KeyboardManager: Attempting to restore views - hasAdjustedView: \(hasAdjustedView), isAnimating: \(isAnimating), currentScrollView: \(currentScrollView != nil), currentViewController: \(currentViewController != nil)")

        // 如果正在动画中，等待动画完成后再恢复
        if isAnimating {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                self?.restoreViews(duration: duration, curve: curve)
            }
            return
        }

        // 如果没有调整过视图，直接清理状态
        if !hasAdjustedView {
            cleanupState()
            return
        }

        isAnimating = true

        if let scrollView = currentScrollView {
            print("KeyboardManager: Restoring ScrollView - original offset: \(originalContentOffset), original inset: \(originalContentInset)")

            // 检查ScrollView是否仍然有效
            guard scrollView.superview != nil else {
                print("KeyboardManager: ScrollView is no longer in view hierarchy")
                isAnimating = false
                cleanupState()
                return
            }

            UIView.animate(withDuration: duration,
                          delay: 0,
                          options: UIView.AnimationOptions(rawValue: curve << 16)) { [weak self] in
                guard let self = self else { return }

                // 恢复到真正的原始状态
                scrollView.contentInset = self.originalContentInset
                scrollView.scrollIndicatorInsets = self.originalScrollIndicatorInsets
                scrollView.contentOffset = self.originalContentOffset

                print("KeyboardManager: ScrollView restored - final offset: \(scrollView.contentOffset), final inset: \(scrollView.contentInset)")
            } completion: { [weak self] finished in
                print("KeyboardManager: ScrollView restore animation finished: \(finished)")
                self?.isAnimating = false
                self?.cleanupState()
            }
        } else if let viewController = currentViewController {
            print("KeyboardManager: Restoring ViewController transform")

            UIView.animate(withDuration: duration,
                          delay: 0,
                          options: UIView.AnimationOptions(rawValue: curve << 16)) {
                viewController.view.transform = .identity
            } completion: { [weak self] finished in
                print("KeyboardManager: ViewController restore animation finished: \(finished)")
                self?.isAnimating = false
                self?.cleanupState()
            }
        } else {
            print("KeyboardManager: No container to restore")
            isAnimating = false
            cleanupState()
        }
    }

    /// 清理之前的状态
    private func cleanupPreviousState() {
        if hasAdjustedView {
            print("KeyboardManager: Cleaning up previous state - restoring to original")
            // 立即恢复之前的状态，不使用动画
            if let scrollView = currentScrollView {
                scrollView.contentInset = originalContentInset
                scrollView.scrollIndicatorInsets = originalScrollIndicatorInsets
                scrollView.contentOffset = originalContentOffset
                print("KeyboardManager: Previous ScrollView state cleaned - offset: \(scrollView.contentOffset), inset: \(scrollView.contentInset)")
            } else if let viewController = currentViewController {
                viewController.view.transform = .identity
                print("KeyboardManager: Previous ViewController state cleaned - transform reset to identity")
            }
        }
        cleanupState()
    }

    /// 清理状态
    private func cleanupState() {
        currentScrollView = nil
        currentViewController = nil
        hasAdjustedView = false
        isAnimating = false
    }


    // MARK: - 工具栏和手势处理

    /// 添加工具栏
    private func addToolbar(to textInput: UIView) {
        // 检查是否已经有工具栏
        if let textField = textInput as? UITextField, textField.inputAccessoryView != nil {
            return
        }
        if let textView = textInput as? UITextView, textView.inputAccessoryView != nil {
            return
        }

        let toolbar = createToolbar()

        if let textField = textInput as? UITextField {
            textField.inputAccessoryView = toolbar
        } else if let textView = textInput as? UITextView {
            textView.inputAccessoryView = toolbar
        }
    }

    /// 创建工具栏
    private func createToolbar() -> UIToolbar {
        let toolbar = UIToolbar()
        toolbar.frame = CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 44)
        toolbar.backgroundColor = UIColor.systemBackground
        toolbar.tintColor = color_blue

        let flexSpace = UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
        let doneButton = UIBarButtonItem(barButtonSystemItem: .done, target: self, action: #selector(doneButtonTapped))

        toolbar.items = [flexSpace, doneButton]
        return toolbar
    }

    @objc private func doneButtonTapped() {
        activeTextInputView?.resignFirstResponder()
    }


    /// 添加点击手势
    private func addTapGesture() {
        guard let window = getKeyWindow() else { return }

        let existingGesture = window.gestureRecognizers?.first { gesture in
            return gesture is UITapGestureRecognizer && gesture.view == window
        }

        if existingGesture == nil {
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(windowTapped))
            tapGesture.cancelsTouchesInView = false
            tapGesture.delegate = self
            window.addGestureRecognizer(tapGesture)
        }
    }

    /// 移除点击手势
    private func removeTapGesture() {
        guard let window = getKeyWindow() else { return }

        window.gestureRecognizers?.forEach { gesture in
            if gesture is UITapGestureRecognizer {
                window.removeGestureRecognizer(gesture)
            }
        }
    }

    @objc private func windowTapped(_ gesture: UITapGestureRecognizer) {
        guard resignOnTouchOutside else { return }

        let location = gesture.location(in: gesture.view)

        // 检查点击位置是否在文本输入框内
        if let activeView = activeTextInputView,
           let window = activeView.window {
            let activeViewFrame = activeView.convert(activeView.bounds, to: window)
            if activeViewFrame.contains(location) {
                return
            }
        }

        print("KeyboardManager: Window tapped, resigning first responder")
        activeTextInputView?.resignFirstResponder()

        // 延迟检查是否需要强制恢复视图（减少延迟时间）
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            guard let self = self else { return }

            // 如果视图还没有恢复且没有正在动画，强制恢复
            if self.hasViewsToRestore() && !self.isAnimating {
                print("KeyboardManager: Views not restored after tap, forcing restore")
                self.forceRestoreViews()
            }
        }
    }

    /// 获取当前的keyWindow
    private func getKeyWindow() -> UIWindow? {
        if #available(iOS 15.0, *) {
            return UIApplication.shared
                .connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .first(where: { $0.activationState == .foregroundActive })?
                .windows
                .first(where: \.isKeyWindow)
        } else if #available(iOS 13.0, *) {
            return UIApplication.shared.windows.first(where: \.isKeyWindow)
        } else {
            return UIApplication.shared.keyWindow
        }
    }
}

// MARK: - UIGestureRecognizerDelegate

extension KeyboardManager: UIGestureRecognizerDelegate {

    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }

    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        if touch.view is UIButton || touch.view is UIControl {
            return false
        }
        return true
    }
}
