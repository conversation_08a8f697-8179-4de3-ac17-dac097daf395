//
//  BasePaddingLabel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

class BasePaddingLabel: UILabel {
    
    // 内边距属性
    @IBInspectable
    var paddingLeft: CGFloat = 0 {
        didSet { updateInsets() }
    }
    
    @IBInspectable
    var paddingRight: CGFloat = 0 {
        didSet { updateInsets() }
    }
    
    @IBInspectable
    var paddingTop: CGFloat = 0 {
        didSet { updateInsets() }
    }
    
    @IBInspectable
    var paddingBottom: CGFloat = 0 {
        didSet { updateInsets() }
    }
    
    // 圆角属性
    @IBInspectable
    var cornerRadius: CGFloat = 0 {
        didSet {
            layer.cornerRadius = cornerRadius
            layer.masksToBounds = cornerRadius > 0
        }
    }
    
    // 内边距
    private var insets: UIEdgeInsets = .zero
    
    private func updateInsets() {
        insets = UIEdgeInsets(
            top: paddingTop,
            left: paddingLeft,
            bottom: paddingBottom,
            right: paddingRight
        )
        invalidateIntrinsicContentSize()
    }
    override init(frame: CGRect) {
        super.init(frame: frame)
        textAlignment = .center
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    // 重写内部尺寸计算方法，考虑内边距
    override var intrinsicContentSize: CGSize {
        let superContentSize = super.intrinsicContentSize
        let width = superContentSize.width + insets.left + insets.right
        let height = superContentSize.height + insets.top + insets.bottom
        return CGSize(width: width, height: height)
    }
    
    // 重写绘制文本区域，考虑内边距
    override func textRect(forBounds bounds: CGRect, limitedToNumberOfLines numberOfLines: Int) -> CGRect {
        let insetRect = bounds.inset(by: insets)
        let textRect = super.textRect(forBounds: insetRect, limitedToNumberOfLines: numberOfLines)
        let invertedInsets = UIEdgeInsets(
            top: -insets.top,
            left: -insets.left,
            bottom: -insets.bottom,
            right: -insets.right
        )
        return textRect.inset(by: invertedInsets)
    }
    
    override func drawText(in rect: CGRect) {
        super.drawText(in: rect.inset(by: insets))
    }
    
    // 便捷方法：同时设置所有内边距
    func setPadding(_ top: CGFloat, _ left: CGFloat, _ bottom: CGFloat, _ right: CGFloat) {
        paddingTop = top
        paddingLeft = left
        paddingBottom = bottom
        paddingRight = right
        updateInsets()
    }
    
    // 便捷方法：设置相同的内边距
    func setPadding(_ padding: CGFloat) {
        setPadding(padding, padding, padding, padding)
    }
    
    // 便捷方法：设置左右相同的内边距
    func setHorizontalPadding(_ padding: CGFloat) {
        paddingLeft = padding
        paddingRight = padding
        updateInsets()
    }
    
    // 便捷方法：设置上下相同的内边距
    func setVerticalPadding(_ padding: CGFloat) {
        paddingTop = padding
        paddingBottom = padding
        updateInsets()
    }
    
    // 便捷方法：分别设置水平和垂直内边距
    func setPadding(horizontal: CGFloat, vertical: CGFloat) {
        paddingLeft = horizontal
        paddingRight = horizontal
        paddingTop = vertical
        paddingBottom = vertical
        updateInsets()
    }
    
    // 便捷方法：设置圆角和内边距
    func setCornerRadiusWithPadding(radius: CGFloat, padding: CGFloat) {
        self.cornerRadius = radius
        self.setPadding(padding)
    }
}
