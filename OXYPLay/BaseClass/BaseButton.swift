//
//  BaseButton.swift
//  OXYPLay
//
//  Created by Renhw on 2023/8/12.
//

import SnapKit
import UIKit

/// 图文排列方式
public enum ButtonImagePosition {
    /// 图片在左，文字在右
    case left
    /// 图片在右，文字在左
    case right
    /// 图片在上，文字在下
    case top
    /// 图片在下，文字在上
    case bottom
}

/// 基础按钮类，支持图文布局、圆角、边框等功能
public class BaseButton: UIButton {
    // MARK: - 公开属性

    /// 图文之间的间距，默认为 5
    public var spacing: CGFloat = 5.0 {
        didSet {
            setNeedsLayout()
        }
    }

    /// 图片位置，默认为左侧
    public var imagePosition: ButtonImagePosition = .left {
        didSet {
            setNeedsLayout()
        }
    }

    /// 图文边距，默认为 UIEdgeInsets.zero
    public var contentInsets: UIEdgeInsets = .zero {
        didSet {
            setNeedsLayout()
        }
    }

    /// 水平方向内边距，设置后会自动更新contentInsets的left和right
    public var horizontalPadding: CGFloat = 0 {
        didSet {
            updateContentInsets()
        }
    }

    /// 垂直方向内边距，设置后会自动更新contentInsets的top和bottom
    public var verticalPadding: CGFloat = 0 {
        didSet {
            updateContentInsets()
        }
    }

    /// 更新内边距
    private func updateContentInsets() {
        contentInsets = UIEdgeInsets(
            top: verticalPadding,
            left: horizontalPadding,
            bottom: verticalPadding,
            right: horizontalPadding
        )
    }

    /// 圆角大小，默认为 0
    public var cornerRadius: CGFloat = 0 {
        didSet {
            layer.cornerRadius = cornerRadius
            layer.masksToBounds = cornerRadius > 0
        }
    }

    /// 边框宽度，默认为 0
    public var borderWidth: CGFloat = 0 {
        didSet {
            layer.borderWidth = borderWidth
        }
    }

    /// 边框颜色，默认为 clear
    public var borderColor: UIColor = .clear {
        didSet {
            layer.borderColor = borderColor.cgColor
        }
    }

    /// 是否设置圆角为高度的一半（胶囊形状）
    public var isRounded: Bool = false {
        didSet {
            if isRounded {
                makeRounded()
            }
        }
    }

    /// 背景填充颜色字典，针对不同状态
    private var backgroundColorDict: [UIControl.State.RawValue: UIColor] = [:]

    // MARK: - 初始化方法

    override public init(frame: CGRect) {
        super.init(frame: frame)
        setupButton()
    }

    public required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupButton()
    }

    /// 便捷初始化方法
    public convenience init(title: String? = nil,
                            font: UIFont? = nil,
                            titleColor: UIColor? = nil,
                            image: UIImage? = nil,
                            backgroundColor: UIColor? = nil)
    {
        self.init(frame: .zero)

        if let title = title {
            setTitle(title, for: .normal)
        }

        if let font = font {
            titleLabel?.font = font
        }

        if let titleColor = titleColor {
            setTitleColor(titleColor, for: .normal)
        }

        if let image = image {
            setImage(image, for: .normal)
        }

        if let backgroundColor = backgroundColor {
            setBackgroundColor(backgroundColor, for: .normal)
        }
    }

    // MARK: - 私有方法

    private func setupButton() {
        // 设置基本属性
        titleLabel?.lineBreakMode = .byTruncatingTail

        // 默认内容模式
        contentMode = .center
        contentHorizontalAlignment = .center
        contentVerticalAlignment = .center

        // 支持多行显示
        titleLabel?.numberOfLines = 0
//
//        // 启用自动布局
//        translatesAutoresizingMaskIntoConstraints = false
    }

    // MARK: - 公开方法

    /// 设置指定状态的背景颜色
    public func setBackgroundColor(_ color: UIColor, for state: UIControl.State) {
        backgroundColorDict[state.rawValue] = color

        // 立即更新当前状态的背景色
        if state.rawValue == self.state.rawValue {
            backgroundColor = color
        }
    }

    /// 获取指定状态的背景颜色
    public func backgroundColor(for state: UIControl.State) -> UIColor? {
        return backgroundColorDict[state.rawValue]
    }

    // MARK: - 重写方法

    override public var isHighlighted: Bool {
        didSet {
            updateBackgroundColor()
        }
    }

    override public var isSelected: Bool {
        didSet {
            updateBackgroundColor()
        }
    }

    override public var isEnabled: Bool {
        didSet {
            updateBackgroundColor()
        }
    }

    private func updateBackgroundColor() {
        if let color = backgroundColorDict[state.rawValue] {
            backgroundColor = color
        } else if let normalColor = backgroundColorDict[UIControl.State.normal.rawValue] {
            backgroundColor = normalColor
        }
    }

    override public func layoutSubviews() {
        super.layoutSubviews()

        // 如果设置了圆形按钮，更新圆角
        if isRounded {
            makeRounded()
        }

        // 如果没有同时设置图片和文字，则使用系统默认布局
        guard let imageSize = imageView?.image?.size,
              let titleSize = titleLabel?.intrinsicContentSize,
              imageSize.width > 0, titleSize.width > 0
        else {
            super.layoutSubviews()
            return
        }

        // 按钮的内容区域
        let contentRect = bounds.inset(by: contentInsets)

        // 根据不同的图片位置布局图片和文字
        switch imagePosition {
        case .left:
            layoutImageLeft(imageSize: imageSize, titleSize: titleSize, contentRect: contentRect)
        case .right:
            layoutImageRight(imageSize: imageSize, titleSize: titleSize, contentRect: contentRect)
        case .top:
            layoutImageTop(imageSize: imageSize, titleSize: titleSize, contentRect: contentRect)
        case .bottom:
            layoutImageBottom(imageSize: imageSize, titleSize: titleSize, contentRect: contentRect)
        }
    }

    // MARK: - 布局方法

    private func layoutImageLeft(imageSize: CGSize, titleSize: CGSize, contentRect: CGRect) {
        let totalWidth = imageSize.width + spacing + titleSize.width
        let imageOriginX = contentRect.minX + (contentRect.width - totalWidth) / 2
        let titleOriginX = imageOriginX + imageSize.width + spacing

        imageView?.frame = CGRect(
            x: imageOriginX,
            y: contentRect.minY + (contentRect.height - imageSize.height) / 2,
            width: imageSize.width,
            height: imageSize.height
        )

        titleLabel?.frame = CGRect(
            x: titleOriginX,
            y: contentRect.minY + (contentRect.height - titleSize.height) / 2,
            width: titleSize.width,
            height: titleSize.height
        )
    }

    private func layoutImageRight(imageSize: CGSize, titleSize: CGSize, contentRect: CGRect) {
        let totalWidth = titleSize.width + spacing + imageSize.width
        let titleOriginX = contentRect.minX + (contentRect.width - totalWidth) / 2
        let imageOriginX = titleOriginX + titleSize.width + spacing

        titleLabel?.frame = CGRect(
            x: titleOriginX,
            y: contentRect.minY + (contentRect.height - titleSize.height) / 2,
            width: titleSize.width,
            height: titleSize.height
        )

        imageView?.frame = CGRect(
            x: imageOriginX,
            y: contentRect.minY + (contentRect.height - imageSize.height) / 2,
            width: imageSize.width,
            height: imageSize.height
        )
    }

    private func layoutImageTop(imageSize: CGSize, titleSize: CGSize, contentRect: CGRect) {
        let totalHeight = imageSize.height + spacing + titleSize.height
        let imageOriginY = contentRect.minY + (contentRect.height - totalHeight) / 2
        let titleOriginY = imageOriginY + imageSize.height + spacing

        imageView?.frame = CGRect(
            x: contentRect.minX + (contentRect.width - imageSize.width) / 2,
            y: imageOriginY,
            width: imageSize.width,
            height: imageSize.height
        )

        titleLabel?.frame = CGRect(
            x: contentRect.minX + (contentRect.width - titleSize.width) / 2,
            y: titleOriginY,
            width: titleSize.width,
            height: titleSize.height
        )
    }

    private func layoutImageBottom(imageSize: CGSize, titleSize: CGSize, contentRect: CGRect) {
        let totalHeight = titleSize.height + spacing + imageSize.height
        let titleOriginY = contentRect.minY + (contentRect.height - totalHeight) / 2
        let imageOriginY = titleOriginY + titleSize.height + spacing

        titleLabel?.frame = CGRect(
            x: contentRect.minX + (contentRect.width - titleSize.width) / 2,
            y: titleOriginY,
            width: titleSize.width,
            height: titleSize.height
        )

        imageView?.frame = CGRect(
            x: contentRect.minX + (contentRect.width - imageSize.width) / 2,
            y: imageOriginY,
            width: imageSize.width,
            height: imageSize.height
        )
    }

    /// 计算按钮内容的固有大小
    override public var intrinsicContentSize: CGSize {
        // 获取标题大小
        let titleSize = titleLabel?.intrinsicContentSize ?? .zero

        // 获取图片大小
        let imageSize = imageView?.image?.size ?? .zero

        // 计算内容总大小
        var contentSize: CGSize = .zero

        switch imagePosition {
        case .left, .right:
            // 左右布局时，宽度为图片宽度+间距+文字宽度，高度为最大的高度
            contentSize.width = imageSize.width + titleSize.width
            if imageSize.width > 0 && titleSize.width > 0 {
                contentSize.width += spacing
            }
            contentSize.height = max(imageSize.height, titleSize.height)
        case .top, .bottom:
            // 上下布局时，宽度为最大的宽度，高度为图片高度+间距+文字高度
            contentSize.width = max(imageSize.width, titleSize.width)
            contentSize.height = imageSize.height + titleSize.height
            if imageSize.height > 0 && titleSize.height > 0 {
                contentSize.height += spacing
            }
        }

        // 添加内边距
        contentSize.width += contentInsets.left + contentInsets.right
        contentSize.height += contentInsets.top + contentInsets.bottom

        return contentSize
    }

    /// 响应触摸事件
    override public func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        // 点击时添加动画效果
        UIView.animate(withDuration: 0.1) {
            self.alpha = 0.7
        }
    }

    override public func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesEnded(touches, with: event)
        // 恢复正常状态
        UIView.animate(withDuration: 0.1) {
            self.alpha = 1.0
        }
    }

    override public func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesCancelled(touches, with: event)
        // 恢复正常状态
        UIView.animate(withDuration: 0.1) {
            self.alpha = 1.0
        }
    }

    // MARK: - 便捷方法

    /// 将按钮设置为胶囊形状（圆角为高度的一半）
    public func makeRounded() {
        cornerRadius = bounds.height / 2
        layer.masksToBounds = true
    }
}
