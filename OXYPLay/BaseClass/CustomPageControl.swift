import UIKit

/// 自定义页面指示器控件，可自定义当前页指示器和普通页指示器的颜色、大小、形状等
class CustomPageControl: BaseView {
    // MARK: - 公开属性
    
    /// 总页数
    var numberOfPages: Int = 0 {
        didSet {
            updateDots()
        }
    }
    
    /// 当前页索引
    var currentPage: Int = 0 {
        didSet {
            updateCurrentPageDisplay()
        }
    }
    
    /// 当前页指示器颜色
    var currentPageIndicatorTintColor: UIColor = color_blue {
        didSet {
            updateDotsAppearance()
        }
    }
    
    /// 普通页指示器颜色
    var pageIndicatorTintColor: UIColor = .white {
        didSet {
            updateDotsAppearance()
        }
    }
    
    /// 当前页指示器大小
    var currentPageIndicatorSize: CGSize = CGSize(width: 4, height: 4) {
        didSet {
            updateDotsAppearance()
        }
    }
    
    /// 普通页指示器大小
    var pageIndicatorSize: CGSize = CGSize(width: 4, height: 4) {
        didSet {
            updateDotsAppearance()
        }
    }
    
    /// 指示器间距
    var dotSpacing: CGFloat = 5 {
        didSet {
            updateDotsLayout()
        }
    }
    
    /// 背景颜色
    var containerBackgroundColor: UIColor? {
        didSet {
            containerView.backgroundColor = containerBackgroundColor
        }
    }
    
    /// 背景圆角
    var containerCornerRadius: CGFloat = 6 {
        didSet {
            containerView.layer.cornerRadius = containerCornerRadius
        }
    }
    
    // MARK: - 私有属性
    
    private var dotViews: [UIView] = []
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = containerCornerRadius
        return view
    }()
    
    private lazy var stackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.alignment = .center
        stack.distribution = .equalSpacing
        stack.spacing = dotSpacing
        return stack
    }()
    
    // MARK: - 初始化
    
    override func configUI() {
        addSubview(containerView)
        containerView.addSubview(stackView)
    }
    
    override func configLayout() {
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(0)
        }
        
        stackView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.equalToSuperview().offset(6)
            make.right.equalToSuperview().offset(-6)
            make.top.bottom.equalToSuperview().inset(4)
        }
    }
    
    // MARK: - 私有方法
    
    private func updateDots() {
        // 清除现有的点
        dotViews.forEach { $0.removeFromSuperview() }
        stackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        dotViews.removeAll()
        
        // 创建新的点
        for _ in 0..<numberOfPages {
            let dotView = createDotView()
            dotViews.append(dotView)
            stackView.addArrangedSubview(dotView)
        }
        
        updateCurrentPageDisplay()
        updateContainerSize()
    }
    
    private func createDotView() -> UIView {
        let view = UIView()
        view.backgroundColor = pageIndicatorTintColor
        view.layer.cornerRadius = pageIndicatorSize.height / 2
        view.snp.makeConstraints { make in
            make.size.equalTo(pageIndicatorSize)
        }
        return view
    }
    
    private func updateCurrentPageDisplay() {
        guard currentPage >= 0, currentPage < dotViews.count else { return }
        
        for (index, dotView) in dotViews.enumerated() {
            if index == currentPage {
                dotView.backgroundColor = currentPageIndicatorTintColor
                dotView.snp.updateConstraints { make in
                    make.size.equalTo(currentPageIndicatorSize)
                }
                dotView.layer.cornerRadius = currentPageIndicatorSize.height / 2
            } else {
                dotView.backgroundColor = pageIndicatorTintColor
                dotView.snp.updateConstraints { make in
                    make.size.equalTo(pageIndicatorSize)
                }
                dotView.layer.cornerRadius = pageIndicatorSize.height / 2
            }
        }
        
        updateContainerSize()
    }
    
    private func updateDotsAppearance() {
        for (index, dotView) in dotViews.enumerated() {
            if index == currentPage {
                dotView.backgroundColor = currentPageIndicatorTintColor
                dotView.snp.updateConstraints { make in
                    make.size.equalTo(currentPageIndicatorSize)
                }
                dotView.layer.cornerRadius = currentPageIndicatorSize.height / 2
            } else {
                dotView.backgroundColor = pageIndicatorTintColor
                dotView.snp.updateConstraints { make in
                    make.size.equalTo(pageIndicatorSize)
                }
                dotView.layer.cornerRadius = pageIndicatorSize.height / 2
            }
        }
    }
    
    private func updateDotsLayout() {
        stackView.spacing = dotSpacing
        updateContainerSize()
    }
    
    private func updateContainerSize() {
        let totalWidth = calculateTotalWidth()
        containerView.snp.updateConstraints { make in
            make.width.equalTo(totalWidth + 20) // 左右各10点的内边距
        }
    }
    
    private func calculateTotalWidth() -> CGFloat {
        var totalWidth: CGFloat = 0
        
        for (index, _) in dotViews.enumerated() {
            if index == currentPage {
                totalWidth += currentPageIndicatorSize.width
            } else {
                totalWidth += pageIndicatorSize.width
            }
        }
        
        // 添加间距
        if dotViews.count > 1 {
            totalWidth += dotSpacing * CGFloat(dotViews.count - 1)
        }
        
        return totalWidth
    }
} 
