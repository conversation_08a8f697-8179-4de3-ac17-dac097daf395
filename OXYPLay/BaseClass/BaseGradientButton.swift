//
//  BaseGradientButton.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/6/21.
//

import UIKit

class BaseGradientButton: BaseButton {
    // MARK: - 属性

    /// 渐变颜色数组（非空检查）
    private var _gradientColors: [UIColor] = []
    var gradientColors: [UIColor] {
        get { return _gradientColors }
        set {
            // 过滤掉无效颜色
            _gradientColors = newValue.compactMap { $0 }
            updateGradientLayer()
        }
    }

    /// 渐变方向 (默认从左到右)
    var gradientDirection: GradientDirection = .leftToRight {
        didSet {
            updateGradientLayer()
        }
    }
    
    /// 禁用状态下的透明度
    var disabledAlpha: CGFloat = 0.6

    // 渐变层
    private var gradientLayer: CAGradientLayer?

    // 渐变方向枚举
    enum GradientDirection {
        case leftToRight
        case topToBottom
        case leftTopToRightBottom
        case leftBottomToRightTop

        var startPoint: CGPoint {
            switch self {
            case .leftToRight: return CGPoint(x: 0, y: 0.5)
            case .topToBottom: return CGPoint(x: 0.5, y: 0)
            case .leftTopToRightBottom: return CGPoint(x: 0, y: 0)
            case .leftBottomToRightTop: return CGPoint(x: 0, y: 1)
            }
        }

        var endPoint: CGPoint {
            switch self {
            case .leftToRight: return CGPoint(x: 1, y: 0.5)
            case .topToBottom: return CGPoint(x: 0.5, y: 1)
            case .leftTopToRightBottom: return CGPoint(x: 1, y: 1)
            case .leftBottomToRightTop: return CGPoint(x: 1, y: 0)
            }
        }
    }

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        commonInit()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        commonInit()
    }

    private func commonInit() {
        titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        setTitleColor(.white, for: .normal)
        
        // 监听isEnabled属性变化
        addTarget(self, action: #selector(updateEnabledState), for: .valueChanged)
    }
    
    // MARK: - 状态更新
    
    @objc private func updateEnabledState() {
        updateGradientLayer()
    }
    
    override var isEnabled: Bool {
        didSet {
            updateGradientLayer()
        }
    }

    // MARK: - 布局

    override func layoutSubviews() {
        super.layoutSubviews()
        updateGradientLayer()
    }

    // MARK: - 渐变层更新

    private func updateGradientLayer() {
        // 移除旧的渐变层
        gradientLayer?.removeFromSuperlayer()

        // 确保有有效的渐变颜色
        guard !gradientColors.isEmpty else { return }

        let gradient = CAGradientLayer()
        gradient.frame = bounds
        gradient.colors = gradientColors.map { $0.cgColor }
        gradient.startPoint = gradientDirection.startPoint
        gradient.endPoint = gradientDirection.endPoint
        gradient.cornerRadius = cornerRadius
        
        // 根据按钮状态设置透明度
        gradient.opacity = isEnabled ? 1.0 : Float(disabledAlpha)

        layer.insertSublayer(gradient, at: 0)
        gradientLayer = gradient

        // 确保标题在最上层
        bringSubviewToFront(titleLabel ?? UIView())
        bringSubviewToFront(imageView ?? UIView())
    }
}
