//
//  BaseTabToolBar.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/7.
//

import UIKit
import Combine
import CombineCocoa

/// 按钮项配置
struct ToolBarButtonItem {
    let normalImage: UIImage?
    let selectedImage: UIImage?
    let title: String
    var isSelected: Bool = false
    var tag: Int = 0
    var titleColor: UIColor?
    var backgroundColor: UIColor?
    var borderColor: UIColor?
}

/// 工具栏样式枚举
enum TabToolBarStyle {
    /// 左侧多个按钮，右侧一个占满剩余宽度的按钮
    case leftMultipleRightFull
    /// 左侧一个固定按钮，右侧多个固定宽度按钮
    case leftSingleRightMultiple
}

/// 工具栏代理协议
protocol TabToolBarDelegate: AnyObject {
    /// 左侧按钮点击事件
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickLeftButtonAt index: Int, item: ToolBarButtonItem)
    /// 右侧按钮点击事件
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?)
}

extension TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickLeftButtonAt index: Int, item: ToolBarButtonItem) {}
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {}
}

class BaseTabToolBar: BaseView {

    // MARK: - 属性

    weak var delegate: TabToolBarDelegate?
    private var style: TabToolBarStyle = .leftMultipleRightFull
    private var leftButtons: [BaseButton] = []
    private var rightButtons: [BaseButton] = []
    private var leftButtonItems: [ToolBarButtonItem] = []
    private var rightButtonItems: [ToolBarButtonItem] = []

    // MARK: - UI组件

    lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.shadowColor = UIColor(hexString: "6F6F77", transparency: 0.16)?.cgColor
        $0.layer.shadowOffset = CGSize(width: 0, height: -2)
        $0.layer.shadowRadius = 16
        $0.layer.shadowOpacity = 1
        $0.layer.masksToBounds = false

        // 设置左上角和右上角圆角
        $0.layer.cornerRadius = 16
        $0.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
    }

    lazy var mainStackView = UIStackView().then {
        $0.axis = .horizontal
        $0.spacing = 0
        $0.alignment = .fill

    }

    lazy var leftStackView = UIStackView().then {
        $0.axis = .horizontal
        $0.spacing = 12
        $0.distribution = .fill
        $0.alignment = .fill
    }

    lazy var rightStackView = UIStackView().then {
        $0.axis = .horizontal
        $0.spacing = 12
        $0.distribution = .fill
        $0.alignment = .fill
    }

    // 右侧单个按钮（用于 leftMultipleRightFull 样式）
    lazy var singleRightButton = BaseGradientButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.setTitleColor(.white, for: .normal)
        $0.backgroundColor = color_blue
        $0.isRounded = true
        $0.isUserInteractionEnabled = true
    }

    // MARK: - UI设置

    override func configUI() {
        backgroundColor = .clear
        addSubview(containerView)
        containerView.addSubview(mainStackView)
        mainStackView.addArrangedSubview(leftStackView)
        mainStackView.addArrangedSubview(rightStackView)
    }

    override func configLayout() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 基础约束，不设置右边距，避免初始化时的约束冲突
        mainStackView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.top.equalToSuperview().offset(12)
            make.height.equalTo(44)
        }
    }

    // MARK: - 公共方法

    /// 配置工具栏样式为左侧多个按钮，右侧一个占满剩余宽度的按钮
    /// - Parameters:
    ///   - leftItems: 左侧按钮配置项数组
    ///   - rightButtonTitle: 右侧按钮标题
    func configureLeftMultipleRightFull(leftItems: [ToolBarButtonItem], rightButtonTitle: String) {
        style = .leftMultipleRightFull

        // 保存按钮配置
        leftButtonItems = leftItems
        rightButtonItems = []

        // 创建左侧按钮
        for (index, item) in leftItems.enumerated() {
            let button = createLeftButton(with: item, at: index)
            leftButtons.append(button)
            leftStackView.addArrangedSubview(button)
        }

        // 设置右侧单个按钮
        singleRightButton.setTitle(rightButtonTitle, for: .normal)
        rightStackView.addArrangedSubview(singleRightButton)
        leftStackView.isHidden = leftItems.isEmpty
       
        // 设置约束
        setupConstraintsForLeftMultipleRightFull()

        // 设置事件绑定
        setupBindings()
    }

    /// 配置工具栏样式为左侧一个固定按钮，右侧多个固定宽度按钮
    /// - Parameters:
    ///   - leftItem: 左侧按钮配置项
    ///   - rightItems: 右侧按钮配置项数组
    func configureLeftSingleRightMultiple(leftItem: ToolBarButtonItem, rightItems: [ToolBarButtonItem]) {
        style = .leftSingleRightMultiple

        // 保存按钮配置
        leftButtonItems = [leftItem]
        rightButtonItems = rightItems

        // 创建左侧单个按钮
        let leftButton = createLeftButton(with: leftItem, at: 0)
        leftButtons.append(leftButton)
        leftStackView.addArrangedSubview(leftButton)

        // 创建右侧多个按钮
        for (index, item) in rightItems.enumerated() {
            let button = createRightButton(with: item, at: index)
            rightButtons.append(button)
            rightStackView.addArrangedSubview(button)
        }
        rightStackView.isHidden = rightItems.isEmpty


        // 设置约束
        setupConstraintsForLeftSingleRightMultiple()

        // 设置事件绑定
        setupBindings()
    }

    /// 设置右侧按钮渐变颜色（仅适用于 leftMultipleRightFull 样式）
    /// - Parameters:
    ///   - colors: 渐变颜色数组
    ///   - direction: 渐变方向
    func setRightButtonGradient(colors: [UIColor], direction: BaseGradientButton.GradientDirection = .leftToRight) {
        singleRightButton.gradientColors = colors
        singleRightButton.gradientDirection = direction
    }

    /// 更新左侧按钮选中状态
    /// - Parameters:
    ///   - index: 按钮索引
    ///   - isSelected: 是否选中
    func updateLeftButtonSelection(at index: Int, isSelected: Bool) {
        guard index < leftButtons.count else { return }

        leftButtons[index].isSelected = isSelected
        if index < leftButtonItems.count {
            leftButtonItems[index].isSelected = isSelected
        }
    }

    /// 更新右侧按钮选中状态（仅适用于 leftSingleRightMultiple 样式）
    /// - Parameters:
    ///   - index: 按钮索引
    ///   - isSelected: 是否选中
    func updateRightButtonSelection(at index: Int, isSelected: Bool) {
        guard index < rightButtons.count else { return }

        rightButtons[index].isSelected = isSelected
        if index < rightButtonItems.count {
            rightButtonItems[index].isSelected = isSelected
        }
    }

    // MARK: - 私有方法

    /// 创建左侧按钮
    private func createLeftButton(with item: ToolBarButtonItem, at index: Int) -> BaseButton {
        let button = BaseButton().then {
            $0.setImage(item.normalImage, for: .normal)
            $0.setImage(item.selectedImage, for: .selected)
            $0.setTitle(item.title, for: .normal)
            $0.titleLabel?.font = UIFont.systemFont(ofSize: 10, weight: .regular)
            $0.setTitleColor(color_2B2C2F64, for: .normal)
            $0.imagePosition = .top
            $0.spacing = 6
            $0.tag = item.tag
            $0.isSelected = item.isSelected
            $0.setContentHuggingPriority(.required, for: .horizontal)
            $0.isUserInteractionEnabled = true
        }
        return button
    }

    /// 创建右侧按钮
    private func createRightButton(with item: ToolBarButtonItem, at index: Int) -> BaseButton {
        let button = BaseButton().then {
            $0.setImage(item.normalImage, for: .normal)
            $0.setImage(item.selectedImage, for: .selected)
            $0.setTitle(item.title, for: .normal)
            $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            $0.tag = item.tag
            $0.isSelected = item.isSelected
            $0.setContentHuggingPriority(.required, for: .horizontal)
            $0.isUserInteractionEnabled = true
            $0.isRounded = true

            // 设置自定义颜色或使用默认颜色
            let titleColor = UIColor(hexString: "2A72FF")!
            let bgColor = item.backgroundColor ?? UIColor.white
            let borderColor = item.borderColor ?? titleColor

            $0.setTitleColor(titleColor, for: .normal)
            $0.backgroundColor = bgColor
            $0.layer.borderColor = borderColor.cgColor

            // 如果没有图片，使用文字样式
            if item.normalImage == nil {
                $0.imagePosition = .left
                $0.spacing = 0
            } else {
                $0.imagePosition = .top
                $0.spacing = 6
                $0.titleLabel?.font = UIFont.systemFont(ofSize: 10, weight: .regular)
                $0.setTitleColor(color_2B2C2F64, for: .normal)
            }
        }
        return button
    }

    /// 设置约束 - 左侧多个按钮，右侧一个占满剩余宽度的按钮
    private func setupConstraintsForLeftMultipleRightFull() {

        // 左侧按钮固定宽度
        for button in leftButtons {
            button.snp.makeConstraints { make in
                make.width.equalTo(60)
                make.height.equalTo(44)
            }
        }

        // 右侧按钮占满剩余宽度
        singleRightButton.snp.makeConstraints { make in
            make.height.equalTo(44)
        }

    }

    /// 设置约束 - 左侧一个固定按钮，右侧多个固定宽度按钮
    private func setupConstraintsForLeftSingleRightMultiple() {
        let spacer = UIView()
        rightStackView.insertArrangedSubview(spacer, at: 0)
        // 左侧按钮固定宽度
        for button in leftButtons {
            button.snp.remakeConstraints { make in
                make.width.equalTo(60)
                make.height.equalTo(44)
            }
        }

        // 右侧按钮固定宽度
        for button in rightButtons {
            button.snp.remakeConstraints { make in
                make.width.equalTo(110)
                make.height.equalTo(44)
            }
        }
    }

    // MARK: - 事件绑定

    override func setupBindings() {
        // 清除之前的订阅
        cancellables.removeAll()

        switch style {
        case .leftMultipleRightFull:
            setupBindingsForLeftMultipleRightFull()
        case .leftSingleRightMultiple:
            setupBindingsForLeftSingleRightMultiple()
        }
    }

    /// 设置左侧多个按钮，右侧一个按钮的事件绑定
    private func setupBindingsForLeftMultipleRightFull() {
        // 右侧单个按钮点击事件
        singleRightButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.tabToolBar(self, didClickRightButtonAt: 0, item: nil)
            }
            .store(in: &cancellables)

        // 左侧按钮点击事件
        for (index, button) in leftButtons.enumerated() {
            button.tapPublisher
                .sink { [weak self] _ in
                    guard let self = self, index < self.leftButtonItems.count else { return }
                    self.delegate?.tabToolBar(self, didClickLeftButtonAt: index, item: self.leftButtonItems[index])
                }
                .store(in: &cancellables)
        }
    }

    /// 设置左侧一个按钮，右侧多个按钮的事件绑定
    private func setupBindingsForLeftSingleRightMultiple() {
        // 左侧单个按钮点击事件
        for (index, button) in leftButtons.enumerated() {
            button.tapPublisher
                .sink { [weak self] _ in
                    guard let self = self, index < self.leftButtonItems.count else { return }
                    self.delegate?.tabToolBar(self, didClickLeftButtonAt: index, item: self.leftButtonItems[index])
                }
                .store(in: &cancellables)
        }

        // 右侧多个按钮点击事件
        for (index, button) in rightButtons.enumerated() {
            button.tapPublisher
                .sink { [weak self] _ in
                    guard let self = self, index < self.rightButtonItems.count else { return }
                    self.delegate?.tabToolBar(self, didClickRightButtonAt: index, item: self.rightButtonItems[index])
                }
                .store(in: &cancellables)
        }
    }
}
