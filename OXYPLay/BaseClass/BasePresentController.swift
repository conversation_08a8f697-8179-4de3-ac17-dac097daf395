import UIKit

class BasePresentController: BaseViewController, CustomPresentable {
    var presentationDirection: PresentationDirection{
        return .bottomToTop
    }
    
    var rightMargin: CGFloat = 0.0
    
    
    // 实现CustomPresentable协议
    var presentationHeight: CGFloat {
        return UIScreen.main.bounds.height * 0.75 // 屏幕高度的75%
    }
    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textColor = color_2B2C2F
    }
    
    private lazy var closeButton = UIButton(type: .system).then {
        $0.setImage(UIImage(named: "closebuttonicon"), for: .normal)

    }
    lazy var contentView = UIView()
    lazy var bottomView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.layer.maskedCorners = [.layerMinXMinYCorner,.layerMaxXMinYCorner]
        $0.masksToBounds = true
    }
    lazy var bottomButton = BaseGradientButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.setTitleColor(.white, for: .normal)
        $0.backgroundColor = color_blue
        $0.isRounded = true
    }
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        view.backgroundColor = color_F6F8F9
        
        // 添加头部视图
        view.addSubview(closeButton)
        view.addSubview(titleLabel)
        view.addSubview(contentView)
        view.addSubview(bottomView)
        bottomView.addSubview(bottomButton)
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(14)
            make.right.equalTo(-14)
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(30)
            make.centerX.equalToSuperview()
        }
        contentView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(24)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(-ScreenInfo.totalTabBarHeight)
        }
        bottomView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
        bottomButton.snp.makeConstraints { make in
            make.left.top.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(44)
        }
        closeButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.dismiss(animated: true)
            }
            .store(in: &cancellables)
    }
    func hideBottomBar(){
        bottomView.isHidden = true
        contentView.snp.updateConstraints { make in
            make.bottom.equalTo(0)
        }
    }
    func configView(title:String?, bottomTitle:String = "确定"){
        self.bottomButton.setTitle(bottomTitle, for: .normal)
        self.titleLabel.text = title
    }
}
