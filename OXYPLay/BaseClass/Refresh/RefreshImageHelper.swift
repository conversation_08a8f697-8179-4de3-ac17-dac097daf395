//
//  RefreshImageHelper.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/1.
//

import UIKit

/// 刷新图片辅助类，用于方便地加载和设置GIF刷新图片
class RefreshImageHelper {

    /// 现代化刷新样式
    enum RefreshStyle {
        /// 简约现代风格
        case modern
        /// 渐变圆环动画
        case gradientRing
        /// 脉冲波动画
        case pulse
        /// 粒子效果
        case particle
    }

    /// 根据指定样式创建刷新配置
    static func createConfig(withStyle style: RefreshStyle) -> GIFRefreshConfig {
        var config = GIFRefreshConfig()

        // 统一的现代化文本样式
        config.textFont = UIFont.systemFont(ofSize: 12, weight: .medium)
        config.textColor = UIColor.systemGray
        config.backgroundColor = UIColor.clear
        config.imageSize = CGSize(width: 24, height: 24)
        
        config.refreshingText = "正在刷新"
        config.pullingText = "松开刷新"
        config.idleText = ""
        config.noMoreDataText = ""
        switch style {
        case .modern:
            // 创建现代化的圆形加载动画
            config.refreshingImages = createModernLoadingAnimation()
            config.idleImages = [createModernIdleIcon()]
            config.pullingImages = [createModernPullingIcon()]

        case .gradientRing:
            // 创建渐变圆环动画
            config.refreshingImages = createGradientRingAnimation()
            config.idleImages = [createGradientRingIdle()]
            config.pullingImages = [createGradientRingPulling()]

        case .pulse:
           
            // 创建脉冲波动画
            config.refreshingImages = createPulseAnimation()
            config.idleImages = [createPulseIdle()]
            config.pullingImages = [createPulsePulling()]

        case .particle:
          
            // 创建粒子效果动画
            config.refreshingImages = createParticleAnimation()
            config.idleImages = [createParticleIdle()]
            config.pullingImages = [createParticlePulling()]
        }

        return config
    }
    
    // MARK: - 现代化动画创建方法

    /// 创建现代化加载动画
    static func createModernLoadingAnimation() -> [UIImage] {
        var images: [UIImage] = []
        let size = CGSize(width: 24, height: 24)

        for i in 0..<12 {
            let angle = CGFloat(i) * .pi / 6
            if let image = createRotatingDotsImage(size: size, angle: angle) {
                images.append(image)
            }
        }
        return images
    }

    /// 创建现代化空闲图标
    static func createModernIdleIcon() -> UIImage {
        return createCircleArrowIcon(size: CGSize(width: 24, height: 24), direction: .down)
    }

    /// 创建现代化拉取图标
    static func createModernPullingIcon() -> UIImage {
        return createCircleArrowIcon(size: CGSize(width: 24, height: 24), direction: .up)
    }

    /// 创建渐变圆环动画
    static func createGradientRingAnimation() -> [UIImage] {
        var images: [UIImage] = []
        let size = CGSize(width: 24, height: 24)

        for i in 0..<8 {
            let progress = CGFloat(i) / 8.0
            if let image = createGradientRingImage(size: size, progress: progress) {
                images.append(image)
            }
        }
        return images
    }

    /// 创建渐变圆环空闲状态
    static func createGradientRingIdle() -> UIImage {
        return createGradientRingImage(size: CGSize(width: 24, height: 24), progress: 0.0) ?? UIImage()
    }

    /// 创建渐变圆环拉取状态
    static func createGradientRingPulling() -> UIImage {
        return createGradientRingImage(size: CGSize(width: 24, height: 24), progress: 0.3) ?? UIImage()
    }

    /// 创建脉冲波动画
    static func createPulseAnimation() -> [UIImage] {
        var images: [UIImage] = []
        let size = CGSize(width: 24, height: 24)

        for i in 0..<6 {
            let scale = 0.5 + CGFloat(i) * 0.1
            if let image = createPulseImage(size: size, scale: scale) {
                images.append(image)
            }
        }
        // 反向添加，形成脉冲效果
        for i in (0..<5).reversed() {
            let scale = 0.5 + CGFloat(i) * 0.1
            if let image = createPulseImage(size: size, scale: scale) {
                images.append(image)
            }
        }
        return images
    }

    /// 创建脉冲空闲状态
    static func createPulseIdle() -> UIImage {
        return createPulseImage(size: CGSize(width: 24, height: 24), scale: 0.5) ?? UIImage()
    }

    /// 创建脉冲拉取状态
    static func createPulsePulling() -> UIImage {
        return createPulseImage(size: CGSize(width: 24, height: 24), scale: 0.8) ?? UIImage()
    }

    /// 创建粒子效果动画
    static func createParticleAnimation() -> [UIImage] {
        var images: [UIImage] = []
        let size = CGSize(width: 24, height: 24)

        for i in 0..<10 {
            let frame = CGFloat(i)
            if let image = createParticleImage(size: size, frame: frame) {
                images.append(image)
            }
        }
        return images
    }

    /// 创建粒子空闲状态
    static func createParticleIdle() -> UIImage {
        return createParticleImage(size: CGSize(width: 24, height: 24), frame: 0) ?? UIImage()
    }

    /// 创建粒子拉取状态
    static func createParticlePulling() -> UIImage {
        return createParticleImage(size: CGSize(width: 24, height: 24), frame: 3) ?? UIImage()
    }

    // MARK: - 图像绘制辅助方法

    /// 创建旋转点动画图像
    static func createRotatingDotsImage(size: CGSize, angle: CGFloat) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        guard let context = UIGraphicsGetCurrentContext() else { return nil }

        let center = CGPoint(x: size.width / 2, y: size.height / 2)
        let radius: CGFloat = 8
        let dotRadius: CGFloat = 1.5

        // 绘制8个点，形成圆形排列
        for i in 0..<8 {
            let dotAngle = angle + CGFloat(i) * .pi / 4
            let x = center.x + cos(dotAngle) * radius
            let y = center.y + sin(dotAngle) * radius

            // 根据位置设置透明度，形成渐变效果
            let alpha = 0.2 + 0.8 * CGFloat(i) / 8.0
            UIColor.systemBlue.withAlphaComponent(alpha).setFill()

            let dotRect = CGRect(x: x - dotRadius, y: y - dotRadius, width: dotRadius * 2, height: dotRadius * 2)
            context.fillEllipse(in: dotRect)
        }

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }

    /// 创建圆形箭头图标
    static func createCircleArrowIcon(size: CGSize, direction: ArrowDirection) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        guard let context = UIGraphicsGetCurrentContext() else { return UIImage() }

        let center = CGPoint(x: size.width / 2, y: size.height / 2)
        let radius: CGFloat = 10

        // 绘制圆形背景
        UIColor.systemGray5.setFill()
        let circleRect = CGRect(x: center.x - radius, y: center.y - radius, width: radius * 2, height: radius * 2)
        context.fillEllipse(in: circleRect)

        // 绘制箭头
        UIColor.systemBlue.setStroke()
        context.setLineWidth(2)
        context.setLineCap(.round)

        let arrowSize: CGFloat = 6
        if direction == .down {
            // 向下箭头
            context.move(to: CGPoint(x: center.x - arrowSize/2, y: center.y - arrowSize/4))
            context.addLine(to: CGPoint(x: center.x, y: center.y + arrowSize/4))
            context.addLine(to: CGPoint(x: center.x + arrowSize/2, y: center.y - arrowSize/4))
        } else {
            // 向上箭头
            context.move(to: CGPoint(x: center.x - arrowSize/2, y: center.y + arrowSize/4))
            context.addLine(to: CGPoint(x: center.x, y: center.y - arrowSize/4))
            context.addLine(to: CGPoint(x: center.x + arrowSize/2, y: center.y + arrowSize/4))
        }
        context.strokePath()

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image ?? UIImage()
    }

    /// 创建渐变圆环图像
    static func createGradientRingImage(size: CGSize, progress: CGFloat) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        guard let context = UIGraphicsGetCurrentContext() else { return nil }

        let center = CGPoint(x: size.width / 2, y: size.height / 2)
        let radius: CGFloat = 9
        let lineWidth: CGFloat = 2

        // 绘制背景圆环
        UIColor.systemGray5.setStroke()
        context.setLineWidth(lineWidth)
        context.addArc(center: center, radius: radius, startAngle: 0, endAngle: .pi * 2, clockwise: false)
        context.strokePath()

        // 绘制进度圆环
        if progress > 0 {
            let endAngle = -.pi / 2 + progress * .pi * 2
            UIColor.systemBlue.setStroke()
            context.setLineWidth(lineWidth)
            context.setLineCap(.round)
            context.addArc(center: center, radius: radius, startAngle: -.pi / 2, endAngle: endAngle, clockwise: false)
            context.strokePath()
        }

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }

    /// 创建脉冲图像
    static func createPulseImage(size: CGSize, scale: CGFloat) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        guard let context = UIGraphicsGetCurrentContext() else { return nil }

        let center = CGPoint(x: size.width / 2, y: size.height / 2)
        let radius = 8 * scale

        // 绘制脉冲圆形，透明度随缩放变化
        let alpha = 1.0 - (scale - 0.5) / 0.5 * 0.7
        UIColor.systemBlue.withAlphaComponent(alpha).setFill()

        let rect = CGRect(x: center.x - radius, y: center.y - radius, width: radius * 2, height: radius * 2)
        context.fillEllipse(in: rect)

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }

    /// 创建粒子效果图像
    static func createParticleImage(size: CGSize, frame: CGFloat) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        guard let context = UIGraphicsGetCurrentContext() else { return nil }

        let center = CGPoint(x: size.width / 2, y: size.height / 2)

        // 绘制多个粒子，位置随帧数变化
        for i in 0..<6 {
            let angle = CGFloat(i) * .pi / 3 + frame * 0.1
            let distance = 6 + sin(frame * 0.2 + CGFloat(i)) * 2
            let x = center.x + cos(angle) * distance
            let y = center.y + sin(angle) * distance

            let particleSize: CGFloat = 1.5 + sin(frame * 0.3 + CGFloat(i)) * 0.5
            let alpha = 0.5 + sin(frame * 0.4 + CGFloat(i)) * 0.3

            UIColor.systemBlue.withAlphaComponent(alpha).setFill()
            let particleRect = CGRect(x: x - particleSize/2, y: y - particleSize/2, width: particleSize, height: particleSize)
            context.fillEllipse(in: particleRect)
        }

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }

    /// 箭头方向枚举
    enum ArrowDirection {
        case up, down
    }
}
