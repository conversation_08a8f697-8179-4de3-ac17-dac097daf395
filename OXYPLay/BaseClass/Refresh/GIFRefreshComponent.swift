//
//  GIFRefreshComponent.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/1.
//

import UIKit
import MJRefresh

/// GIF刷新组件配置
struct GIFRefreshConfig {
    /// 刷新状态文字
    var refreshingText: String = "正在加载..."
    /// 即将刷新状态文字
    var pullingText: String = "松开刷新..."
    /// 闲置状态文字
    var idleText: String = "下拉刷新..."
    /// 没有更多数据的文字
    var noMoreDataText: String = "已经到底啦~"
    /// 刷新状态的GIF图片数组
    var refreshingImages: [UIImage] = []
    /// 即将刷新状态的GIF图片数组
    var pullingImages: [UIImage] = []
    /// 闲置状态的GIF图片数组
    var idleImages: [UIImage] = []
    /// 刷新图片的尺寸
    var imageSize: CGSize = CGSize(width: 20, height: 20)
    /// 文字字体
    var textFont: UIFont = UIFont.systemFont(ofSize: 13)
    /// 文字颜色
    var textColor: UIColor = UIColor.gray
    /// 背景颜色
    var backgroundColor: UIColor = UIColor.clear
    
    /// 根据GIF图片名称前缀和数量设置刷新图片
    mutating func setRefreshingImages(prefix: String, count: Int, bundle: Bundle = Bundle.main) {
        var images: [UIImage] = []
        for i in 0..<count {
            if let image = UIImage(named: "\(prefix)\(i)", in: bundle, compatibleWith: nil) {
                images.append(image)
            }
        }
        self.refreshingImages = images
    }
    
    /// 使用图片名数组设置刷新图片
    mutating func setRefreshingImages(names: [String], bundle: Bundle = Bundle.main) {
        var images: [UIImage] = []
        for name in names {
            if let image = UIImage(named: name, in: bundle, compatibleWith: nil) {
                images.append(image)
            }
        }
        self.refreshingImages = images
    }
}

/// 自定义头部刷新 - 支持GIF图片
class GIFRefreshHeader: MJRefreshGifHeader {
    
    /// 创建头部刷新组件
    static func create(withHandler handler: @escaping () -> Void,
                       config: GIFRefreshConfig = GIFRefreshConfig()) -> MJRefreshHeader {
        
        let header = GIFRefreshHeader {
            handler()
        }
        
        // 设置状态文字
        header.setTitle(config.idleText, for: .idle)
        header.setTitle(config.pullingText, for: .pulling)
        header.setTitle(config.refreshingText, for: .refreshing)
        
        // 设置字体颜色
        header.stateLabel?.font = config.textFont
        header.stateLabel?.textColor = config.textColor
        header.lastUpdatedTimeLabel?.font = UIFont.systemFont(ofSize: 11)
        header.lastUpdatedTimeLabel?.textColor = config.textColor
        
        // 设置刷新图片
        if !config.idleImages.isEmpty {
            header.setImages(config.idleImages, for: .idle)
        }
        if !config.pullingImages.isEmpty {
            header.setImages(config.pullingImages, for: .pulling)
        }
        if !config.refreshingImages.isEmpty {
            header.setImages(config.refreshingImages, for: .refreshing)
        } else {
            // 使用默认菊花转圈
        }
        
        // 设置背景色
        header.backgroundColor = config.backgroundColor
        
        return header
    }
}

/// 自定义底部刷新 - 支持GIF图片
class GIFRefreshFooter: MJRefreshAutoGifFooter {
    
    /// 创建底部刷新组件
    static func create(withHandler handler: @escaping () -> Void,
                      config: GIFRefreshConfig = GIFRefreshConfig()) -> MJRefreshAutoFooter {
        
        let footer = GIFRefreshFooter {
            handler()
        }
        
        // 设置状态文字
        footer.setTitle(config.idleText, for: .idle)
        footer.setTitle(config.refreshingText, for: .refreshing)
        footer.setTitle(config.noMoreDataText, for: .noMoreData)
        
        // 设置字体颜色
        footer.stateLabel?.font = config.textFont
        footer.stateLabel?.textColor = config.textColor
        
        // 设置刷新图片
        if !config.idleImages.isEmpty {
            footer.setImages(config.idleImages, for: .idle)
            // 没有更多数据时也使用空闲状态的图片
            footer.setImages(config.idleImages, for: .noMoreData)
        }
        if !config.refreshingImages.isEmpty {
            footer.setImages(config.refreshingImages, for: .refreshing)
        } else {
            // 使用默认菊花转圈
        }

        // 确保没有更多数据时不显示动画
        footer.setImages([], for: .noMoreData)
        
        // 设置背景色
        footer.backgroundColor = config.backgroundColor
        
        return footer
    }
}

/// GIF刷新组件工具类
class GIFRefreshComponent {
    /// 创建头部刷新组件
    static func createHeader(withRefreshing refreshingBlock: @escaping () -> Void,
                            config: GIFRefreshConfig? = nil) -> MJRefreshHeader {
        let config = config ?? GIFRefreshConfig()
        return GIFRefreshHeader.create(withHandler: refreshingBlock, config: config)
    }
    
    /// 创建底部刷新组件
    static func createFooter(withRefreshing refreshingBlock: @escaping () -> Void,
                            config: GIFRefreshConfig? = nil) -> MJRefreshAutoFooter {
        let config = config ?? GIFRefreshConfig()
        return GIFRefreshFooter.create(withHandler: refreshingBlock, config: config)
    }
} 
