//
//  JXBaseViewController.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/5/26.
//

import JXSegmentedView
import UIKit

class JXBaseViewController: BaseViewController, JXSegmentedListContainerViewListDelegate {
    weak var superJXVC: BaseViewController?

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
    }

    func listView() -> UIView {
        return view
    }

    func subJXPushVC(vc: UIViewController, hiddenTabbar: Bool, animated: Bool) {
        if hiddenTabbar {
            superJXVC?.pushVcHiddenTabBar(vc, animated: animated)
        } else {
            superJXVC?.pushVc(vc, animated: animated)
        }
    }

    func subJXPresentVC(vc: UIViewController, animated: Bool, completion: (() -> Void)?) {
        superJXVC?.presentVc(vc, animated: animated, completion: completion)
    }
}
