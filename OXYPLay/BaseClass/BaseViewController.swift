//
//  BaseViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/22.
//

import UIKit
import Combine
import DZNEmptyDataSet

class BaseViewController: UIViewController {
    /// 存储订阅
    var cancellables = Set<AnyCancellable>()
   
    init() {
        super.init(nibName: nil, bundle: nil)
        modalPresentationStyle = .fullScreen
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        self.fd_interactivePopDisabled = false
        self.view.backgroundColor = color_F6F8F9
        setupEmptyDataSetNotification()
    }

    func configUI(){}
    func configLayout(){}
    func setupBindings(){}
    func logOut(){
        LoginManager.shared.logout()
    }

    /// 空数据状态重新加载回调（子类可重写）
    /// - Parameter scrollView: 触发重新加载的scrollView
    @objc open dynamic func onEmptyDataSetReload(_ scrollView: UIScrollView) {
        // 默认实现：如果scrollView有刷新控件，触发刷新
        if let refreshControl = scrollView.mj_header {
            refreshControl.beginRefreshing()
        }
    }
}
// MARK: - BaseViewController刷新扩展

extension BaseViewController {

    /// 便捷方法：为ScrollView设置刷新
    /// - Parameters:
    ///   - scrollView: 需要设置刷新的ScrollView
    ///   - viewModel: 关联的ViewModel（可选）
    ///   - enableHeader: 是否启用下拉刷新，默认true
    ///   - enableFooter: 是否启用上拉加载，默认true
    ///   - headerConfig: 头部刷新配置
    ///   - footerConfig: 底部刷新配置
    ///   - onHeaderRefresh: 下拉刷新回调（当viewModel为nil时使用）
    ///   - onFooterRefresh: 上拉加载回调（当viewModel为nil时使用）
    func setupRefresh(
        for scrollView: UIScrollView,
        with viewModel: BaseViewModel? = nil,
        enableHeader: Bool = true,
        enableFooter: Bool = true,
        headerConfig: GIFRefreshConfig? = nil,
        footerConfig: GIFRefreshConfig? = nil,
        onHeaderRefresh: (() -> Void)? = nil,
        onFooterRefresh: (() -> Void)? = nil
    ) {
        if let viewModel = viewModel {
            // 使用ViewModel的方式
            viewModel.setupRefresh(
                for: scrollView,
                enableHeader: enableHeader,
                enableFooter: enableFooter,
                headerConfig: headerConfig,
                footerConfig: footerConfig
            )
            .store(in: &cancellables)
        } else {
            // 使用回调的方式
            setupRefreshWithCallbacks(
                for: scrollView,
                enableHeader: enableHeader,
                enableFooter: enableFooter,
                headerConfig: headerConfig,
                footerConfig: footerConfig,
                onHeaderRefresh: onHeaderRefresh,
                onFooterRefresh: onFooterRefresh
            )
        }
    }

    /// 私有方法：使用回调方式设置刷新
    /// - Parameters:
    ///   - scrollView: 需要设置刷新的ScrollView
    ///   - enableHeader: 是否启用下拉刷新
    ///   - enableFooter: 是否启用上拉加载
    ///   - headerConfig: 头部刷新配置
    ///   - footerConfig: 底部刷新配置
    ///   - onHeaderRefresh: 下拉刷新回调
    ///   - onFooterRefresh: 上拉加载回调
    private func setupRefreshWithCallbacks(
        for scrollView: UIScrollView,
        enableHeader: Bool,
        enableFooter: Bool,
        headerConfig: GIFRefreshConfig?,
        footerConfig: GIFRefreshConfig?,
        onHeaderRefresh: (() -> Void)?,
        onFooterRefresh: (() -> Void)?
    ) {
        // 设置刷新控件
        if enableHeader {
            let header = GIFRefreshHeader.create(withHandler: {
                onHeaderRefresh?()
            }, config: headerConfig ?? RefreshImageHelper.createConfig(withStyle: .modern))
            scrollView.mj_header = header
        }

        if enableFooter {
            let footer = GIFRefreshFooter.create(withHandler: { 
                onFooterRefresh?()
            }, config: footerConfig ?? RefreshImageHelper.createConfig(withStyle: .modern))
            scrollView.mj_footer = footer
        }
    }

    /// 手动触发刷新
    /// - Parameter viewModel: 关联的ViewModel（可选）
    func triggerRefresh(with viewModel: BaseViewModel? = nil) {
        viewModel?.refreshData()
    }

    /// 手动触发加载更多
    /// - Parameter viewModel: 关联的ViewModel（可选）
    func triggerLoadMore(with viewModel: BaseViewModel? = nil) {
        viewModel?.loadMoreData()
    }

    /// 手动结束刷新状态
    /// - Parameter scrollView: 需要结束刷新的ScrollView
    func endRefreshing(for scrollView: UIScrollView) {
        scrollView.mj_header?.endRefreshing()
        scrollView.mj_footer?.endRefreshing()
    }

    /// 手动结束加载更多状态
    /// - Parameters:
    ///   - scrollView: 需要结束加载的ScrollView
    ///   - noMoreData: 是否没有更多数据
    func endLoadMore(for scrollView: UIScrollView, noMoreData: Bool = false) {
        if noMoreData {
            scrollView.mj_footer?.endRefreshingWithNoMoreData()
        } else {
            scrollView.mj_footer?.endRefreshing()
        }
    }
}

// MARK: - BaseViewController EmptyDataSet扩展

extension BaseViewController {

    /// 设置空数据状态通知监听
    private func setupEmptyDataSetNotification() {
        NotificationCenter.default.publisher(for: .emptyDataSetDidTapReload)
            .sink {[weak self] notification in
                guard let self = self else {return}
                guard let scrollView = notification.object as? UIScrollView else { return }
                if scrollView.isDescendant(of: self.view) {
                    self.onEmptyDataSetReload(scrollView)
                }
            }
            .store(in: &cancellables)
       
    }



    /// 便捷方法：为ScrollView设置空数据状态
    /// - Parameters:
    ///   - scrollView: 目标ScrollView
    ///   - type: 空数据状态类型
    func setEmptyDataSet(for scrollView: UIScrollView, type: EmptyDataSetType) {
        scrollView.setEmptyDataSet(type: type)
    }

    /// 便捷方法：隐藏ScrollView的空数据状态
    /// - Parameter scrollView: 目标ScrollView
    func hideEmptyDataSet(for scrollView: UIScrollView) {
        scrollView.hideEmptyDataSet()
    }
}


extension BaseViewController {
    func pushVc(_ viewController: UIViewController, animated: Bool, removeVc: UIViewController) {
        if !(viewController is UINavigationController) {
            navigationController?.pushViewController(viewController, animated: animated)
        }
        var array = navigationController?.viewControllers
        array?.removeAll { $0 == removeVc }
        navigationController?.viewControllers = array ?? [UIViewController]()
    }

    func pushVc(_ viewController: UIViewController, animated: Bool) {
        if !(viewController is UINavigationController) {
            navigationController?.pushViewController(viewController, animated: animated)
        }
    }

    func pushVcHiddenTabBar(_ viewController: UIViewController, animated: Bool) {
        hidesBottomBarWhenPushed = true
        pushVc(viewController, animated: animated)

        let vcCount = navigationController?.viewControllers.count ?? 0
        if vcCount == 2 {
            hidesBottomBarWhenPushed = false
        }
    }

    func pushVcHiddenTabBarNew(_ viewController: UIViewController, animated: Bool) {
        hidesBottomBarWhenPushed = true
        pushVc(viewController, animated: animated)
    }

    // MARK: - PerformSegue

    func performSegueWithIdentifierHiddenTabBar(_ identifier: String, sender: Any?) {
        hidesBottomBarWhenPushed = true
        performSegue(withIdentifier: identifier, sender: sender)

        let vcCount = navigationController?.viewControllers.count ?? 0
        if vcCount == 2 {
            hidesBottomBarWhenPushed = false
        }
    }

    func performSegueWithIdentifierHiddenTabBar(_ identifier: String) {
        performSegueWithIdentifierHiddenTabBar(identifier, sender: nil)
    }

    // MARK: - presentViewController

    func presentVc(_ viewController: UIViewController, animated: Bool, completion: (() -> Void)?) {
        navigationController?.present(viewController, animated: animated, completion: completion)
    }

    // MARK: - 根据Sb中的Identifier推出VC

    func performSbWithIdentifierHiddenTabBar(_ identifier: String) {
        performSbWithIdentifierHiddenTabBar(identifier, sender: nil)
    }

    func performSbWithIdentifierHiddenTabBar(_ identifier: String, sender: AnyObject?) {
        hidesBottomBarWhenPushed = true
        tabBarController?.tabBar.isHidden = true
        performSegue(withIdentifier: identifier, sender: sender)

        let vcCount = navigationController?.viewControllers.count ?? 0
        if vcCount == 2 {
            hidesBottomBarWhenPushed = false
        }
    }
    
}
// MARK: - SlideMenuViewDelegate
extension BaseViewController: SlideMenuControllerDelegate {
    func slideMenuDidSelectItem(_ menuItem: SlideMenuController.MenuItem, title: String) {
        var pushVc:BaseViewController?
        switch menuItem {
        case .addFriend:
            pushVc = MineaAddFriendController()
        case .shoppingCart:
            pushVc = ProductCartController()
        case .purchased:
            let vc = ProductSellingPurchasedController()
            vc.isSelling = false
            pushVc = vc
        case .sold:
            pushVc = ProductSellingPurchasedController()
        case .address:
            pushVc = SelectAdressController()
        case .settings:
            pushVc = MineSettingController()
        case .customerService:
            pushVc = CustomerServiceController()
        case .myComments: break
        case .browsingHistory: break
        }
        guard let pushVc = pushVc else { return }
        pushVcHiddenTabBar(pushVc, animated: true)
    }
    
    @objc func showSlideMenu() {
        SlideMenuController.show(from: self, delegate: self)
    }


    func slideMenuDidTapLogout() {
        logOut()
    }
}
extension BaseViewController {
    @objc func showQrCode() {
        let qrScannerVC = QRScannerViewController()
        pushVcHiddenTabBar(qrScannerVC, animated: true)
    }
}
