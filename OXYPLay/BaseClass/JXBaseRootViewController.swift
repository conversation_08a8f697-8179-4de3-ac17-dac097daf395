//
//  JXBaseRootViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import UIKit
import JXSegmentedView

class JXBaseRootViewController: BaseViewController {
    
    // MARK: - Properties
    var titles: [String] = []
    var viewControllers: [UIViewController & JXSegmentedListContainerViewListDelegate] = []
    
    // 是否隐藏导航栏
    var shouldHideNavigationBar: Bool = true
    var isNavSegementView: Bool = false

    // 分段控制器配置
    var segmentedViewHeight: CGFloat = 40
    var segmentedViewTop: CGFloat = ScreenInfo.totalNavBarHeight
    var segmentedViewBackgroundColor: UIColor = .white
    var segmentedViewCornerRadius: CGFloat = 16
    var segmentedItemWidth: CGFloat = 28
    var segmentedItemSpacing: CGFloat = 20
    var segmentedViewContentEdgeInsetLeft: CGFloat = 23.5
    var segmentedViewContentEdgeInsetRight: CGFloat = 23.5
    var segmentedViewMaskedCorners: CACornerMask = [.layerMaxXMaxYCorner, .layerMinXMaxYCorner]
    
    // 指示器配置
    var indicatorType: IndicatorType = .line
    var indicatorColor: UIColor = UIColor(hexString: "2A72FF", transparency: 1)!
    var indicatorWidth: CGFloat = 10
    var indicatorHeight: CGFloat = 3
    
    // 标题配置
    var titleSelectedColor: UIColor = UIColor(hexString: "2A72FF", transparency: 1)!
    var titleNormalColor: UIColor = UIColor(hexString: "2B2C2F", transparency: 0.8)!
    var titleSelectedFont: UIFont = .systemFont(ofSize: 14, weight: .regular)
    var titleNormalFont: UIFont = .systemFont(ofSize: 14, weight: .regular)
    var isTitleColorGradientEnabled: Bool = true
    var isTitleZoomEnabled: Bool = false
    
    // 默认选中的索引
    var defaultSelectedIndex: Int = 0
    
    // MARK: - Enum
    enum IndicatorType {
        case line
        case background
    }
    
    // MARK: - Lazy Properties
    lazy var segmentedDataSource: JXSegmentedTitleDataSource = {
        let dataSource = JXSegmentedTitleDataSource()
        dataSource.titles = titles
        dataSource.titleSelectedColor = titleSelectedColor
        dataSource.titleNormalColor = titleNormalColor
        dataSource.titleNormalFont = titleNormalFont
        dataSource.titleSelectedFont = titleSelectedFont
        dataSource.isTitleColorGradientEnabled = isTitleColorGradientEnabled
        dataSource.isTitleZoomEnabled = isTitleZoomEnabled
        dataSource.itemWidth = segmentedItemWidth
        dataSource.itemSpacing = segmentedItemSpacing
        return dataSource
    }()
    
    lazy var segmentedView: JXSegmentedView = {
        let view = JXSegmentedView()
        view.backgroundColor = segmentedViewBackgroundColor
        view.contentEdgeInsetLeft = segmentedViewContentEdgeInsetLeft
        view.contentEdgeInsetRight = segmentedViewContentEdgeInsetRight
        view.layer.cornerRadius = segmentedViewCornerRadius
        view.layer.maskedCorners = segmentedViewMaskedCorners
        view.masksToBounds = true
        view.isContentScrollViewClickTransitionAnimationEnabled = false
        
        // 创建指示器
        switch indicatorType {
        case .line:
            let lineView = JXSegmentedIndicatorLineView()
            lineView.indicatorColor = indicatorColor
            lineView.indicatorWidth = indicatorWidth
            lineView.indicatorHeight = indicatorHeight
            view.indicators = [lineView]
        case .background:
            let indicator = JXSegmentedIndicatorBackgroundView()
            indicator.indicatorWidthIncrement = indicatorWidth
            indicator.indicatorHeight = indicatorHeight
            indicator.indicatorColor = indicatorColor
            view.indicators = [indicator]
        }
        
        view.listContainer = listContainerView
        view.dataSource = segmentedDataSource
        view.delegate = self
        view.defaultSelectedIndex = defaultSelectedIndex
        return view
    }()
    
    lazy var listContainerView: JXSegmentedListContainerView = {
        return JXSegmentedListContainerView(dataSource: self)
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupSegmentUI()
    }
    
    // MARK: - UI Setup
    func setupSegmentUI() {
        fd_prefersNavigationBarHidden = shouldHideNavigationBar
        if isNavSegementView{
            self.navigationItem.titleView = segmentedView
            view.addSubview(listContainerView)
            listContainerView.snp.makeConstraints { make in
                make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
                make.left.right.bottom.equalToSuperview()
            }
        }else{
            view.addSubview(segmentedView)
            view.addSubview(listContainerView)
            
            segmentedView.snp.makeConstraints { make in
                make.top.equalTo(segmentedViewTop)
                make.left.right.equalToSuperview()
                make.height.equalTo(segmentedViewHeight)
            }
            
            listContainerView.snp.makeConstraints { make in
                make.top.equalTo(segmentedView.snp.bottom)
                make.left.right.bottom.equalToSuperview()
            }
        }
        
    }
    
    // MARK: - Public Methods
    func reloadData() {
        segmentedDataSource.titles = titles
        segmentedView.reloadData()
    }
}

// MARK: - JXSegmentedViewDelegate, JXSegmentedListContainerViewDataSource
extension JXBaseRootViewController: JXSegmentedViewDelegate, JXSegmentedListContainerViewDataSource {
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        if let dotDataSource = segmentedDataSource as? JXSegmentedDotDataSource {
            dotDataSource.dotStates[index] = false
            segmentedView.reloadItem(at: index)
        }
        navigationController?.interactivePopGestureRecognizer?.isEnabled = (segmentedView.selectedIndex == 0)
    }
    
    func numberOfLists(in _: JXSegmentedListContainerView) -> Int {
        return segmentedDataSource.titles.count
    }
    
    func listContainerView(_: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        return viewControllers[index]
    }
} 
