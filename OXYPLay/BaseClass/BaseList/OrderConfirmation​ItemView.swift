//
//  OrderConfirmation​ItemView.swift
//  OXYPLay
//
//  Created by <PERSON>h<PERSON> on 2025/7/17.
//

class OrderConfirmation​ItemView: ListItemView {

    // MARK: - UI组件
    weak var delegate: ListSelectItemViewDelegate?

    lazy var orderCommonView = OrderCommonView()
    lazy var realPriceTitleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textColor = UIColor(hexString: "#3D3E40", transparency: 1)
        $0.text = "到手价"
    }
    lazy var realPriceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12,weight: .medium)
        $0.textColor = UIColor(hexString: "#F12D24", transparency: 1)
    }

    lazy var arrowImageView = UIImageView().then {
        $0.image = UIImage(named: "baselist_arrow")
    }
    lazy var line1 = UIView().then {
        $0.backgroundColor = UIColor(hexString: "#B2C2F", transparency: 0.08)
    }
    lazy var line2 = UIView().then {
        $0.backgroundColor = UIColor(hexString: "#B2C2F", transparency: 0.08)
    }
    lazy var shippingCostLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .medium)
        $0.textColor = UIColor(hexString: "#3D3E40", transparency: 1)
        $0.text = "运费"
    }
    lazy var shippingCostNumberLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = UIColor(hexString: "#3D3E40", transparency: 1)
    }
    lazy var ticketBackView = UIView()
    lazy var ticketLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .medium)
        $0.textColor = UIColor(hexString: "#3D3E40", transparency: 1)
        $0.text = "平台优惠劵"
    }
    
    lazy var ticketNumberLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .regular)
        $0.textColor = UIColor(hexString: "#FF0000", transparency: 1)
    }
    // MARK: - 初始化方法

    override func configUI() {
        backgroundColor = .white
        addSubview(orderCommonView)
        addSubview(line1)
        addSubview(line2)
        addSubview(realPriceTitleLabel)
        addSubview(realPriceLabel)
        addSubview(shippingCostLabel)
        addSubview(shippingCostNumberLabel)
        addSubview(ticketBackView)
        ticketBackView.addSubview(ticketLabel)
        ticketBackView.addSubview(ticketNumberLabel)
        ticketBackView.addSubview(arrowImageView)
    }

    override func configLayout() {
      
        orderCommonView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(12)
            make.right.equalTo(-12)
        }
        line1.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(orderCommonView.snp.bottom).offset(12)
            make.right.equalTo(-12)
            make.height.equalTo(0.5)
        }
        shippingCostLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(line1.snp.bottom).offset(12)
        }
        shippingCostNumberLabel.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.top.equalTo(line1.snp.bottom).offset(12)
        }
        ticketBackView.snp.makeConstraints { make in
            make.top.equalTo(shippingCostLabel.snp.bottom).offset(0)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(38)
        }
        ticketLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview()
        }
        ticketNumberLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalTo(arrowImageView.snp.left).offset(-8)
        }
        arrowImageView.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        line2.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(ticketBackView.snp.bottom)
            make.right.equalTo(-12)
            make.height.equalTo(0.5)
        }
        realPriceTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(line2.snp.bottom).offset(12)
            make.right.equalTo(self.snp.centerX).offset(-2)
            make.bottom.equalTo(-12)
        }
    
        realPriceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(realPriceTitleLabel)
            make.left.equalTo(self.snp.centerX).offset(2)
        }
     
        // 添加点击事件
        let tapGesture = UITapGestureRecognizer()
        tapGesture.tapPublisher
            .sink { [weak self] _ in
                guard let self = self,let config = self.itemConfig else { return }
                self.delegate?.listItemViewClick(self, config: config)
            }
            .store(in: &cancellables)
        
        ticketBackView.addGestureRecognizer(tapGesture)

    }
    override func configure(with config: ListItemConfig) {
        super.configure(with: config)

        if let model = config.data as? HomeDetailModel{
            orderCommonView.configModel(model: model)
            shippingCostNumberLabel.text = model.postage_fee.formattedPrice

            // 计算最终价格：商品价格 + 运费 - 优惠券
            let finalPrice = calculateFinalPrice(
                itemPrice: model.price,
                postageFee: model.postage_fee,
                couponAmount: model.coupon.getCurrentSelectedPrice()
            )

            if model.coupon.available.isEmpty{
                ticketNumberLabel.text =  "0张优惠卷可用"
                ticketNumberLabel.textColor = color_3D3E40
            }else{
                ticketNumberLabel.text =  "-\(model.coupon.getCurrentSelectedPrice().formattedPrice)"
                ticketNumberLabel.textColor = UIColor(hexString: "#FF0000", transparency: 1)
            }
            realPriceLabel.text = finalPrice.formattedPrice
        }

        if let model = config.data as? ProductDetailModel{
            orderCommonView.configModel(model: model)
            shippingCostNumberLabel.text = model.product.postage_fee.formattedPrice

            // 计算最终价格：商品价格 + 运费 - 优惠券
            let finalPrice = calculateFinalPrice(
                itemPrice: model.getcurrentQuantityPrice(),
                postageFee: model.product.postage_fee,
                couponAmount: model.coupon.getCurrentSelectedPrice()
            )

            if model.coupon.available.isEmpty{
                ticketNumberLabel.text =  "0张优惠卷可用"
                ticketNumberLabel.textColor = color_3D3E40
            }else{
                ticketNumberLabel.text =  "-\(model.coupon.getCurrentSelectedPrice().formattedPrice)"
                ticketNumberLabel.textColor = UIColor(hexString: "#FF0000", transparency: 1)
            }
            realPriceLabel.text = finalPrice.formattedPrice
        }
    }

    /// 计算最终价格：商品价格 + 运费 - 优惠券
    /// - Parameters:
    ///   - itemPrice: 商品价格
    ///   - postageFee: 运费
    ///   - couponAmount: 优惠券金额
    /// - Returns: 最终价格字符串
    private func calculateFinalPrice(itemPrice: String, postageFee: String, couponAmount: String) -> String {
        let itemPriceFloat = Float(itemPrice) ?? 0.0
        let postageFeeFloat = Float(postageFee) ?? 0.0
        let couponAmountFloat = Float(couponAmount) ?? 0.0

        // 最终价格 = 商品价格 + 运费 - 优惠券金额
        let finalPrice = itemPriceFloat + postageFeeFloat - couponAmountFloat

        // 确保最终价格不为负数
        let result = max(finalPrice, 0.0)

        return String(format: "%.2f", result)
    }
}

class OrderCommonView:BaseView{
    lazy var iconImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFit
        $0.layer.cornerRadius = 12
        $0.backgroundColor = .random
        $0.masksToBounds = true
    }

    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16,weight: .medium)
        $0.textColor = color_3D3E40
        $0.numberOfLines = 1
    }
    lazy var centerStackView = UIStackView().then{
        $0.axis = .horizontal
        $0.spacing = 8
    }

    lazy var locationButton = BaseButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.setTitleColor( UIColor(hexString: "2B2C2F", transparency: 0.64), for: .normal)
        $0.setImage(UIImage(named: "baselist_location"), for: .normal)
        $0.spacing = 0
    }
    lazy var styleLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.64)
        $0.setHorizontalPadding(4)
        $0.setVerticalPadding(4)
        $0.backgroundColor = UIColor(hexString: "788092", transparency: 0.08)
        $0.text = "xxx,款式"
        $0.textAlignment = .center
        $0.cornerRadius = 6
    }
    lazy var numberLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.64)
        $0.setHorizontalPadding(4)
        $0.setVerticalPadding(4)
        $0.backgroundColor = UIColor(hexString: "788092", transparency: 0.08)
        $0.text = "数量*1"
        $0.textAlignment = .center
        $0.cornerRadius = 6
    }
    lazy var priceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12,weight: .medium)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 1)
        $0.text = "100".formattedPrice

    }
    lazy var originalPriceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.48)
        $0.text = "100".formattedPrice
    }
    lazy var shippingMethodLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 12,weight: .medium)
        $0.textColor = UIColor(hexString: "#FF0000", transparency: 1)
        $0.backgroundColor = UIColor(hexString: "#FF0000", transparency: 0.08)
        $0.cornerRadius = 4
        $0.masksToBounds = true
        $0.setHorizontalPadding(4)

    }
 
    func configModel(model: ProductOrderModel, item: OrderItem) {
        // 加载商品图片
        if let url = URL(string: item.thumb_image) {
            iconImageView.kf.setImage(with: url, placeholder: UIImage(systemName: "photo"))
        }

        // 设置商品信息
        titleLabel.text = item.title
        priceLabel.text = String(format: "%.2f", item.total_price).formattedPrice
        // 设置位置信息（仅服务帖有）
        if item.item_type == 2 {
            locationButton.setTitle(item.location, for: .normal)
            locationButton.isHidden = false
            numberLabel.isHidden = true
            styleLabel.isHidden = true
        } else {
            // 设置数量
            styleLabel.text = item.spec_value_text
            numberLabel.text = "数量\(item.quantity)"
            numberLabel.isHidden = false
            locationButton.isHidden = true
            styleLabel.isHidden = false
        }

        // 设置运费信息
        shippingMethodLabel.text = item.postage_type_text

        originalPriceLabel.isHidden = true
    }
    func configModel(model:HomeDetailModel){
        if let url = URL(string: model.img) {
            iconImageView.kf.setImage(with: url, placeholder: UIImage(systemName: "person.circle"))
        }
        styleLabel.isHidden = true
        numberLabel.isHidden = true
        originalPriceLabel.isHidden = true
        locationButton.setTitle(model.location, for: .normal)
        priceLabel.text = model.price.formattedPrice
        titleLabel.text = model.title
        shippingMethodLabel.text = model.postage_type_text
    }
    func configModel(model: ProductDetailModel) {
        // 加载商品图片
        if let url = URL(string: model.getImage()) {
            iconImageView.kf.setImage(with: url, placeholder: UIImage(systemName: "photo"))
        }

        // 设置商品信息
        titleLabel.text = model.product.name
        priceLabel.text = model.getcurrentQuantityPrice().formattedPrice
       
        styleLabel.text = model.getCurrentSelectedSpecNames()
        numberLabel.text = "数量\(model.currentQuantity)"
        numberLabel.isHidden = false
        locationButton.isHidden = true
        styleLabel.isHidden = false
        // 设置运费信息
        shippingMethodLabel.text = model.product.postage_type_text

        originalPriceLabel.isHidden = true
    }
    

    /// 配置购物车商品数据
    func configModel(model: CartItem) {
        
        // 加载商品图片
        if let url = URL(string: model.thumb_image) {
            iconImageView.kf.setImage(with: url, placeholder: UIImage(systemName: "photo"))
        }

        // 设置商品信息
        titleLabel.text = model.name
        priceLabel.text = String(format: "%.2f", model.price).formattedPrice

        shippingMethodLabel.text = model.postage_type_text
        
        styleLabel.text = model.spec_value_txt
        numberLabel.isHidden = true
        locationButton.isHidden = true
        originalPriceLabel.isHidden = true

    }
    func configModel(model:OrderDetailItem,isProduct:Bool){
        // 加载商品图片
        if let url = URL(string: model.thumb_image) {
            iconImageView.kf.setImage(with: url, placeholder: UIImage(systemName: "photo"))
        }

        // 设置商品信息
        titleLabel.text = model.title
        priceLabel.text = String(format: "%.2f", model.price).formattedPrice
       
        styleLabel.text = model.spec_value_text
        numberLabel.text = "数量\(model.quantity)"
        
        locationButton.setTitle(model.location, for: .normal)

        // 设置运费信息
        shippingMethodLabel.text = model.postage_type_text

        originalPriceLabel.isHidden = true
        numberLabel.isHidden = !isProduct
        styleLabel.isHidden = !isProduct
        locationButton.isHidden = isProduct
        
        
    }
    override func configUI() {
        addSubview(iconImageView)
        addSubview(titleLabel)
        addSubview(priceLabel)
        addSubview(originalPriceLabel)
        addSubview(shippingMethodLabel)
        addSubview(centerStackView)
        centerStackView.addArrangedSubview(locationButton)
        centerStackView.addArrangedSubview(styleLabel)
        centerStackView.addArrangedSubview(numberLabel)

    }
    override func configLayout() {
        iconImageView.snp.makeConstraints { make in
            make.top.left.equalTo(0)
            make.width.height.equalTo(70)
            make.bottom.equalTo(0)
        }
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(10)
            make.top.equalTo(iconImageView)
        }
        shippingMethodLabel.snp.makeConstraints { make in
            make.right.equalTo(0)
            make.centerY.equalTo(titleLabel)
            make.height.equalTo(19)
            make.left.greaterThanOrEqualTo(titleLabel.snp.right).offset(0)
        }
        centerStackView.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.centerY.equalTo(iconImageView)
            make.height.equalTo(19)
        }
        priceLabel.snp.makeConstraints { make in
            make.bottom.equalTo(iconImageView.snp.bottom)
            make.left.equalTo(titleLabel)
        }
        originalPriceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(priceLabel)
            make.left.equalTo(priceLabel.snp.right).offset(8)
        }
    }
}
