import UIKit

/// 无限循环轮播视图协议
protocol InfiniteBannerViewDataSource: AnyObject {
    /// 返回轮播项目的数量
    func numberOfItems(in bannerView: InfiniteBannerView) -> Int
    
    /// 为指定索引创建并返回一个单元格
    func bannerView(_ bannerView: InfiniteBannerView, cellForItemAt index: Int) -> UICollectionViewCell
}

/// 无限循环轮播视图代理
protocol InfiniteBannerViewDelegate: AnyObject {
    /// 当轮播视图滚动到新的索引时调用
    func bannerView(_ bannerView: InfiniteBannerView, didScrollFrom fromIndex: Int, to toIndex: Int)
    
    /// 当用户选择一个轮播项目时调用
    func bannerView(_ bannerView: InfiniteBannerView, didSelectItemAt index: Int)
}

/// 无限循环轮播视图，支持自动滚动、无限循环和自定义布局
class InfiniteBannerView: BaseView {
    // MARK: - 公开属性
    
    /// 数据源
    weak var dataSource: InfiniteBannerViewDataSource?
    
    /// 代理
    weak var delegate: InfiniteBannerViewDelegate?
    
    /// 是否启用无限循环
    var isInfiniteLoop: Bool = true {
        didSet {
            reloadData()
        }
    }
    
    /// 自动滚动间隔，设置为0时禁用自动滚动
    var autoScrollInterval: TimeInterval = 0 {
        didSet {
            setupAutoScroll()
        }
    }
    
    /// 当前页面索引
    private(set) var currentIndex: Int = 0
    
    /// 滚动方向
    var scrollDirection: UICollectionView.ScrollDirection = .horizontal {
        didSet {
            flowLayout.scrollDirection = scrollDirection
        }
    }
    
    /// 是否显示页面指示器
    var showsPageControl: Bool = true
    
    // MARK: - 私有属性
    
    private var realItemCount: Int = 0
    private var timer: Timer?
    private var isUserScrolling: Bool = false
    private var initialContentOffset: CGPoint = .zero
    
    private lazy var flowLayout: UICollectionViewFlowLayout = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = scrollDirection
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        return layout
    }()
    
    private lazy var collectionView: UICollectionView = {
        let cv = UICollectionView(frame: .zero, collectionViewLayout: flowLayout)
        cv.backgroundColor = .clear
        cv.showsHorizontalScrollIndicator = false
        cv.showsVerticalScrollIndicator = false
        cv.isPagingEnabled = true
        cv.delegate = self
        cv.dataSource = self
        return cv
    }()
    
    // MARK: - 初始化
    
    override func configUI() {
        addSubview(collectionView)
    }
    
    override func configLayout() {
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - 公开方法
    
    /// 注册可重用的单元格
    func register(_ cellClass: AnyClass?, forCellWithReuseIdentifier identifier: String) {
        collectionView.register(cellClass, forCellWithReuseIdentifier: identifier)
    }
    
    /// 注册可重用的单元格（通过Nib）
    func register(_ nib: UINib?, forCellWithReuseIdentifier identifier: String) {
        collectionView.register(nib, forCellWithReuseIdentifier: identifier)
    }
    
    /// 获取可重用的单元格
    func dequeueReusableCell(withReuseIdentifier identifier: String, for index: Int) -> UICollectionViewCell {
        return collectionView.dequeueReusableCell(withReuseIdentifier: identifier, for: IndexPath(item: index, section: 0))
    }
    
    /// 重新加载数据
    func reloadData() {
        // 停止定时器
        stopTimer()
        
        // 获取真实的项目数量
        realItemCount = dataSource?.numberOfItems(in: self) ?? 0
        
        // 重新加载集合视图
        collectionView.reloadData()
        
        // 如果有项目，滚动到中间位置
        if realItemCount > 0 && isInfiniteLoop {
            let targetIndex = realItemCount * 100 // 设置一个足够大的数字，使得初始位置在中间
            collectionView.scrollToItem(at: IndexPath(item: targetIndex, section: 0), at: .centeredHorizontally, animated: false)
            currentIndex = 0
        }
        
        // 重新设置定时器
        setupAutoScroll()
    }
    
    /// 滚动到指定索引
    func scrollToItem(at index: Int, animated: Bool) {
        guard realItemCount > 0 else { return }
        
        var targetIndex = index
        if isInfiniteLoop {
            // 计算当前可见项的基础索引（去除循环因子）
            let currentBase = (collectionView.indexPathsForVisibleItems.first?.item ?? 0) / realItemCount * realItemCount
            targetIndex = currentBase + index
        }
        
        collectionView.scrollToItem(at: IndexPath(item: targetIndex, section: 0), at: .centeredHorizontally, animated: animated)
        
        // 如果不是动画滚动，立即更新索引
        if !animated {
            currentIndex = index
        }
    }
    
    // MARK: - 私有方法
    
    private func setupAutoScroll() {
        stopTimer()
        
        guard autoScrollInterval > 0, realItemCount > 1 else { return }
        
        timer = Timer.scheduledTimer(timeInterval: autoScrollInterval, target: self, selector: #selector(automaticScroll), userInfo: nil, repeats: true)
        RunLoop.main.add(timer!, forMode: .common)
    }
    
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    @objc private func automaticScroll() {
        guard !isUserScrolling, realItemCount > 0 else { return }
        
        let currentItem = collectionView.indexPathsForVisibleItems.sorted { $0.item < $1.item }.first?.item ?? 0
        let targetIndex = currentItem + 1
        collectionView.scrollToItem(at: IndexPath(item: targetIndex, section: 0), at: .centeredHorizontally, animated: true)
    }
    
    private func convertItemIndex(_ indexPath: IndexPath) -> Int {
        guard realItemCount > 0 else { return 0 }
        return indexPath.item % realItemCount
    }
    
    private func updateCurrentIndex() {
        let visibleItems = collectionView.indexPathsForVisibleItems.sorted(by: { $0.item < $1.item })
        guard let centerIndexPath = centerVisibleIndexPath() else { return }
        
        let newIndex = convertItemIndex(centerIndexPath)
        
        if newIndex != currentIndex {
            delegate?.bannerView(self, didScrollFrom: currentIndex, to: newIndex)
            currentIndex = newIndex
        }
    }
    
    private func centerVisibleIndexPath() -> IndexPath? {
        // 获取可见的索引路径
        let visibleItems = collectionView.indexPathsForVisibleItems.sorted { $0.item < $1.item }
        guard !visibleItems.isEmpty else { return nil }
        
        // 如果只有一个可见项，直接返回
        if visibleItems.count == 1 {
            return visibleItems[0]
        }
        
        // 计算集合视图的中心点
        let centerX = collectionView.contentOffset.x + collectionView.bounds.width / 2
        
        // 找到最接近中心的单元格
        var closestIndexPath = visibleItems[0]
        var minDistance: CGFloat = .greatestFiniteMagnitude
        
        for indexPath in visibleItems {
            if let attributes = collectionView.layoutAttributesForItem(at: indexPath) {
                let cellCenterX = attributes.center.x
                let distance = abs(cellCenterX - centerX)
                
                if distance < minDistance {
                    minDistance = distance
                    closestIndexPath = indexPath
                }
            }
        }
        
        return closestIndexPath
    }
    
    // 重置到中间区域，避免滚动到边缘
    private func resetToMiddleIfNeeded() {
        guard isInfiniteLoop, realItemCount > 0 else { return }
        
        let currentItem = collectionView.indexPathsForVisibleItems.sorted { $0.item < $1.item }.first?.item ?? 0
        
        // 如果滚动到了边缘区域，重置到中间区域
        if currentItem < realItemCount * 50 || currentItem >= realItemCount * 150 {
            let targetIndex = realItemCount * 100 + currentIndex
            collectionView.scrollToItem(at: IndexPath(item: targetIndex, section: 0), at: .centeredHorizontally, animated: false)
        }
    }
}

// MARK: - UICollectionViewDataSource
extension InfiniteBannerView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if realItemCount == 0 {
            return 0
        }
        
        return isInfiniteLoop ? realItemCount * 200 : realItemCount // 使用一个足够大的数字来模拟无限循环
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let index = convertItemIndex(indexPath)
        return dataSource?.bannerView(self, cellForItemAt: index) ?? UICollectionViewCell()
    }
}

// MARK: - UICollectionViewDelegate
extension InfiniteBannerView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let index = convertItemIndex(indexPath)
        delegate?.bannerView(self, didSelectItemAt: index)
    }
    
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        isUserScrolling = true
        initialContentOffset = scrollView.contentOffset
        stopTimer()
    }
    
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        isUserScrolling = false
        
        if !decelerate {
            updateCurrentIndex()
            resetToMiddleIfNeeded()
            setupAutoScroll()
        }
    }
    
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        updateCurrentIndex()
        resetToMiddleIfNeeded()
        setupAutoScroll()
    }
    
    func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
        updateCurrentIndex()
        resetToMiddleIfNeeded()
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension InfiniteBannerView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return bounds.size
    }
} 
