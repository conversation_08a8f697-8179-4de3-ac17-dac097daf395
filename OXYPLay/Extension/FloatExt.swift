//
//  FloatExt.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/25.
//

import Foundation

extension Float {
    /// 格式化为价格字符串
    var formattedPrice: String {
        return String(format: "¥%.2f", self)
    }
    
    /// 格式化为价格字符串（无小数点）
    var formattedPriceNoDecimal: String {
        if self == _math.floor(self) {
            return String(format: "¥%.0f", self)
        } else {
            return String(format: "¥%.2f", self)
        }
    }
}

extension Double {
    /// 格式化为价格字符串
    var formattedPrice: String {
        return String(format: "¥%.2f", self)
    }
    
    /// 格式化为价格字符串（无小数点）
    var formattedPriceNoDecimal: String {
        if self == _math.floor(self) {
            return String(format: "¥%.0f", self)
        } else {
            return String(format: "¥%.2f", self)
        }
    }
}
