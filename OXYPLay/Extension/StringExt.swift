//
//  StringExt.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/6.
//
extension String {
    var isValidChinesePhoneNumber: Bool {
        // 正则规则：11位数字，1开头，第二位为3-9
        let pattern = "^1[3-9]\\d{9}$"
        return range(of: pattern, options: .regularExpression) != nil
    }
    // 使用SwiftDate库格式化时间为"几分钟前"、"几小时前"等
    var formatTimeAgo:String{
        // 设置默认区域为中文
        SwiftDate.defaultRegion = Region(calendar: Calendars.gregorian, zone: Zones.current, locale: Locales.chinese)
        
        // 解析日期字符串
        guard let date = self.toDate("yyyy-MM-dd HH:mm:ss") else {
            return self
        }
        
        // 获取当前时间
        let now = Date()
        
        // 创建相对日期格式化器并设置区域为中文
        let formatter = RelativeDateTimeFormatter()
        formatter.dateTimeStyle = .named
        formatter.unitsStyle = .full
        formatter.locale = Locale(identifier: "zh_CN") // 明确设置为中文区域
        
        // 获取相对时间
        return formatter.localizedString(for: date.date, relativeTo: now)
    }
    var formattedPrice: String {
        return "¥\(self)"
    }
    // MARK: - Private Methods
    
    /// 手机号码脱敏处理
    var maskPhoneNumber:String {
        guard self.count >= 11 else { return self }
        
        let startIndex = self.index(self.startIndex, offsetBy: 3)
        let endIndex = self.index(self.startIndex, offsetBy: 7)
        let maskedPart = String(repeating: "*", count: 4)
        
        let prefix = String(self[..<startIndex])
        let suffix = String(self[endIndex...])
        
        return "\(prefix)\(maskedPart)\(suffix)"
    }
}
