//
//  KingfisherExt.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/25.
//

import Kingfisher
import UIKit

// MARK: - UIImageView 扩展，静默处理错误

extension UIImageView {
    
    /// 静默加载图片，不打印错误信息
    /// - Parameters:
    ///   - url: 图片URL
    ///   - placeholder: 占位图
    ///   - completion: 完成回调
    func setImageSilently(
        with url: URL?,
        placeholder: UIImage? = UIImage(named: "placeholder_image"),
        completion: ((Result<RetrieveImageResult, KingfisherError>) -> Void)? = nil
    ) {
        guard let url = url else {
            self.image = placeholder
            return
        }
        
        let options: KingfisherOptionsInfo = [
            .transition(.fade(0.2)),
            .cacheOriginalImage,
            .backgroundDecode,
            .onFailureImage(placeholder)
        ]
        
        self.kf.setImage(
            with: url,
            placeholder: placeholder,
            options: options
        ) { result in
            // 静默处理结果，不打印错误
            switch result {
            case .success(let value):
                // 成功时可以执行一些操作，但不打印日志
                completion?(.success(value))
            case .failure(let error):
                // 失败时静默处理，不打印错误
                completion?(.failure(error))
            }
        }
    }
    
    /// 静默加载图片（字符串URL版本）
    /// - Parameters:
    ///   - urlString: 图片URL字符串
    ///   - placeholder: 占位图
    ///   - completion: 完成回调
    func setImageSilently(
        with urlString: String?,
        placeholder: UIImage? = UIImage(named: "placeholder_image"),
        completion: ((Result<RetrieveImageResult, KingfisherError>) -> Void)? = nil
    ) {
        guard let urlString = urlString,
              let url = URL(string: urlString) else {
            self.image = placeholder
            return
        }
        
        setImageSilently(with: url, placeholder: placeholder, completion: completion)
    }
}

// MARK: - Kingfisher 错误处理扩展

extension KingfisherError {
    
    /// 是否为网络错误
    var isNetworkError: Bool {
        switch self {
        case .requestError, .responseError:
            return true
        default:
            return false
        }
    }
    
    /// 是否为缓存错误
    var isCacheError: Bool {
        switch self {
        case .cacheError:
            return true
        default:
            return false
        }
    }
    
    /// 获取用户友好的错误描述
    var userFriendlyDescription: String {
        switch self {
        case .requestError:
            return "网络请求失败"
        case .responseError:
            return "图片加载失败"
        case .cacheError:
            return "缓存错误"
        case .processorError:
            return "图片处理失败"
        case .imageSettingError:
            return "图片设置失败"
        }
    }
}

// MARK: - 全局 Kingfisher 配置

extension KingfisherManager {
    
    /// 配置静默模式
    static func configureSilentMode() {
        // 设置全局默认选项
        KingfisherManager.shared.defaultOptions = [
            .transition(.fade(0.2)),
            .cacheOriginalImage,
            .backgroundDecode,
            .onFailureImage(UIImage(named: "placeholder_image")),
            .keepCurrentImageWhileLoading // 加载时保持当前图片
        ]
        
        // 配置缓存
        let cache = ImageCache.default
        cache.memoryStorage.config.totalCostLimit = 50 * 1024 * 1024 // 50MB
        cache.diskStorage.config.sizeLimit = 200 * 1024 * 1024 // 200MB
        
        // 配置下载器
        let downloader = ImageDownloader.default
        downloader.downloadTimeout = 15.0
        downloader.sessionConfiguration.urlCache = URLCache(
            memoryCapacity: 20 * 1024 * 1024,
            diskCapacity: 100 * 1024 * 1024,
            diskPath: nil
        )
    }
}
