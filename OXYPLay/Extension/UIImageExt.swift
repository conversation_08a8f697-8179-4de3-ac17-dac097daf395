//
//  UIImageExt.swift
//  OXYPLay
//
//  Created by renhw on 2023/1/14.
//

import Kingfisher
import UIKit

// MARK: - UIImageView扩展

extension UIImageView {
    /// 从URL加载图片，添加占位图（静默模式，不打印错误）
    func setImage(url: String?, placeholder: String = "placeholder_image") {
        guard let urlString = url, let imageURL = URL(string: urlString) else {
            self.image = UIImage(named: placeholder)
            return
        }

        // 使用静默方法加载图片，避免错误日志打印
        self.setImageSilently(with: imageURL, placeholder: UIImage(named: placeholder))
    }
}

// MARK: - UIImage扩展

extension UIImage {
    /// 创建纯色图片
    static func createSolidColorImage(color: UIColor, size: CGSize = CGSize(width: 1, height: 1)) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        guard let context = UIGraphicsGetCurrentContext() else { return nil }
        
        context.setFillColor(color.cgColor)
        context.fill(CGRect(origin: .zero, size: size))
        
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return image
    }
    
    /// 调整图片大小
    func resize(to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        draw(in: CGRect(origin: .zero, size: size))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return resizedImage
    }
    
    /// 创建圆角图片
    func withRoundedCorners(radius: CGFloat? = nil) -> UIImage? {
        let maxRadius = min(size.width, size.height) / 2
        let cornerRadius: CGFloat = radius ?? maxRadius
        
        UIGraphicsBeginImageContextWithOptions(size, false, scale)
        let rect = CGRect(origin: .zero, size: size)
        UIBezierPath(roundedRect: rect, cornerRadius: cornerRadius).addClip()
        draw(in: rect)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }

    /// 创建渐变背景图片
    /// - Parameters:
    ///   - size: 图片尺寸
    ///   - colors: 渐变颜色数组
    ///   - state: 按钮状态
    /// - Returns: 渐变背景图片
    class func createGradientImage(size: CGSize, colors: [UIColor], state: UIControl.State) -> UIImage {
        // 创建图形上下文
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        guard let context = UIGraphicsGetCurrentContext() else {
            return UIImage()
        }
        
        // 创建圆角路径
        let cornerRadius = size.height / 2
        let path = UIBezierPath(roundedRect: CGRect(origin: .zero, size: size), cornerRadius: cornerRadius)
        context.addPath(path.cgPath)
        context.clip()
        
        // 创建渐变
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let colorLocations: [CGFloat] = [0.0, 1.0]
        let cgColors = colors.map { $0.cgColor } as CFArray
        
        guard let gradient = CGGradient(colorsSpace: colorSpace, colors: cgColors, locations: colorLocations) else {
            return UIImage()
        }
        
        // 绘制渐变
        let startPoint = CGPoint(x: 0, y: size.height / 2)
        let endPoint = CGPoint(x: size.width, y: size.height / 2)
        
        context.drawLinearGradient(gradient, start: startPoint, end: endPoint, options: [])
        
        // 根据按钮状态调整图片
        if state == .highlighted {
            // 高亮状态下稍微暗一些
            context.setFillColor(UIColor.black.withAlphaComponent(0.1).cgColor)
            context.fill(CGRect(origin: .zero, size: size))
        } else if state == .disabled {
            // 禁用状态下添加灰色蒙层
            context.setFillColor(UIColor.gray.withAlphaComponent(0.3).cgColor)
            context.fill(CGRect(origin: .zero, size: size))
        }
        
        // 获取图片并结束上下文
        let image = UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
        UIGraphicsEndImageContext()
        
        return image
    }
}
