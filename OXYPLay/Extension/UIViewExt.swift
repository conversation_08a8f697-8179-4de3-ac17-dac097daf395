//
//  UIViewExt.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/24.
//

import UIKit

extension UIView {
    func setCustomCorners(topLeft: CGFloat, topRight: CGFloat,
                         bottomLeft: CGFloat, bottomRight: CGFloat) {
        let path = UIBezierPath()
        
        // 左上
        path.move(to: CGPoint(x: 0, y: topLeft))
        path.addArc(withCenter: CGPoint(x: topLeft, y: topLeft), radius: topLeft,
                    startAngle: .pi, endAngle: .pi * 1.5, clockwise: true)
        
        // 右上
        path.addLine(to: CGPoint(x: bounds.width - topRight, y: 0))
        path.addArc(withCenter: CGPoint(x: bounds.width - topRight, y: topRight), radius: topRight,
                    startAngle: .pi * 1.5, endAngle: 0, clockwise: true)
        
        // 右下
        path.addLine(to: CGPoint(x: bounds.width, y: bounds.height - bottomRight))
        path.addArc(withCenter: CGPoint(x: bounds.width - bottomRight, y: bounds.height - bottomRight), radius: bottomRight,
                    startAngle: 0, endAngle: .pi * 0.5, clockwise: true)
        
        // 左下
        path.addLine(to: CGPoint(x: bottomLeft, y: bounds.height))
        path.addArc(withCenter: CGPoint(x: bottomLeft, y: bounds.height - bottomLeft), radius: bottomLeft,
                    startAngle: .pi * 0.5, endAngle: .pi, clockwise: true)
        
        path.close()
        
        let mask = CAShapeLayer()
        mask.path = path.cgPath
        layer.mask = mask
    }
}



// MARK: - 布局
extension UIView {
    
    @discardableResult
    func constraints(left: NSLayoutXAxisAnchor? = nil,
                     top: NSLayoutYAxisAnchor? = nil,
                     right: NSLayoutXAxisAnchor? = nil,
                     bottom: NSLayoutYAxisAnchor? = nil,
                     centerX: NSLayoutXAxisAnchor? = nil,
                     centerY: NSLayoutYAxisAnchor? = nil,
                     leftConstant: CGFloat = 0,
                     topConstant: CGFloat = 0,
                     rightConstant: CGFloat = 0,
                     bottomConstant: CGFloat = 0,
                     centerXConstant: CGFloat = 0,
                     centerYConstant: CGFloat = 0,
                     widthConstant: CGFloat = 0,
                     heightConstant: CGFloat = 0) -> [NSLayoutConstraint] {
        
        translatesAutoresizingMaskIntoConstraints = false
        
        var constraints = [NSLayoutConstraint]()
        
        if let left = left {
            constraints.append(leftAnchor.constraint(equalTo: left, constant: leftConstant))
        }
        
        if let top = top {
            constraints.append(topAnchor.constraint(equalTo: top, constant: topConstant))
        }
        
        if let right = right {
            constraints.append(rightAnchor.constraint(equalTo: right, constant: -rightConstant))
        }
        
        if let bottom = bottom {
            constraints.append(bottomAnchor.constraint(equalTo: bottom, constant: -bottomConstant))
        }
        
        if let centerX = centerX {
            constraints.append(centerXAnchor.constraint(equalTo: centerX, constant: centerXConstant))
        }
        
        if let centerY = centerY {
            constraints.append(centerYAnchor.constraint(equalTo: centerY, constant: centerYConstant))
        }
        
        if widthConstant > 0 {
            constraints.append(widthAnchor.constraint(equalToConstant: widthConstant))
        }
        
        if heightConstant > 0 {
            constraints.append(heightAnchor.constraint(equalToConstant: heightConstant))
        }
        
        NSLayoutConstraint.activate(constraints)
        
        return constraints
    }
}

// MARK: - 圆角设置
extension UIView {
    func roundCorners(corners: UIRectCorner, radius: CGFloat) {
        let path = UIBezierPath(roundedRect: bounds, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        let mask = CAShapeLayer()
        mask.path = path.cgPath
        layer.mask = mask
    }
}

// MARK: - String Extension
extension String {
    /// 计算字符串在给定宽度和字体下的高度
    func height(withConstrainedWidth width: CGFloat, font: UIFont) -> CGFloat {
        let constraintRect = CGSize(width: width, height: .greatestFiniteMagnitude)
        let boundingBox = self.boundingRect(with: constraintRect,
                                           options: .usesLineFragmentOrigin,
                                           attributes: [.font: font],
                                           context: nil)
        return ceil(boundingBox.height)
    }
    
    /// 计算字符串在给定高度和字体下的宽度
    func width(withConstrainedHeight height: CGFloat, font: UIFont) -> CGFloat {
        let constraintRect = CGSize(width: .greatestFiniteMagnitude, height: height)
        let boundingBox = self.boundingRect(with: constraintRect,
                                           options: .usesLineFragmentOrigin,
                                           attributes: [.font: font],
                                           context: nil)
        return ceil(boundingBox.width)
    }
}
