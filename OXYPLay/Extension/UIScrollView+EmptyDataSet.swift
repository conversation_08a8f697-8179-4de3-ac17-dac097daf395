//
//  UIScrollView+EmptyDataSet.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/29.
//

import UIKit
import DZNEmptyDataSet

/// 空数据状态类型
enum EmptyDataSetType {
    case noData          // 无数据
    case noNetwork       // 无网络
    case loading         // 加载中
    case error           // 错误状态
    case noSearchResult  // 无搜索结果
    case custom(EmptyDataSetConfig) // 自定义配置
}

/// 空数据状态配置
struct EmptyDataSetConfig {
    let image: UIImage?
    let title: String?
    let description: String?
    let buttonTitle: String?
    let buttonAction: (() -> Void)?
    
    init(image: UIImage? = nil,
         title: String? = nil,
         description: String? = nil,
         buttonTitle: String? = nil,
         buttonAction: (() -> Void)? = nil) {
        self.image = image
        self.title = title
        self.description = description
        self.buttonTitle = buttonTitle
        self.buttonAction = buttonAction
    }
}

// MARK: - UIScrollView EmptyDataSet 扩展

extension UIScrollView {
    
    private struct AssociatedKeys {
        static var emptyDataSetType = "emptyDataSetType"
        static var emptyDataSetConfig = "emptyDataSetConfig"
        static var emptyDataSetDelegate = "emptyDataSetDelegate"
    }
    
    /// 当前空数据状态类型
    var emptyDataSetType: EmptyDataSetType? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.emptyDataSetType) as? EmptyDataSetType
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.emptyDataSetType, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    /// 自定义配置
    private var customConfig: EmptyDataSetConfig? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.emptyDataSetConfig) as? EmptyDataSetConfig
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.emptyDataSetConfig, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    /// 设置空数据状态
    /// - Parameter type: 空数据状态类型
    func setEmptyDataSet(type: EmptyDataSetType) {
        self.emptyDataSetType = type
        
        // 如果是自定义类型，保存配置
        if case .custom(let config) = type {
            self.customConfig = config
        }
        
        // 设置代理
        setupEmptyDataSetIfNeeded()
        
        // 刷新空数据状态
        self.reloadEmptyDataSet()
    }
    
    /// 隐藏空数据状态
    func hideEmptyDataSet() {
        self.emptyDataSetType = nil
        self.customConfig = nil
        self.reloadEmptyDataSet()
    }
    
    /// 设置空数据状态代理（如果还没有设置）
    private func setupEmptyDataSetIfNeeded() {
        if self.emptyDataSetSource == nil {
            let emptyDelegate = EmptyDataSetHandler()
            objc_setAssociatedObject(self, &AssociatedKeys.emptyDataSetDelegate, emptyDelegate, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
            
            self.emptyDataSetSource = emptyDelegate
            self.emptyDataSetDelegate = emptyDelegate
        }
    }
}

// MARK: - EmptyDataSet 处理器

private class EmptyDataSetHandler: NSObject, DZNEmptyDataSetSource, DZNEmptyDataSetDelegate {
    
    // MARK: - DZNEmptyDataSetSource
    
    func image(forEmptyDataSet scrollView: UIScrollView!) -> UIImage! {
        guard let type = scrollView.emptyDataSetType else { return nil }
        
        switch type {
        case .noData:
            return UIImage(named: "empty_no_data")
        case .noNetwork:
            return UIImage(named: "empty_no_network")
        case .loading:
            return UIImage(named: "empty_loading")
        case .error:
            return UIImage(named: "empty_error")
        case .noSearchResult:
            return UIImage(named: "empty_no_search")
        case .custom(let config):
            return config.image
        }
    }
    
    func title(forEmptyDataSet scrollView: UIScrollView!) -> NSAttributedString! {
        guard let type = scrollView.emptyDataSetType else { return nil }
        
        let titleText: String
        switch type {
        case .noData:
            titleText = "还没有相关内容"
        case .noNetwork:
            titleText = "网络连接失败"
        case .loading:
            titleText = "加载中..."
        case .error:
            titleText = "加载失败"
        case .noSearchResult:
            titleText = "没有找到相关内容"
        case .custom(let config):
            titleText = config.title ?? ""
        }
        
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 16, weight: .medium),
            .foregroundColor: color_2B2C2F
        ]
        
        return NSAttributedString(string: titleText, attributes: attributes)
    }

    func description(forEmptyDataSet scrollView: UIScrollView!) -> NSAttributedString! {
        guard let type = scrollView.emptyDataSetType else { return nil }

        let descriptionText: String
        switch type {
        case .noData:
            descriptionText = "点击刷新试试看"
        case .noNetwork:
            descriptionText = "请检查网络设置后重试"
        case .loading:
            descriptionText = "正在为您加载内容..."
        case .error:
            descriptionText = "加载出现问题\n请稍后重试"
        case .noSearchResult:
            descriptionText = "试试其他关键词\n或者浏览推荐内容"
        case .custom(let config):
            descriptionText = config.description ?? ""
        }

        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 14, weight: .regular),
            .foregroundColor: UIColor(hexString: "2B2C2F", transparency: 0.64)
        ]

        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 4
        paragraphStyle.alignment = .center

        let attributedString = NSMutableAttributedString(string: descriptionText, attributes: attributes)
        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: descriptionText.count))

        return attributedString
    }
    
    func buttonTitle(forEmptyDataSet scrollView: UIScrollView!, for state: UIControl.State) -> NSAttributedString! {
        guard let type = scrollView.emptyDataSetType else { return nil }
        
        let buttonText: String?
        switch type {
        case .noData:
            buttonText = "点击刷新" // 无数据状态显示刷新按钮
        case .noNetwork:
            buttonText = "重新加载"
        case .loading:
            buttonText = nil // 加载中不显示按钮
        case .error:
            buttonText = "重试"
        case .noSearchResult:
            buttonText = nil // 无搜索结果不显示按钮
        case .custom(let config):
            buttonText = config.buttonTitle
        }
        
        guard let text = buttonText else { return nil }
        
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 14, weight: .medium),
            .foregroundColor: color_blue
        ]
        
        return NSAttributedString(string: text, attributes: attributes)
    }
    
    func backgroundColor(forEmptyDataSet scrollView: UIScrollView!) -> UIColor! {
        return UIColor.clear
    }
    
    func verticalOffset(forEmptyDataSet scrollView: UIScrollView!) -> CGFloat {
        return -40 // 向上偏移40点，避免被导航栏遮挡
    }
    
    func spaceHeight(forEmptyDataSet scrollView: UIScrollView!) -> CGFloat {
        return 20 // 图片和文字之间的间距
    }
    
    // MARK: - DZNEmptyDataSetDelegate
    
    func emptyDataSetShouldDisplay(_ scrollView: UIScrollView!) -> Bool {
        return scrollView.emptyDataSetType != nil
    }
    
    func emptyDataSetShouldAllowTouch(_ scrollView: UIScrollView!) -> Bool {
        return true
    }
    
    func emptyDataSetShouldAllowScroll(_ scrollView: UIScrollView!) -> Bool {
        return true
    }
    
    func emptyDataSet(_ scrollView: UIScrollView!, didTap button: UIButton!) {
        guard let type = scrollView.emptyDataSetType else { return }
        
        switch type {
        case .noData, .noNetwork, .error:
            // 发送通知，让控制器处理重新加载
            NotificationCenter.default.post(name: .emptyDataSetDidTapReload, object: scrollView)
        case .custom(let config):
            config.buttonAction?()
        default:
            break
        }
    }
}

// MARK: - 通知名称扩展

extension Notification.Name {
    static let emptyDataSetDidTapReload = Notification.Name("EmptyDataSetDidTapReload")
}
