@_exported import MBProgressHUD

extension MBProgressHUD {
    // 通用提示方法
    class func showPrompt(_ text: String?,
                          in view: UIView,
                          duration: TimeInterval = 1.5,
                          darkStyle: Bool = true)
    {
        guard let text = text else { return }

        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        configureHUD(hud, darkStyle: darkStyle)
        hud.mode = .text
        hud.label.text = text
        scheduleHide(hud, duration: duration)
    }

    // 成功提示
    class func showSuccess(_ text: String?,
                           in view: UIView,
                           duration: TimeInterval = 1.5,
                           darkStyle: Bool = true)
    {
        guard let text = text else { return }

        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        configureHUD(hud, darkStyle: darkStyle)
        hud.mode = .customView
        hud.customView = UIImageView(image: UIImage(named: "icon_prompt_success"))
        hud.label.text = text
        scheduleHide(hud, duration: duration)
    }

    // 失败提示
    class func showError(_ text: String?,
                         in view: UIView,
                         duration: TimeInterval = 1.5,
                         darkStyle: Bool = true)
    {
        guard let text = text else { return }

        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        configureHUD(hud, darkStyle: darkStyle)
        hud.mode = .customView
        hud.customView = UIImageView(image: UIImage(named: "icon_prompt_failed"))
        hud.label.text = text
        scheduleHide(hud, duration: duration)
    }
    
    // 加载提示
    class func showLoading(in view: UIView?,
                           text: String? = "加载中...",
                           darkStyle: Bool = true)
    {
        guard let view = view else { return }

        let hud = MBProgressHUD.showAdded(to: view, animated: true)
        configureHUD(hud, darkStyle: darkStyle)
        hud.mode = .indeterminate
        hud.label.text = text
        hud.removeFromSuperViewOnHide = true
    }

    // MARK: - 私有方法

    private class func configureHUD(_ hud: MBProgressHUD, darkStyle: Bool) {
        hud.bezelView.style = .solidColor
        hud.bezelView.backgroundColor = darkStyle ? UIColor(white: 0, alpha: 0.8) : .clear
        hud.contentColor = darkStyle ? .white : .darkGray
        hud.label.font = UIFont.systemFont(ofSize: 14)
        hud.label.numberOfLines = 0
    }

    private class func scheduleHide(_ hud: MBProgressHUD, duration: TimeInterval) {
        hud.hide(animated: true, afterDelay: duration)
        hud.removeFromSuperViewOnHide = true
    }
}
