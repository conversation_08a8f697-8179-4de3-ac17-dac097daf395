//
//  UIImage+Extension.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/28.
//

import UIKit

extension UIImage {
    
    /// 调整图片尺寸
    /// - Parameter size: 目标尺寸
    /// - Returns: 调整后的图片
    func resized(to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, scale)
        defer { UIGraphicsEndImageContext() }
        
        draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    /// 压缩图片到指定大小（KB）
    /// - Parameter maxSizeKB: 最大文件大小（KB）
    /// - Returns: 压缩后的图片数据
    func compressedData(maxSizeKB: Int) -> Data? {
        let maxBytes = maxSizeKB * 1024
        var compression: CGFloat = 1.0
        var data = jpegData(compressionQuality: compression)
        
        while let imageData = data, imageData.count > maxBytes && compression > 0.1 {
            compression -= 0.1
            data = jpegData(compressionQuality: compression)
        }
        
        return data
    }
    
    /// 生成缩略图
    /// - Parameter maxSize: 最大尺寸
    /// - Returns: 缩略图
    func thumbnail(maxSize: CGFloat = 150) -> UIImage? {
        let scale = min(maxSize / size.width, maxSize / size.height)
        let newSize = CGSize(width: size.width * scale, height: size.height * scale)
        return resized(to: newSize)
    }
}
