//
//  UILabelExt.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import UIKit

// 为UILabel添加圆角功能的扩展
extension UILabel {
    
    @IBInspectable
    var labelCornerRadius: CGFloat {
        get { return layer.cornerRadius }
        set {
            layer.cornerRadius = newValue
            layer.masksToBounds = newValue > 0
        }
    }
    /// 在标签文本中间添加横线（删除线）
      /// - Parameters:
      ///   - text: 要显示的文本
      ///   - style: 删除线样式（默认为单实线）
      ///   - color: 删除线颜色（默认为文本颜色）
      func setStrikethroughText(_ text: String,
                                style: NSUnderlineStyle = .single,
                                color: UIColor? = nil) {
          let attributes: [NSAttributedString.Key: Any] = [
              .strikethroughStyle: style.rawValue,
              .strikethroughColor: color ?? self.textColor ?? .black
          ]
          
          let attributedString = NSAttributedString(
              string: text,
              attributes: attributes
          )
          
          self.attributedText = attributedString
      }
}

