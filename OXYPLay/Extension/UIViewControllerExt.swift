//
//  UIViewControllerExt.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/6/25.
//

extension UIViewController {
    static func getCurrentViewController() -> UIViewController? {
        guard let window = UIApplication.shared.keyWindow ?? UIApplication.shared.windows.first(where: { $0.isKeyWindow }) else {
            return nil
        }
        return findVisibleViewController(from: window.rootViewController)
    }
    
    private static func findVisibleViewController(from vc: UIViewController?) -> UIViewController? {
        guard let vc = vc else { return nil }
        
        if let presented = vc.presentedViewController {
            return findVisibleViewController(from: presented) // 模态弹窗
        }
        else if let nav = vc as? UINavigationController {
            return findVisibleViewController(from: nav.visibleViewController) // 导航栈顶
        }
        else if let tab = vc as? UITabBarController, let selected = tab.selectedViewController {
            return findVisibleViewController(from: selected) // 标签页选中
        }
        return vc
    }
}

// MARK: - 图片保存相册回调
extension UIViewController {
    @objc func image(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if let error = error {
            // 保存失败
            let alert = UIAlertController(title: "保存失败", message: error.localizedDescription, preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        } else {
            // 保存成功
            let alert = UIAlertController(title: "保存成功", message: "二维码已保存到相册", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        }
    }
}
