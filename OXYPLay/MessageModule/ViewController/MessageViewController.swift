//
//  MessageViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/22.
//


import UIKit

class MessageViewController: JXBaseRootViewController {
    private lazy var navView = MessageNavView()
    
    override func viewDidLoad() {
        configSegement()
        // 配置UI
        super.viewDidLoad()
        configUI()
    }
    override func configUI(){
        // 添加自定义导航栏
        view.addSubview(navView)
        navView.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalNavBarHeight)
        }
    }
    func configSegement(){
        titles = ["私信","通知","评论","点赞","收藏"]
        viewControllers = [MessageListController(), NotificationsListController(), NotificationsListController(), NotificationsListController(), NotificationsListController()]
    }
}
