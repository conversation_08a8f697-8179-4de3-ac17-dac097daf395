//
//  MessageListController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/16.
//

import UIKit
import RongIMKit
import JXSegmentedView

class MessageListController: RCConversationListViewController {

    override func viewDidLoad() {
        super.viewDidLoad()
        // Do any additional setup after loading the view.
    }
 

}
extension MessageListController:JXSegmentedListContainerViewListDelegate{
    func listView() -> UIView {
        return view
    }
}
