//
//  MessageNavView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/16.
//

/// 顶部搜索栏视图
class MessageNavView: BaseView {
    // MARK: - 属性
    
    // MARK: - UI组件
    
    /// 搜索容器视图
    private lazy var searchContainerView = UIView().then {
        $0.backgroundColor = color_F6F8F9
        $0.layer.cornerRadius = 18  // 高度的一半，将在布局时确保高度为40
        $0.layer.masksToBounds = true
    }
    
    /// 搜索图标
    private lazy var searchIconImageView = UIImageView().then {
        $0.image = UIImage(named: "assembly_search")
        $0.contentMode = .scaleAspectFit
    }
    
    /// 搜索文本框
    private lazy var searchTextView = UITextField().then {
        $0.font = UIFont.systemFont(ofSize: 14)
        $0.textColor = color_3D3E40
        $0.text = "在这里搜索内容"
        $0.textColor = color_686A6D80
    }
    
    /// 搜索按钮
    private lazy var searchButton = UIButton(type: .system).then {
        $0.backgroundColor = color_blue
        $0.setTitle("搜索", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.layer.cornerRadius = 15  // 按钮的高度将是30，所以圆角是15
    }
    
    /// 菜单按钮
    private lazy var menuButton = UIButton(type: .system).then {
        $0.setImage(UIImage(named: "home_leftmenu"), for: .normal)
        $0.tintColor = .black
    }
    
    
    // MARK: - 配置方法
    
    override func configUI() {
        super.configUI()
        backgroundColor = .white
        // 添加子视图
        addSubview(menuButton)
        addSubview(searchContainerView)
        
        // 搜索容器内的子视图
        searchContainerView.addSubview(searchIconImageView)
        searchContainerView.addSubview(searchTextView)
        searchContainerView.addSubview(searchButton)
        

    }
    
    override func configLayout() {
        super.configLayout()
        
        // 主按钮约束
        menuButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalTo(searchContainerView)
            make.width.height.equalTo(24)
        }
        
        searchContainerView.snp.makeConstraints { make in
            make.left.equalTo(menuButton.snp.right).offset(24)
            make.bottom.equalToSuperview().offset(-8)
            make.height.equalTo(36)
            make.right.equalTo(-24)
        }
        
        // 搜索容器内部约束
        searchIconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
        searchTextView.snp.makeConstraints { make in
            make.left.equalTo(searchIconImageView.snp.right).offset(8)
            make.top.bottom.equalToSuperview()
            make.right.equalTo(searchButton.snp.left).offset(-8)
        }
        
        searchButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-5)
            make.centerY.equalToSuperview()
            make.width.equalTo(60)
            make.height.equalTo(30)
        }
    }
}


