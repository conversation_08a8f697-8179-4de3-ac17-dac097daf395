import UIKit
import Combine
import Then

class LoginViewController: BaseViewController {
    // MARK: - 属性
    
    private let viewModel = LoginViewModel()
    
    // MARK: - 生命周期方法

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        loginView.phoneTextField.text = "15642947778"

        view.backgroundColor = color_2B2C2F
        
        // 添加子视图
        view.addSubview(loginTopView)
        view.addSubview(loginView)
        
        // 布局
        fd_prefersNavigationBarHidden = true
        
        loginTopView.snp.makeConstraints { make in
            make.top.bottom.left.right.equalToSuperview()
        }
        
        loginView.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.right.equalTo(0)
            make.bottom.equalTo(0)
        }
    }
    
    // MARK: - 绑定ViewModel
    
   override func setupBindings() {
        bindInputs()
        bindOutputs()
    }
    
    private func bindInputs() {
        // 绑定手机号输入
        loginView.phoneTextField.textPublisher
            .sink { [weak self] text in
                self?.viewModel.phone = text ?? ""
            }
            .store(in: &cancellables)
        
        // 绑定密码输入
        loginView.passwordTextField.textPublisher
            .sink { [weak self] text in
                self?.viewModel.password = text ?? ""
            }
            .store(in: &cancellables)
        
        // 绑定验证码输入
        loginView.verifyCodeTextField.textPublisher
            .sink { [weak self] text in
                self?.viewModel.code = text ?? ""
            }
            .store(in: &cancellables)
    }
    
    private func bindOutputs() {
        // 绑定登录按钮状态
        viewModel.$isLoginButtonEnabled
            .receive(on: RunLoop.main)
            .sink { [weak self] isEnabled in
                self?.loginView.updateLoginButtonState(isEnabled: isEnabled)
            }
            .store(in: &cancellables)
        
        // 绑定登录模式
        viewModel.$isPasswordMode
            .receive(on: RunLoop.main)
            .sink { [weak self] isPasswordMode in
                self?.loginView.switchLoginMode(isPasswordMode: isPasswordMode)
            }
            .store(in: &cancellables)
        
        // 绑定倒计时
        viewModel.$countdown
            .receive(on: RunLoop.main)
            .sink { [weak self] countdown in
                self?.loginView.updateVerifyCodeButton(countdown: countdown)
            }
            .store(in: &cancellables)
        
        // 绑定登录结果
        viewModel.loginResultPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] success in
                guard let self = self else { return }
                if success {
                    LoginManager.shared.showMainTabBar()
                } else {
                    MBProgressHUD.showError("登录失败", in: self.view)
                }
            }
            .store(in: &cancellables)
        
        // 绑定发送验证码结果
        viewModel.sendCodeResultPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] success in
                guard let self = self else { return }
                if !success {
                    MBProgressHUD.showError("验证码发送失败", in: self.view)
                }
            }
            .store(in: &cancellables)
        
        // 绑定加载状态
        viewModel.$isLoading
            .receive(on: RunLoop.main)
            .sink { [weak self] isLoading in
                guard let self = self else { return }
                if isLoading {
                    MBProgressHUD.showLoading(in: self.view)
                } else {
                    MBProgressHUD.hide(for: self.view, animated: true)
                }
            }
            .store(in: &cancellables)
        
    }

    // MARK: - 懒加载UI组件
    
    /// 登录页顶部视图
    lazy var loginTopView = LoginTopView().then {
        $0.delegate = self
    }

    /// 登录表单视图
    lazy var loginView = LoginView().then {
        $0.delegate = self
    }
}

// MARK: - LoginTopViewDelegate

extension LoginViewController: LoginTopViewDelegate {
    func loginTopViewDidTapSkipButton(_ view: LoginTopView) {
        LoginManager.shared.showMainTabBar()
    }
}

// MARK: - LoginViewDelegate

extension LoginViewController: LoginViewDelegate {
    func loginViewDidTapLoginButton(_ view: LoginView) {
        if viewModel.isPasswordMode {
            viewModel.loginWithPassword()
        } else {
            viewModel.loginWithVerifyCode()
        }
    }

    func loginViewDidTapWechatLoginButton(_: LoginView) {
        viewModel.wechatLogin()
    }

    func loginViewDidTapSendCodeButton(_: LoginView) {
        viewModel.sendCode()
    }

    func loginViewDidTapQuickButton(_ view: LoginView) {
        LoginManager.shared.startQuickLogin()
    }
    
    func loginViewDidToggleLoginMode(_ view: LoginView, isPasswordMode: Bool) {
        viewModel.toggleLoginMode()
    }
}
