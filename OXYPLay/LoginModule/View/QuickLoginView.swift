//
//  QuickLoginView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/25.
//

import ATAuthSDK

class QuickLoginView: TXCustomModel {
    // MARK: - 布局常量
    
    private struct Layout {
        // 基准Y位置
        static let baseY: CGFloat = 482
        // 各元素高度
        static let numberHeight: CGFloat = 24
        static let loginBtnHeight: CGFloat = 44
        static let changeBtnHeight: CGFloat = 12
        static let privacyHeight: CGFloat = 88
        // 间距
        static let standardSpacing: CGFloat = 24
        static let smallSpacing: CGFloat = 12
        static let largeSpacing: CGFloat = 30
        // 宽度
        static let buttonWidth: CGFloat = 280
    }
    
    override init() {
        super.init()
        configUI()
    }
    
    func configUI() {
        // 隐藏不需要的元素
        sloganIsHidden = true
        
        //隐藏导航
        navIsHidden = true
        
        privacyAlertIsNeedShow = true
        
        //电话号码
        numberColor = .white
        numberFont = .systemFont(ofSize: 24, weight: .medium)
        numberFrameBlock = { screenSize, superViewSize, frame in
            return CGRect(x: frame.origin.x, y: Layout.baseY, width: 0, height: 0)
        }
        
        // 登录按钮
        loginBtnBgImgs = [
            UIImage.createGradientImage(size: CGSize(width: Layout.buttonWidth, height: Layout.loginBtnHeight), colors: [color_48A1FF, color_blue], state: .normal),
            UIImage.createGradientImage(size: CGSize(width: Layout.buttonWidth, height: Layout.loginBtnHeight), colors: [color_48A1FF, color_blue], state: .normal),
            UIImage.createGradientImage(size: CGSize(width: Layout.buttonWidth, height: Layout.loginBtnHeight), colors: [color_48A1FF, color_blue], state: .normal)
        ]
        loginBtnText = NSAttributedString(string: "一键登录", attributes: [
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 14, weight: .medium),
        ])
        loginBtnFrameBlock = { screenSize, superViewSize, frame in
            let centerX = (screenSize.width - Layout.buttonWidth) / 2
            let y = Layout.baseY + Layout.numberHeight + Layout.standardSpacing
            return CGRect(x: centerX, y: y, width: Layout.buttonWidth, height: Layout.loginBtnHeight)
        }
        
        //切换短信
        changeBtnTitle = NSAttributedString(string: "其他手机号登录", attributes: [
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 12, weight: .regular),
        ])
        changeBtnFrameBlock = { screenSize, superViewSize, frame in
            let centerX = (screenSize.width - Layout.buttonWidth) / 2
            let loginBtnBottom = Layout.baseY + Layout.numberHeight + Layout.standardSpacing + Layout.loginBtnHeight
            let y = loginBtnBottom + Layout.standardSpacing
            return CGRect(x: centerX, y: y, width: Layout.buttonWidth, height: Layout.changeBtnHeight)
        }
        
        // 隐私协议
        privacyOne = ["用户协议", "https://www.example.com/terms"]
        privacyTwo = ["隐私政策", "https://www.example.com/privacy"]
        privacyFont = UIFont.systemFont(ofSize: 12, weight: .light)
        privacyOperatorPreText = "《"
        privacyOperatorSufText = "》"
        privacyFrameBlock = { screenSize, superViewSize, frame in
            let changeBtnBottom = Layout.baseY + Layout.numberHeight + Layout.standardSpacing + Layout.loginBtnHeight + Layout.standardSpacing + Layout.changeBtnHeight
            let y = changeBtnBottom + Layout.smallSpacing + Layout.standardSpacing + Layout.smallSpacing
            return CGRect(x: 20, y: y, width: screenSize.width - 40, height: Layout.privacyHeight)
        }
        
        customViewBlock = { [weak self] view in
            guard let self = self else { return }
            self.loginTopView.frame = view.frame
            view.addSubview(self.loginTopView)
            view.addSubview(self.wechatLoginView)
            wechatLoginView.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                
                // 微信登录视图位置计算
                let privacyBottom = Layout.baseY + Layout.numberHeight + Layout.standardSpacing +
                    Layout.loginBtnHeight + Layout.standardSpacing +
                    Layout.changeBtnHeight + Layout.smallSpacing +
                    Layout.standardSpacing + Layout.smallSpacing + Layout.privacyHeight
                
                let wechatTopY = privacyBottom + Layout.largeSpacing
                make.top.equalTo(wechatTopY)
            }
        }
        
        // 配置美化二次弹窗
        configPrivacyAlert()
    }
    
    // MARK: - 配置二次隐私协议弹窗
    
    private func configPrivacyAlert() {
        // 基本设置
        privacyAlertIsNeedAutoLogin = true
        
        // 弹窗样式设置
        privacyAlertCornerRadiusArray = [16, 16, 16, 16] // 四个角都是圆角
        privacyAlertBackgroundColor = .white
        privacyAlertAlpha = 1.0
        
        // 弹窗尺寸和位置
        privacyAlertFrameBlock = { screenSize, superViewSize, frame in
            let width = min(screenSize.width - 80, 300)
            let height: CGFloat = 280
            let x = (screenSize.width - width) / 2
            let y = (screenSize.height - height) / 2 - 20 // 稍微往上一点
            return CGRect(x: x, y: y, width: width, height: height)
        }
        
        // 优化弹窗动画，避免闪屏问题
        let entryAnimation = CABasicAnimation(keyPath: "transform.scale")
        entryAnimation.fromValue = 0.95
        entryAnimation.toValue = 1.0
        entryAnimation.duration = 0.25
        entryAnimation.timingFunction = CAMediaTimingFunction(name: .easeOut)
        entryAnimation.fillMode = .forwards
        entryAnimation.isRemovedOnCompletion = false
        privacyAlertEntryAnimation = entryAnimation
        
        let exitAnimation = CABasicAnimation(keyPath: "transform.scale")
        exitAnimation.fromValue = 1.0
        exitAnimation.toValue = 0.95
        exitAnimation.duration = 0.2
        exitAnimation.timingFunction = CAMediaTimingFunction(name: .easeIn)
        exitAnimation.fillMode = .forwards
        exitAnimation.isRemovedOnCompletion = false
        privacyAlertExitAnimation = exitAnimation
        
        // 标题设置
        privacyAlertTitleContent = "请阅读并同意以下条款"
        privacyAlertTitleFont = UIFont.systemFont(ofSize: 16, weight: .medium)
        privacyAlertTitleColor = UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0)
        privacyAlertTitleBackgroundColor = .white
        privacyAlertTitleAlignment = .center
        
        privacyAlertTitleFrameBlock = { screenSize, superViewSize, frame in
            return CGRect(x: 20, y: 24, width: superViewSize.width - 40, height: 22)
        }
        
        // 内容设置
        privacyAlertContentFont = UIFont.systemFont(ofSize: 13, weight: .regular)
        privacyAlertLineSpaceDp = 5.0
        privacyAlertContentBackgroundColor = .white
        privacyAlertContentColors = [UIColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 1.0), color_blue]
        privacyAlertContentOperatorFont = UIFont.systemFont(ofSize: 13, weight: .regular)
        privacyAlertContentUnderline = false
        privacyAlertContentAlignment = .left
        
        // 为了保持一致性，可以与应用中的其他隐私政策文案保持一致
        privacyAlertPreText = "我已阅读并同意"
        privacyAlertSufText = "，未注册手机号登录成功后将自动注册。"
        
        privacyAlertPrivacyContentFrameBlock = { screenSize, superViewSize, frame in
            return CGRect(x: 24, y: 24 + 22 + 16, width: superViewSize.width - 48, height: 120)
        }
        
        // 按钮设置
        privacyAlertBtnContent = "同意并继续"
        privacyAlertBtnCornerRadius = 22 // 圆角按钮
        
        // 创建渐变背景按钮图片
        let btnSize = CGSize(width: 240, height: 44)
        let normalImage = UIImage.createGradientImage(size: btnSize, colors: [color_48A1FF, color_blue], state: .normal)
        let highlightImage = UIImage.createGradientImage(size: btnSize, colors: [color_48A1FF.withAlphaComponent(0.9), color_blue.withAlphaComponent(0.9)], state: .highlighted)
        
        privacyAlertBtnBackgroundImages = [normalImage, highlightImage]
        privacyAlertButtonTextColors = [.white, .white]
        privacyAlertButtonFont = UIFont.systemFont(ofSize: 15, weight: .medium)
        
        privacyAlertButtonFrameBlock = { screenSize, superViewSize, frame in
            let width: CGFloat = 240
            let height: CGFloat = 44
            let x = (superViewSize.width - width) / 2
            let y = superViewSize.height - height - 30
            return CGRect(x: x, y: y, width: width, height: height)
        }
        
        // 关闭按钮设置
        privacyAlertCloseButtonIsNeedShow = true
        
        privacyAlertCloseFrameBlock = { screenSize, superViewSize, frame in
            return CGRect(x: superViewSize.width - 44 - 16, y: 0, width: 44, height: 44)
        }
        
        // 蒙版设置
        privacyAlertMaskIsNeedShow = true
        tapPrivacyAlertMaskCloseAlert = true
        privacyAlertMaskColor = UIColor.black
        privacyAlertMaskAlpha = 0.5
        
        // 优化蒙版动画，避免闪屏问题
        let maskEntryAnimation = CABasicAnimation(keyPath: "opacity")
        maskEntryAnimation.fromValue = 0.0
        maskEntryAnimation.toValue = 1.0
        maskEntryAnimation.duration = 0.25
        maskEntryAnimation.timingFunction = CAMediaTimingFunction(name: .easeOut)
        maskEntryAnimation.fillMode = .forwards
        maskEntryAnimation.isRemovedOnCompletion = false
        privacyAlertMaskEntryAnimation = maskEntryAnimation
        
        let maskExitAnimation = CABasicAnimation(keyPath: "opacity")
        maskExitAnimation.fromValue = 1.0
        maskExitAnimation.toValue = 0.0
        maskExitAnimation.duration = 0.2
        maskExitAnimation.timingFunction = CAMediaTimingFunction(name: .easeIn)
        maskExitAnimation.fillMode = .forwards
        maskExitAnimation.isRemovedOnCompletion = false
        privacyAlertMaskExitAnimation = maskExitAnimation
    }
    
    /// 登录页顶部视图
    lazy var loginTopView = LoginTopView().then {
        $0.delegate = self
    }
    
    // 微信登录视图
    lazy var wechatLoginView = WechatLoginView().then {
        $0.delegate = self
    }
}

// MARK: - LoginTopViewDelegate

extension QuickLoginView: LoginTopViewDelegate {
    func loginTopViewDidTapSkipButton(_ view: LoginTopView) {
        TXCommonHandler.sharedInstance().cancelLoginVC(animated: true)
        LoginManager.shared.showMainTabBar()
    }
}

// MARK: - WechatLoginViewDelegate

extension QuickLoginView: WechatLoginViewDelegate {
    func wechatLoginViewDidTapWechatButton(_ view: WechatLoginView) {
        WechatManager.shared.login { userInfo, error in
            guard let userInfo = userInfo,
                  let unionId = userInfo["unionid"] as? String else {
                print("微信登录失败: \(error?.localizedDescription ?? "未知错误")")
                return
            }

            // 使用unionId进行登录
            print("获取到微信用户信息: \(userInfo)")
            print("unionId: \(unionId)")
            var params = [String: Any]()
            params["unionId"] = unionId
            LoginManager.shared.requestLoginWithParams(params: params)
        }
    }
}
