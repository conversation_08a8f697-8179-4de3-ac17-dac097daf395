import UIKit
import Then
import SnapKit
import Kingfisher

/// 登录页面顶部视图
class LoginTopView: BaseView {
    // MARK: - 属性
    
    /// 跳过按钮点击事件代理
    weak var delegate: LoginTopViewDelegate?
    
    // MARK: - 重写方法
    
    override func configUI() {
        // 添加子视图
        addSubview(backgroundImageView)
        addSubview(skipButton)
        addSubview(titleLabel)
        addSubview(subTitleLabel)
        
        // 设置背景图
        let url = URL(string: "https://p1.itc.cn/q_70/images03/20211010/0060c7297cee463b83cf8094a66bcd85.gif")
        backgroundImageView.kf.setImage(with: url)
        
        // 绑定按钮事件
        skipButton.addTarget(self, action: #selector(skipButtonTapped), for: .touchUpInside)
    }
    
    // MARK: - 按钮事件
    
    @objc private func skipButtonTapped() {
        delegate?.loginTopViewDidTapSkipButton(self)
    }
    
    override func configLayout() {
        backgroundImageView.snp.makeConstraints { $0.edges.equalToSuperview() }
        
        skipButton.snp.makeConstraints { make in
            make.right.equalTo(-24)
            make.top.equalTo(ScreenInfo.totalNavBarHeight)
            make.height.equalTo(28)
            make.width.equalTo(56)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(48)
            make.top.equalTo(skipButton.snp.bottom).offset(88)
        }
        
        subTitleLabel.snp.makeConstraints { make in
            make.left.equalTo(48)
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
        }
    }
    
    // MARK: - 懒加载UI组件
    
    lazy var backgroundImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
    }

    lazy var titleLabel = UILabel().then {
        $0.text = "手机号注册登录"
        $0.font = .systemFont(ofSize: 24, weight: .medium)
        $0.textColor = UIColor.white
    }

    lazy var subTitleLabel = UILabel().then {
        $0.text = "未注册手机号登录成功后将自动注册"
        $0.font = .systemFont(ofSize: 12, weight: .regular)
        $0.textColor = color_FFFFFF80
    }

    lazy var skipButton = UIButton().then {
        $0.setTitle("跳过", for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 12, weight: .regular)
        $0.setTitleColor(color_FFFFFF80, for: .normal)
        $0.layer.cornerRadius = 8
        $0.layer.borderWidth = 0.5
        $0.layer.borderColor = color_FFFFFF80.cgColor
        $0.clipsToBounds = true
    }
}

/// 登录页面顶部视图代理协议
protocol LoginTopViewDelegate: AnyObject {
    /// 跳过按钮点击事件
    func loginTopViewDidTapSkipButton(_ view: LoginTopView)
} 
