import UIKit
import Then
import SnapKit

/// 微信登录视图代理协议
protocol WechatLoginViewDelegate: AnyObject {
    /// 微信登录按钮点击事件
    func wechatLoginViewDidTapWechatButton(_ view: WechatLoginView)
}

/// 微信登录视图
class WechatLoginView: BaseView {
    // MARK: - 属性
    
    /// 代理
    weak var delegate: WechatLoginViewDelegate?
    
    // MARK: - 重写方法
    
    override func configUI() {
        // 添加子视图
        addSubview(lineL)
        addSubview(lineR)
        addSubview(wechatLabel)
        addSubview(wechatLoginButton)
        
        // 绑定按钮事件
        wechatLoginButton.addTarget(self, action: #selector(wechatLoginButtonTapped), for: .touchUpInside)
    }
    
    // MARK: - 按钮事件
    
    @objc private func wechatLoginButtonTapped() {
        delegate?.wechatLoginViewDidTapWechatButton(self)
    }
    
    override func configLayout() {
        wechatLoginButton.snp.makeConstraints { make in
            make.bottom.equalTo(-54)
            make.centerX.equalToSuperview()
            make.width.equalTo(32)
            make.height.equalTo(32)
        }
        
        wechatLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(wechatLoginButton.snp.top).offset(-20)
            make.top.equalToSuperview()
        }
        
        lineL.snp.makeConstraints { make in
            make.right.equalTo(wechatLabel.snp.left).offset(-20)
            make.centerY.equalTo(wechatLabel)
            make.width.equalTo(70)
            make.height.equalTo(1)
        }
        
        lineR.snp.makeConstraints { make in
            make.left.equalTo(wechatLabel.snp.right).offset(20)
            make.centerY.equalTo(wechatLabel)
            make.width.equalTo(70)
            make.height.equalTo(1)
        }
    }
    
    // MARK: - 懒加载UI组件
    
    lazy var lineL = UIView().then {
        $0.backgroundColor = .white
    }

    lazy var lineR = UIView().then {
        $0.backgroundColor = .white
    }

    lazy var wechatLabel = UILabel().then {
        $0.text = "第三方账号登录"
        $0.textColor = .white
        $0.font = .systemFont(ofSize: 11, weight: .light)
    }

    lazy var wechatLoginButton = UIButton(type: .system).then {
        $0.backgroundColor = UIColor.green
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
    }
} 
