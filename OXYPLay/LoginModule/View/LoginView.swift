import ActiveLabel
import UIKit
import Combine
import Then
import SnapKit

// 登录视图代理协议
protocol LoginViewDelegate: AnyObject {
    /// 登录按钮点击事件
    func loginViewDidTapLoginButton(_ view: LoginView)
    /// 微信登录按钮点击事件
    func loginViewDidTapWechatLoginButton(_ view: LoginView)
    /// 发送验证码按钮点击事件
    func loginViewDidTapSendCodeButton(_ view: LoginView)
    /// 一键登录按钮点击事件
    func loginViewDidTapQuickButton(_ view: LoginView)
    /// 切换登录模式事件
    func loginViewDidToggleLoginMode(_ view: LoginView, isPasswordMode: Bool)
}

class LoginView: BaseView {
    // MARK: - 属性
    
    /// 代理对象
    weak var delegate: LoginViewDelegate?
    
    
    // MARK: - 生命周期方法
    
    override func configUI() {
        setupUI()
        setupTextFieldObservers()
        updateLoginButtonState()
    }
    
    // MARK: - UI设置
    
    func setupUI() {
        addSubviews()
        setupButtonActions()
    }
    
    func addSubviews() {
        // 添加主要视图
        addSubview(phoneView)
        addSubview(codeView)
        addSubview(passwordView)
        addSubview(loginButton)
        addSubview(privacyLabel)
        addSubview(privacyButton)
        addSubview(changeTypeButton)
        addSubview(quickButton)
        addSubview(wechatLoginView)
        
        // 添加子视图
        phoneView.addSubview(phone86)
        phoneView.addSubview(linePhone)
        phoneView.addSubview(phoneTextField)
        passwordView.addSubview(passwordIcon)
        passwordView.addSubview(passwordLine)
        passwordView.addSubview(passwordTextField)
        codeView.addSubview(linev)
        codeView.addSubview(verifyCodeTextField)
        codeView.addSubview(sendCodeButton)
    }
    
    func setupButtonActions() {
        // 绑定按钮事件
        changeTypeButton.addTarget(self, action: #selector(changeTypeButtonTapped), for: .touchUpInside)
        sendCodeButton.addTarget(self, action: #selector(sendCodeButtonTapped), for: .touchUpInside)
        loginButton.addTarget(self, action: #selector(loginButtonTapped), for: .touchUpInside)
        quickButton.addTarget(self, action: #selector(quickButtonTapped), for: .touchUpInside)
    }
    
    // MARK: - 事件监听
    
    // 设置文本框变化监听
    func setupTextFieldObservers() {
        // 监听手机号输入框
        phoneTextField.textPublisher
            .sink { [weak self] _ in
                self?.updateLoginButtonState()
            }
            .store(in: &cancellables)
        
        // 监听密码输入框
        passwordTextField.textPublisher
            .sink { [weak self] text in
                guard let self = self else { return }
                self.updateLoginButtonState()
                guard let text = text,text.isEmpty else{
                    return self.passwordButton.isHidden = false
                }
                self.passwordButton.isHidden = true
            }
            .store(in: &cancellables)
        
        // 监听验证码输入框
        verifyCodeTextField.textPublisher
            .sink { [weak self] _ in
                self?.updateLoginButtonState()
            }
            .store(in: &cancellables)
    }
    
    // 更新登录按钮状态
    func updateLoginButtonState(isEnabled: Bool? = nil) {
        let phoneText = phoneTextField.text ?? ""
        let passwordText = passwordTextField.text ?? ""
        let codeText = verifyCodeTextField.text ?? ""
        
        // 判断是否启用登录按钮
        let shouldEnable: Bool
        
        if let isEnabled = isEnabled {
            shouldEnable = isEnabled
        } else if changeTypeButton.isSelected {
            // 密码登录模式：手机号和密码都不为空
            shouldEnable = !phoneText.isEmpty && !passwordText.isEmpty
        } else {
            // 验证码登录模式：手机号和验证码都不为空
            shouldEnable = !phoneText.isEmpty && !codeText.isEmpty
        }
        
        // 更新登录按钮状态
        loginButton.isEnabled = shouldEnable
        loginButton.gradientColors = shouldEnable ? [color_48A1FF, color_blue] : [color_999DA1, color_8D9096]
        loginButton.setTitleColor(shouldEnable ? .white : color_FFFFFF72, for: .normal)
    }
    
    // 切换登录模式（密码/验证码）
    func switchLoginMode(isPasswordMode: Bool) {
        changeTypeButton.isSelected = isPasswordMode
        codeView.isHidden = isPasswordMode
        passwordView.isHidden = !isPasswordMode
        updateLoginButtonState()
    }
    
    // 更新验证码按钮状态
    func updateVerifyCodeButton(countdown: Int) {
        if countdown > 0 {
            sendCodeButton.isEnabled = false
            sendCodeButton.setTitle("\(countdown)秒后重试", for: .normal)
            sendCodeButton.setTitleColor(color_999DA1, for: .normal)
        } else {
            sendCodeButton.isEnabled = true
            sendCodeButton.setTitle("获取验证码", for: .normal)
            sendCodeButton.setTitleColor(color_blue, for: .normal)
        }
    }
    
    // MARK: - 按钮事件
    
    @objc  func changeTypeButtonTapped() {
        changeTypeButton.isSelected.toggle()
        codeView.isHidden = changeTypeButton.isSelected
        passwordView.isHidden = !changeTypeButton.isSelected
        
        // 通知代理
        delegate?.loginViewDidToggleLoginMode(self, isPasswordMode: changeTypeButton.isSelected)
    }
    
    @objc  func sendCodeButtonTapped() {
        delegate?.loginViewDidTapSendCodeButton(self)
    }
    
    @objc  func loginButtonTapped() {
        delegate?.loginViewDidTapLoginButton(self)
    }
    
    @objc  func quickButtonTapped() {
        delegate?.loginViewDidTapQuickButton(self)
    }
    
    // MARK: - 布局
    
    override func configLayout() {
        setupLayout()
    }
    
    func setupLayout() {
        // 输入框布局
        phone86.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.bottom.equalToSuperview()
        }
        linePhone.snp.makeConstraints { make in
            make.left.equalTo(58)
            make.width.equalTo(1)
            make.top.equalTo(10)
            make.bottom.equalTo(-10)
        }
        
        phoneTextField.snp.makeConstraints { make in
            make.left.equalTo(linePhone).offset(12)
            make.right.equalToSuperview().offset(-15)
            make.top.bottom.equalToSuperview()
        }
        passwordIcon.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.centerY.equalToSuperview()
        }
        passwordLine.snp.makeConstraints { make in
            make.left.equalTo(58)
            make.width.equalTo(1)
            make.top.equalTo(10)
            make.bottom.equalTo(-10)
        }
        passwordTextField.snp.makeConstraints { make in
            make.left.equalTo(passwordLine.snp.right).offset(12)
            make.right.equalToSuperview().offset(-15)
            make.top.bottom.equalToSuperview()
        }
        
        sendCodeButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-15)
            make.centerY.equalToSuperview()
            make.width.equalTo(100)
        }
        
        linev.snp.makeConstraints { make in
            make.top.equalTo(10)
            make.bottom.equalTo(-10)
            make.width.equalTo(1)
            make.right.equalTo(sendCodeButton.snp.left).offset(-10)
        }
        
        verifyCodeTextField.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.right.equalTo(linev.snp.left).offset(-10)
            make.top.bottom.equalToSuperview()
        }
        /// 按钮高度
        let buttonHeight: CGFloat = 48
        
        /// 输入框高度
        let textFieldHeight: CGFloat = 44
        
        /// 标准边距
        // 容器视图布局
        phoneView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalTo(loginButton)
            make.height.equalTo(textFieldHeight)
        }
        
        codeView.snp.makeConstraints { make in
            make.top.equalTo(phoneView.snp.bottom).offset(16)
            make.left.right.equalTo(loginButton)
            make.height.equalTo(textFieldHeight)
        }
        
        passwordView.snp.makeConstraints { make in
            make.top.equalTo(phoneView.snp.bottom).offset(16)
            make.left.right.equalTo(loginButton)
            make.height.equalTo(textFieldHeight)
        }
        
        // 按钮布局
        loginButton.snp.makeConstraints { make in
            make.top.equalTo(codeView.snp.bottom).offset(30)
            make.left.equalTo(48)
            make.right.equalTo(-48)
            make.height.equalTo(buttonHeight)
        }
        
        quickButton.snp.makeConstraints { make in
            make.top.equalTo(loginButton.snp.bottom).offset(20)
            make.left.equalTo(48)
            make.bottom.equalTo(privacyLabel.snp.top).offset(-28)
        }
        
        changeTypeButton.snp.makeConstraints { make in
            make.top.equalTo(loginButton.snp.bottom).offset(20)
            make.right.equalTo(-48)
            make.bottom.equalTo(privacyLabel.snp.top).offset(-28)
        }
        
        // 隐私政策布局
        privacyLabel.snp.makeConstraints { make in
            make.right.lessThanOrEqualTo(-20)
            make.left.equalTo(privacyButton.snp.right).offset(5)
            make.bottom.equalTo(wechatLoginView.wechatLabel.snp.top).offset(-30)
            make.centerX.equalToSuperview()
        }
        
        privacyButton.snp.makeConstraints { make in
            make.centerY.equalTo(privacyLabel)
            make.left.equalTo(20)
            make.width.height.equalTo(13.5)
        }
        
        // 微信登录视图布局
        wechatLoginView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(privacyLabel.snp.bottom).offset(30)
        }
    }
    @objc func togglePasswordVisibility(_ sender: UIButton) {
        sender.isSelected.toggle()
        passwordTextField.isSecureTextEntry = !sender.isSelected
    }
    // 视图销毁时确保清理定时器和订阅
    deinit {
        cancellables.removeAll()
    }
    
    // MARK: - 懒加载UI组件
    
    lazy var phoneView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 22
    }
    
    lazy var codeView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 22
    }
    lazy var passwordIcon = UIImageView().then {
        $0.image = UIImage(named: "password_icon")
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }
    lazy var passwordLine =  UIView().then {
        $0.backgroundColor = color_3D3E4012
    }
    lazy var passwordButton = UIButton().then{
        $0.frame = CGRectMake(0, 0, 20, 20)
        $0.setImage(UIImage(named: "password_show"), for: .selected)
        $0.setImage(UIImage(named: "password_dissmis"), for: .normal)
        $0.addTarget(self, action: #selector(togglePasswordVisibility), for: .touchUpInside)
    }
    lazy var passwordTextField = UITextField().then {
        $0.placeholder = "请输入密码"
        $0.keyboardType = .asciiCapable
        $0.isSecureTextEntry = true
        $0.textColor = color_3D3E40
        $0.font = .systemFont(ofSize: 14, weight: .regular)
        $0.rightView = passwordButton
        $0.rightViewMode = .always
    }
    lazy var passwordView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 22
        $0.isHidden = true
    }
    
    lazy var linev = UIView().then {
        $0.backgroundColor = color_3D3E4012
    }
    lazy var linePhone = UIView().then {
        $0.backgroundColor = color_3D3E4012
    }
    lazy var changeTypeButton = UIButton().then {
        $0.setTitle("验证码登录", for: .selected)
        $0.setTitle("密码登录", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 12, weight: .regular)
    }
    
    lazy var quickButton = UIButton().then {
        $0.setTitle("本机号码一键登录", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 12, weight: .regular)
    }
    lazy var phone86 = UILabel().then {
        $0.text = "+86"
        $0.font = .systemFont(ofSize: 14, weight: .regular)
        $0.textColor = color_3D3E40
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }
    lazy var phoneTextField = UITextField().then {
        $0.placeholder = "请输入手机号"
        $0.keyboardType = .phonePad
        $0.clearButtonMode = .whileEditing // 编辑时显示清除按钮
        $0.textColor = color_3D3E40
        $0.font = .systemFont(ofSize: 14, weight: .regular)
    }
    
    lazy var verifyCodeTextField = UITextField().then {
        $0.placeholder = "请输入验证码"
        $0.keyboardType = .numberPad
        $0.textColor = color_3D3E40
        $0.font = .systemFont(ofSize: 14, weight: .regular)
    }
    
    
    lazy var sendCodeButton = UIButton(type: .system).then {
        $0.setTitle("获取验证码", for: .normal)
        $0.setTitleColor(color_blue, for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
    }
    
    lazy var loginButton = BaseGradientButton().then {
        $0.setTitle("登录", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        $0.backgroundColor = UIColor(hexString: "667BFF")
        $0.gradientColors = [color_8D9096, color_999DA1]
        $0.gradientDirection = .leftToRight
        $0.layer.cornerRadius = 24
        $0.layer.masksToBounds = true
    }
    
    lazy var privacyButton = UIButton().then {
        $0.backgroundColor = UIColor.green
        $0.layer.cornerRadius = 13.5 / 2
        $0.layer.masksToBounds = true
    }
    
    lazy var privacyLabel = ActiveLabel().then {
        let customType1 = ActiveType.custom(pattern: "《用户协议》")
        let customType = ActiveType.custom(pattern: "《隐私政策》")
        $0.enabledTypes.append(customType)
        $0.enabledTypes.append(customType1)
        $0.numberOfLines = 1
        $0.text = "我已阅读并同意《用户协议》《隐私政策》"
        $0.font = .systemFont(ofSize: 12, weight: .light)
        $0.customColor[customType] = .white
        $0.customColor[customType1] = .white
        $0.textColor = color_FFFFFF80
        $0.handleCustomTap(for: customType, handler: { [weak self] _ in
            guard let self = self else { return }
            
        })
        $0.handleCustomTap(for: customType1, handler: { [weak self] _ in
            guard let self = self else { return }
            
        })
    }
    
    // 微信登录视图
    lazy var wechatLoginView = WechatLoginView().then {
        $0.delegate = self
    }
}

// MARK: - WechatLoginViewDelegate

extension LoginView: WechatLoginViewDelegate {
    func wechatLoginViewDidTapWechatButton(_ view: WechatLoginView) {
        delegate?.loginViewDidTapWechatLoginButton(self)
    }
}
