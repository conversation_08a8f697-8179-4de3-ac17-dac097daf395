import Foundation
import SmartCodable
import Combine

/// 用户模型
struct UserModel: SmartCodable {
    // MARK: - 核心标识
    /// 用户ID（默认值防崩溃）
    var id: String = ""
    /// 用户编号（格式示例：00000121）
    var uid: String = ""
    
    // MARK: - 账号信息
    /// 用户昵称（默认空字符串）
    var nickname: String = ""
    /// 手机号码（格式示例：15642947778）
    var phone: String = ""
    /// MD5加密的密码（示例：e10adc3949ba59abbe56e057f20f883e）
    var password: String = ""
    
    // MARK: - 微信绑定
    /// 微信开放平台唯一标识（未绑定默认为空）
    var openid: String = ""
    /// 微信UnionID（跨应用唯一标识，默认0表示未绑定）
    var unionid: Int = 0
    
    // MARK: - 个人资料
    /// 性别：1男 2女 3其他 0未设置（默认0）
    var gender: Int = 1
    /// 头像URL地址（默认空字符串）
    var avatar: String = ""
    /// 用户位置（如"辽宁省葫芦岛市"）
    var location: String = ""
    /// 滑雪年限（单位：年，默认0）
    var ski_age: String = ""
    /// 个人简介/签名（默认空字符串）
    var description: String = ""
    /// 关注数（默认空字符串）
    var follow_count: String = ""
    /// 粉丝数（默认空字符串）
    var fans_count: String = ""
    // MARK: - 账户状态
    /// 广告主标识：1是 2不是（默认2-非广告主）
    var is_advertiser: Int = 2
    
    // MARK: - 时间记录
    /// 账户创建时间（格式：yyyy-MM-dd HH:mm:ss）
    var created_at: String = "1970-01-01 00:00:00"
    /// 最后资料更新时间
    var updated_at: String = "1970-01-01 00:00:00"
    /// 最后登录时间
    var last_login_at: String = "1970-01-01 00:00:00"
}

/// 用户管理器
/// 负责用户信息的存储、获取和更新
class UserManager {
    // MARK: - 单例
    
    static let shared = UserManager()
    
    // MARK: - 属性
    
    /// 当前用户信息
    private var currentUser: UserModel?
    
    /// 存储取消令牌
    private var cancellables = Set<AnyCancellable>()
    
    /// 用户信息存储键
    private let userInfoKey = "currentUserInfo"
    
    // MARK: - 初始化
    
    private init() {
        currentUser = getCurrentUser()
    }
    
    // MARK: - 公共方法
    
    /// 是否已登录
    var isLoggedIn: Bool {
        return TokenManager.shared.accessToken != nil && currentUser != nil
    }
    
    /// 获取当前用户
    func getCurrentUser() -> UserModel? {
        if currentUser == nil {
            loadUserFromLocal()
        }
        return currentUser
    }
    
    /// 保存用户信息
    func saveUser(_ user: UserModel) {
        currentUser = user
        saveUserToLocal(user)
    }
    
    /// 清除用户信息
    func clearUserInfo() {
        currentUser = nil
        UserDefaults.standard.removeObject(forKey: userInfoKey)
        TokenManager.shared.clearTokens()
    }
    /// 重新获取用户信息以更新关注数和粉丝数
    func refreshUserInfo() {
        guard let currentUser = UserManager.shared.getCurrentUser() else { return }

        // 重新获取用户信息
        UserManager.shared.fetchUserInfo(uid: currentUser.id) { success in
            if success {
                print("用户信息更新成功")
            } else {
                print("用户信息更新失败")
            }
        }
    }
    /// 获取用户信息
    /// - Parameters:
    ///   - uid: 用户ID
    ///   - completion: 完成回调
    func fetchUserInfo(uid: String, completion: @escaping (Bool) -> Void) {
        let params: [String: Any] = ["userId": uid]

        CombineNetworkManager.shared.requestModel(LoginService.getUserInfo(params: params), type: UserModel.self)
            .sink { completionResult in
                if case .failure = completionResult {
                    completion(false)
                }
            } receiveValue: { [weak self] user in
                self?.saveUser(user)
                // 发送通知，通知UI更新
                NotificationCenter.default.post(name: AppNotifications.userFollowCountUpdated, object: nil)
                completion(true)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 私有方法
    
    /// 从本地加载用户信息
    private func loadUserFromLocal() {
        if let jsonString = UserDefaults.standard.string(forKey: userInfoKey) {
            currentUser = UserModel.deserialize(from: jsonString)
        }
    }
    
    /// 保存用户信息到本地
    private func saveUserToLocal(_ user: UserModel) {
        if let jsonData = try? JSONEncoder().encode(user),
           let jsonString = String(data: jsonData, encoding: .utf8)
        {
            UserDefaults.standard.set(jsonString, forKey: userInfoKey)
        }
    }
}
