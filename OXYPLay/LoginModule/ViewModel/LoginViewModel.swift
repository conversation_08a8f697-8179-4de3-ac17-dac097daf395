//
//  LoginViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2023/6/10.
//

import Foundation
import Combine
import SmartCodable

/// 登录视图模型
class LoginViewModel: BaseViewModel {
    // MARK: - 输入
    
    /// 用户名/手机号
    @Published var phone: String = ""
    
    /// 密码
    @Published var password: String = ""
    
    /// 验证码
    @Published var code: String = ""
    
    // MARK: - 输出
    
    /// 用户信息
    @Published private(set) var userInfo: UserModel? = nil
    
    /// 登录结果
    let loginResultPublisher = PassthroughSubject<Bool, Never>()
    
    /// 发送验证码结果
    let sendCodeResultPublisher = PassthroughSubject<Bool, Never>()
    
    /// 倒计时
    @Published private(set) var countdown: Int = 0
    
    /// 是否为密码登录模式
    @Published private(set) var isPasswordMode: Bool = false
    
    /// 登录按钮是否可用
    @Published private(set) var isLoginButtonEnabled: Bool = false
    
    // MARK: - 初始化
    
    override init() {
        super.init()
    }
    
    // MARK: - 绑定
    
    override func setupBindings() {
        super.setupBindings()
        
        // 监听输入字段变化，更新登录按钮状态
        Publishers.CombineLatest3($phone, $password, $code)
            .map { [weak self] phone, password, code -> Bool in
                guard let self = self else { return false }
                
                if self.isPasswordMode {
                    // 密码登录模式：手机号和密码都不为空
                    return !phone.isEmpty && !password.isEmpty
                } else {
                    // 验证码登录模式：手机号和验证码都不为空
                    return !phone.isEmpty && !code.isEmpty
                }
            }
            .assign(to: &$isLoginButtonEnabled)
    }
    
    // MARK: - 公共方法
    
    /// 切换登录模式（密码/验证码）
    func toggleLoginMode() {
        isPasswordMode.toggle()
    }
    
    /// 密码登录
    func loginWithPassword() {
        guard phone.isValidChinesePhoneNumber else {
            if let view = ScreenInfo.activeWindow{
                MBProgressHUD.showError("请输入正确的手机号", in: view)
            }
            return
        }
        guard !phone.isEmpty && !password.isEmpty else {
            loginResultPublisher.send(false)
            return
        }
        
        let params: [String: Any] = [
            "phone": phone,
            "password": password
        ]
        
        performLogin(with: params)
    }
    
    /// 验证码登录
    func loginWithVerifyCode() {
        guard phone.isValidChinesePhoneNumber else {
            if let view = ScreenInfo.activeWindow{
                MBProgressHUD.showError("请输入正确的手机号", in: view)
            }
            return
        }
        guard !phone.isEmpty && !code.isEmpty else {
            loginResultPublisher.send(false)
            return
        }
        
        let params: [String: Any] = [
            "phone": phone,
            "code": code
        ]
        
        performLogin(with: params)
    }
    
    /// 执行登录请求
    private func performLogin(with params: [String: Any]) {
        requestModel(LoginService.login(params: params), type: AuthResponse.self)
            .sink { [weak self] completion in
                if case .failure(_) = completion {
                    self?.loginResultPublisher.send(false)
                }
            } receiveValue: { [weak self] authResponse in
                // 保存Token
                if let accessToken = authResponse.access_token,
                   let refreshToken = authResponse.refresh_token,
                   let uid = authResponse.uid {
                    TokenManager.shared.updateTokens(accessToken: accessToken, refreshToken: refreshToken)
                    
                    // 获取用户信息
                    self?.getUserInfo(uid: uid)
                } else {
                    self?.loginResultPublisher.send(false)
                }
            }
            .store(in: &cancellables)
    }
    
    /// 发送验证码
    func sendCode() {
        guard phone.isValidChinesePhoneNumber else {
            if let view = ScreenInfo.activeWindow{
                MBProgressHUD.showError("请输入正确的手机号", in: view)
            }
            return
        }
        guard !phone.isEmpty else {
            sendCodeResultPublisher.send(false)
            return
        }
        
        let params: [String: Any] = [
            "phone": phone
        ]
        
        request(LoginService.sendCode(params: params))
            .sink { [weak self] completion in
                if case .failure(_) = completion {
                    self?.sendCodeResultPublisher.send(false)
                }
            } receiveValue: { [weak self] _ in
                self?.sendCodeResultPublisher.send(true)
                self?.startCountdown()
            }
            .store(in: &cancellables)
    }
    
    /// 获取用户信息
    func getUserInfo(uid: Int) {
        let params: [String: Any] = [
            "userId": uid
        ]
        
        requestModel(LoginService.getUserInfo(params: params), type: UserModel.self)
            .sink { [weak self] completion in
                if case .failure(_) = completion {
                    self?.loginResultPublisher.send(false)
                }
            } receiveValue: { [weak self] userModel in
                UserManager.shared.saveUser(userModel)
                self?.userInfo = userModel
                self?.loginResultPublisher.send(true)
            }
            .store(in: &cancellables)
    }
    
    /// 忘记密码
    func forgetPassword() {
        guard !phone.isEmpty && !code.isEmpty && !password.isEmpty else {
            loginResultPublisher.send(false)
            return
        }
        
        let params: [String: Any] = [
            "phone": phone,
            "code": code,
            "password": password
        ]
        
        request(LoginService.forgetPassword(params: params))
            .sink { [weak self] completion in
                if case .failure(_) = completion {
                    self?.loginResultPublisher.send(false)
                }
            } receiveValue: { [weak self] _ in
                self?.loginResultPublisher.send(true)
            }
            .store(in: &cancellables)
    }
    
    /// 微信登录
    func wechatLogin() {
        WechatManager.shared.login { [weak self] userInfo, error in
            guard let self = self,
                  let userInfo = userInfo,
                  let unionId = userInfo["unionid"] as? String,
                  error == nil
            else {
                print("微信登录失败: \(error?.localizedDescription ?? "未知错误")")
                self?.loginResultPublisher.send(false)
                return
            }

            // 打印完整的用户信息
            print("获取到微信用户信息: \(userInfo)")
            print("unionId: \(unionId)")

            // 可以获取其他用户信息
            let nickname = userInfo["nickname"] as? String ?? ""
            let headimgurl = userInfo["headimgurl"] as? String ?? ""
            let openid = userInfo["openid"] as? String ?? ""

            print("用户昵称: \(nickname)")
            print("用户头像: \(headimgurl)")
            print("用户openid: \(openid)")

            // 构建登录参数
            let params: [String: Any] = [
                "unionId": unionId
            ]

            self.performLogin(with: params)
        }
    }
    
    // MARK: - 倒计时
    
    /// 开始倒计时
    private func startCountdown() {
        // 取消现有的倒计时
        stopCountdown()
        
        // 设置初始值
        countdown = 60
        
        // 创建定时器
        let timer = Timer.publish(every: 1, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                guard let self = self else { return }
                
                if self.countdown > 0 {
                    self.countdown -= 1
                } else {
                    self.stopCountdown()
                }
            }
        
        // 存储定时器
        cancellables.insert(timer)
    }
    
    /// 停止倒计时
    private func stopCountdown() {
        // 重置倒计时值
        countdown = 0
    }
} 
