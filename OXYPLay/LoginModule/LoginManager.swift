import UIKit
import ATAuthSDK
import Combine

/// 登录管理器
/// 负责处理应用的登录状态、界面切换和认证流程
class LoginManager {
    // MARK: - 单例
    
    static let shared = LoginManager()
    var mainTabBarVc:MyTabBarController?

    // MARK: - 属性
    
    private var window: UIWindow?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    
    private init() {}
    
    // MARK: - 公共方法
    
    /// 设置登录管理器
    /// - Parameter window: 应用主窗口
    func setup(with window: UIWindow?) {
        self.window = window
        checkLoginStatus()
        setupNotificationObserver()
    }
    
    /// 检查登录状态
    func checkLoginStatus() {
        if isLogin() {
            showMainTabBar()
        } else {
            checkLoginEnvAvailable()
        }
    }
    func isLogin()->Bool{
        return UserManager.shared.isLoggedIn
    }
    
    /// 显示主界面
    func showMainTabBar() {
        if let mainTabBarVc = mainTabBarVc{
            animateRootViewControllerChange(to: mainTabBarVc)
        }else{
            self.mainTabBarVc = MyTabBarController()
            animateRootViewControllerChange(to: self.mainTabBarVc!)
        }
    }
    
    /// 退出登录
    @objc func logout() {
        // 清除用户信息和token
        UserManager.shared.clearUserInfo()
        LoginManager.shared.checkLoginStatus()
    }
    
    // MARK: - 私有方法
    
    /// 显示手机号登录页面
    private func showCodeLoginPage() {
        let loginVC = LoginViewController()
        let nav = UINavigationController(rootViewController: loginVC)
        nav.isNavigationBarHidden = true
        animateRootViewControllerChange(to: nav)
    }
    
    /// 动画切换根视图控制器
    private func animateRootViewControllerChange(to viewController: UIViewController) {
        UIView.transition(with: window!, duration: 0.5, options: .transitionCrossDissolve, animations: {
            self.window?.rootViewController = viewController
        }, completion: nil)
    }
    
    /// 设置通知观察者
    private func setupNotificationObserver() {
        NotificationCenter.default.publisher(for: AppNotifications.NotificationUserDidLogin)
            .sink {[weak self] notification in
                guard let self = self else {return}
                self.userDidLogin()
            }
            .store(in: &cancellables)
        NotificationCenter.default.publisher(for: AppNotifications.userTokenExpired)
            .sink {[weak self] notification in
                guard let self = self else {return}
                self.logout()
            }
            .store(in: &cancellables)
    }
    
    /// 用户登录成功的通知处理
    @objc private func userDidLogin() {
        showMainTabBar()
    }
    
}

// MARK: - 一键登录方法

extension LoginManager {
    /// 检查环境是否支持一键登录
    func checkLoginEnvAvailable(){
        TXCommonHandler.sharedInstance().checkEnvAvailable(with: .loginToken) { [weak self] resultDic in
            guard let self = self else { return }
            
            // 直接比较resultCode值，不做类型转换
            let isSupported = resultDic?["resultCode"] as? NSNumber == 600000 ||
            resultDic?["resultCode"] as? String == "600000"
            
            if isSupported {
                self.accelerateLoginPage()
            } else {
                self.showCodeLoginPage()
            }
        }
    }
    
    /// 加速一键登录调起
    func accelerateLoginPage(){
        TXCommonHandler.sharedInstance().accelerateLoginPage(withTimeout: 3.0) { [weak self] resultDic in
            guard let self = self else { return }
            self.startQuickLogin()
        }
    }
    
    /// 启动一键登录流程
    func startQuickLogin() {
        // 弹出授权页面获取Token
        TXCommonHandler.sharedInstance().getLoginToken(
            withTimeout: 3.0,
            controller: UIViewController.getCurrentViewController()!,
            model: QuickLoginView()
        ) { [weak self] resultDic in
            guard let self = self else { return }
            
            // 检查结果代码
            if resultDic["resultCode"] as? NSNumber == 600000 || resultDic["resultCode"] as? String == "600000" {
                // 成功获取token
                if let token = resultDic["token"] as? String {
                    let params = ["alToken": token]
                    self.requestLoginWithParams(params: params)
                }
            }else if resultDic["resultCode"] as? NSNumber == 600002 || resultDic["resultCode"] as? String == "600002" || resultDic["resultCode"] as? NSNumber == 700001 || resultDic["resultCode"] as? String == "700001" {
                self.showCodeLoginPage()
            }else {
                self.showCodeLoginPage()

            }
        }
    }
    
    /// 使用参数请求登录
    /// - Parameter params: 登录参数
    func requestLoginWithParams(params: [String: Any]) {
        CombineNetworkManager.shared.requestModel(LoginService.login(params: params), type: AuthResponse.self)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    print("登录失败: \(error.localizedDescription)")
                }
            } receiveValue: { [weak self] authResponse in
                guard let self = self,
                      let uid = authResponse.uid,
                        let accessToken = authResponse.access_token,
                        let refreshToken = authResponse.refresh_token else {
                    return
                }
                
                // 保存令牌
                TokenManager.shared.updateTokens(accessToken: accessToken, refreshToken: refreshToken)
                
                // 获取用户信息
                UserManager.shared.fetchUserInfo(uid: String(uid)) { success in
                    if success {
                      
                        TXCommonHandler.sharedInstance().cancelLoginVC(animated: true)
                        NotificationCenter.default.post(name: AppNotifications.NotificationUserDidLogin, object: nil)
                    } else {
                        // 登录失败处理
                        print("获取用户信息失败")
                    }
                }
            }
            .store(in: &cancellables)
    }
}
