import UIKit
import SnapKit

// MARK: - OXYTabBar代理协议
@objc public protocol OXYTabBarDelegate: NSObjectProtocol {
    func tabBarDidSelectMiddleButton()
}

// 中间按钮尺寸常量
fileprivate let centerWidth = 50

@objcMembers
public class OXYTabBar: UITabBar {
    weak public var customDelegate: OXYTabBarDelegate?
    
    // 圆角大小
    private let cornerRadius: CGFloat = 20
    
    // 背景图片视图
    private lazy var addImageView: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "tabbar_background"))
        imageView.contentMode = .scaleAspectFill
        return imageView
    }()
    
    // 中间按钮视图
    fileprivate lazy var centerView: OXYTabBarCenterView = {
        let centerView = OXYTabBarCenterView()
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(viewTapped))
        centerView.addGestureRecognizer(tapGesture)
        return centerView
    }()
    
    // 自定义背景视图，用于添加圆角和阴影
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: -3)
        view.layer.shadowOpacity = 0.1
        view.layer.shadowRadius = 6
        return view
    }()
    
    // MARK: - 初始化
    override public init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI设置
    private func setupUI() {
        // 添加子视图
        insertSubview(backgroundView, at: 0)
        addSubview(addImageView)
        addSubview(centerView)
        
        // 配置TabBar样式
        barTintColor = .clear
        backgroundColor = .clear
        barStyle = .default
        shadowImage = UIImage() // 移除默认阴影线
        backgroundImage = UIImage() // 移除默认背景
        isTranslucent = true
        
        // 设置TabBar图标颜色
        tintColor = UIColor(hexString: "#2A6CFF") // 选中颜色
        unselectedItemTintColor = UIColor(hexString: "#999999") // 未选中颜色
    }
    
    // 中间按钮点击事件
    @objc func viewTapped() {
        customDelegate?.tabBarDidSelectMiddleButton()
    }
    
    // MARK: - 布局
    public override func layoutSubviews() {
        super.layoutSubviews()
        
        // 设置背景视图尺寸和位置
        backgroundView.frame = bounds
        
        // 添加圆角
        let maskPath = UIBezierPath(roundedRect: backgroundView.bounds,
                                   byRoundingCorners: [.topLeft, .topRight],
                                   cornerRadii: CGSize(width: cornerRadius, height: cornerRadius))
        
        let maskLayer = CAShapeLayer()
        maskLayer.path = maskPath.cgPath
        backgroundView.layer.mask = maskLayer
        
        // 调整TabBar按钮布局
        adjustTabBarButtonLayout()
        
        // 设置中间按钮位置
        let tabBarWidth: CGFloat = bounds.size.width
        centerView.frame = CGRect(x: tabBarWidth * 0.5 - CGFloat(centerWidth) * 0.5, 
                                 y: -15, 
                                 width: CGFloat(centerWidth), 
                                 height: CGFloat(centerWidth))
        bringSubviewToFront(centerView)
        addImageView.snp.makeConstraints { make in
            make.bottom.equalTo(self.snp.top)
            make.centerX.equalToSuperview()
        }
    }
    
    // 调整TabBar按钮布局
    private func adjustTabBarButtonLayout() {
        // 获取所有TabBar按钮
        var tabBarButtons = [UIView]()
        for subView in subviews {
            if subView.isKind(of: NSClassFromString("UITabBarButton")!) {
                tabBarButtons.append(subView)
            }
        }
        
        // 如果有5个按钮（包括中间占位），则需要调整布局
        if tabBarButtons.count == 5 {
            let tabBarWidth = bounds.size.width
            let buttonWidth = tabBarWidth / 5
            
            for (index, button) in tabBarButtons.enumerated() {
                var frame = button.frame
                
                // 调整按钮位置，跳过中间按钮
                if index < 2 {
                    // 左侧两个按钮
                    frame.origin.x = buttonWidth * CGFloat(index)
                } else if index == 2 {
                    // 中间占位按钮隐藏
                    button.isHidden = true
                } else {
                    // 右侧两个按钮
                    frame.origin.x = buttonWidth * CGFloat(index)
                }
                
                button.frame = frame
            }
        }
    }
    
    // MARK: - 事件处理
    // 重写hitTest方法，让超出tabBar部分也能响应事件
    public override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        // 先检查是否可以响应事件
        if !isUserInteractionEnabled || isHidden || alpha <= 0.01 {
            return nil
        }
        
        // 检查中间按钮区域
        let centerButtonFrame = centerView.frame
        // 扩大点击区域
        let extendedFrame = CGRect(
            x: centerButtonFrame.origin.x - 10,
            y: centerButtonFrame.origin.y - 10,
            width: centerButtonFrame.width + 20,
            height: centerButtonFrame.height + 20
        )
        
        if extendedFrame.contains(point) {
            return centerView
        }
        
        // 默认处理
        return super.hitTest(point, with: event)
    }
}

// MARK: - 中间按钮视图
class OXYTabBarCenterView: UIControl {
    // 按钮图片视图
    private lazy var imageView: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "tabbar_add"))
        imageView.contentMode = .scaleAspectFit // 使用scaleAspectFit确保图片正确显示
        imageView.isUserInteractionEnabled = false
        return imageView
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        configUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func configUI() {
        isUserInteractionEnabled = true
        backgroundColor = .clear
        addSubview(imageView)
        imageView.frame = CGRect(x: 0, y: 12, width: centerWidth, height: centerWidth)
        
        // 居中显示图片
        imageView.center = CGPoint(x: centerWidth / 2, y: centerWidth / 2 + 6)
    }
    
    // 确保图片居中显示
    override func layoutSubviews() {
        super.layoutSubviews()
        imageView.center = CGPoint(x: bounds.width / 2, y: bounds.height / 2 + 6) // 稍微向下偏移
    }
} 
