import UIKit

class CenterTabBarButton: UIButton {
    
    private let innerShadowAlpha: CGFloat = 0.15
    private let gradientColors: [CGColor] = [
        UIColor(hexString: "4D7FFF")!.cgColor,
        UIColor(hexString: "2A6CFF")!.cgColor
    ]
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupButton()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupButton()
    }
    
    // MARK: - 设置按钮
    
    private func setupButton() {
      
        // 设置圆角和阴影
        layer.cornerRadius = frame.height / 2
        layer.masksToBounds = false
        
        // 外阴影
        layer.shadowColor = UIColor.black.cgColor
        layer.shadowOffset = CGSize(width: 0, height: 2)
        layer.shadowRadius = 6
        layer.shadowOpacity = 0.2
        
        // 添加渐变背景
        setupGradientBackground()
    }
    
    private func setupGradientBackground() {
        // 创建渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = bounds
        gradientLayer.colors = gradientColors
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.cornerRadius = frame.height / 2
        
        // 创建一个形状图层作为渐变的容器
        let shapeLayer = CAShapeLayer()
        shapeLayer.path = UIBezierPath(ovalIn: bounds).cgPath
        
        // 将渐变层设置为形状图层的填充
        gradientLayer.mask = shapeLayer
        
        // 添加到按钮的图层
        layer.insertSublayer(gradientLayer, at: 0)
        
        // 添加内阴影效果
        addInnerShadow()
    }
    
    private func addInnerShadow() {
        // 创建内阴影图层
        let innerShadowLayer = CALayer()
        innerShadowLayer.frame = bounds
        innerShadowLayer.cornerRadius = frame.height / 2
        innerShadowLayer.masksToBounds = true
        
        // 创建一个比按钮大的矩形路径
        let shadowPath = CGPath(rect: bounds.insetBy(dx: -10, dy: -10), transform: nil)
        
        // 创建内阴影形状
        let innerShadow = CAShapeLayer()
        innerShadow.frame = bounds
        innerShadow.backgroundColor = UIColor.clear.cgColor
        innerShadow.shadowColor = UIColor.black.cgColor
        innerShadow.shadowOffset = CGSize.zero
        innerShadow.shadowOpacity = Float(innerShadowAlpha)
        innerShadow.shadowRadius = 3
        innerShadow.fillRule = .evenOdd
        
        // 创建路径
        let path = CGMutablePath()
        path.addPath(shadowPath)
        path.addPath(CGPath(ellipseIn: bounds, transform: nil))
        
        innerShadow.path = path
        
        // 添加内阴影图层
        layer.addSublayer(innerShadowLayer)
        innerShadowLayer.addSublayer(innerShadow)
    }
    
    // MARK: - 交互反馈
    
    override var isHighlighted: Bool {
        didSet {
            UIView.animate(withDuration: 0.1) {
                self.transform = self.isHighlighted ? 
                    CGAffineTransform(scaleX: 0.9, y: 0.9) : 
                    .identity
            }
        }
    }
    
    // 设置图标居中
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // 确保图片居中
        if let imageView = self.imageView {
            imageView.contentMode = .center
            imageView.frame = bounds
        }
        
        // 更新渐变层尺寸
        for layer in layer.sublayers ?? [] {
            if layer is CAGradientLayer {
                layer.frame = bounds
                layer.cornerRadius = bounds.height / 2
            }
        }
    }
} 
