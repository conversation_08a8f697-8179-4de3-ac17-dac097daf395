//
//  MyTabBarController.swift
//  OXYPLay
//
//  Created by RenOXY on 2025/5/22.
//

import UIKit
import SnapKit

class MyTabBarController: UITabBarController {
    
    // 保存上一次选中的索引
    private var lastSelectedIndex: Int = 0
    
    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        // 设置自定义TabBar
        let customTabBar = OXYTabBar()
        customTabBar.customDelegate = self
        self.setValue(customTabBar, forKey: "tabBar")
        
        // 设置代理以监听选项卡切换
        delegate = self
        
        // 默认选中首页
        selectedIndex = 0
        setupViewControllers()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
    }
    lazy var home = BaseNavigationController(rootViewController: HomeViewController())
    // MARK: - 设置视图控制器
    private func setupViewControllers() {
        
        home.tabBarItem = createTabBarItem(normalImage: "home_normal", selectedImage: "home_highlight")
        
        let connection = BaseNavigationController(rootViewController: AssemblyViewController())
        connection.tabBarItem = createTabBarItem(normalImage: "jijie_normal", selectedImage: "jijie_highlight")
        
        let message = BaseNavigationController(rootViewController: MessageViewController())
        message.tabBarItem = createTabBarItem(normalImage: "message_normal", selectedImage: "message_highlight")
        
        let personal = BaseNavigationController(rootViewController: MineViewController())
        personal.tabBarItem = createTabBarItem(normalImage: "mine_normal", selectedImage: "mine_highlight")
        
        // 设置中间的占位控制器
        let placeholder = UIViewController()
        placeholder.tabBarItem = UITabBarItem(title: "", image: nil, selectedImage: nil)
        placeholder.tabBarItem.isEnabled = false
        
        viewControllers = [home, connection, placeholder, message, personal]
    }
    
    // 创建保留原始图片渲染的TabBarItem
    private func createTabBarItem( normalImage: String, selectedImage: String) -> UITabBarItem {
        let item = UITabBarItem(title: "", image: nil, selectedImage: nil)
        
        if let normalImg = UIImage(named: normalImage) {
            item.image = normalImg.withRenderingMode(.alwaysOriginal)
        }
        
        if let selectedImg = UIImage(named: selectedImage) {
            item.selectedImage = selectedImg.withRenderingMode(.alwaysOriginal)
        }
        
        return item
    }
}

// MARK: - OXYTabBarDelegate
extension MyTabBarController: OXYTabBarDelegate {
    func tabBarDidSelectMiddleButton() {
        // 检查登录状态
        if LoginManager.shared.isLogin(){
            let publish = BaseNavigationController(rootViewController: PublishViewController())
            publish.modalPresentationStyle = .fullScreen
            self.present(publish, animated: true)
        }else{
            LoginManager.shared.checkLoginStatus()
        }
        
    }
}

// MARK: - UITabBarControllerDelegate
extension MyTabBarController: UITabBarControllerDelegate {
    func tabBarController(_ tabBarController: UITabBarController, shouldSelect viewController: UIViewController) -> Bool {
        // 如果是中间的占位控制器，不允许选中
        if let index = tabBarController.viewControllers?.firstIndex(of: viewController), index == 2 {
            return false
        }
        
        // 如果是当前已选中的控制器，不触发选中事件
        if viewController == tabBarController.selectedViewController {
            return false
        }
        
        return true
    }
    
    func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
        guard let index = tabBarController.viewControllers?.firstIndex(of: viewController) else { return }
                
        // 添加动画
        TabBarAnimationManager.shared.addAnimationToTabBarButton(tabBar: tabBar, at: index)
        
        // 更新上一次选中的索引
        lastSelectedIndex = index
    }
}



