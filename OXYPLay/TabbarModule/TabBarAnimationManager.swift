import UIKit

/// TabBar动画管理器，负责处理TabBar项的动画效果
class TabBarAnimationManager {
    
    /// 单例实例
    static let shared = TabBarAnimationManager()
    
    /// 私有初始化方法，确保单例模式
    private init() {}
    
    /// 为TabBar按钮添加动画效果
    /// - Parameters:
    ///   - tabBar: TabBar实例
    ///   - index: 按钮索引
    func addAnimationToTabBarButton(tabBar: UITabBar, at index: Int) {
        guard let tabBarButton = getTabBarButton(tabBar: tabBar, at: index) else { return }
        
        // 缩放动画
        let scaleAnimation = CAKeyframeAnimation(keyPath: "transform.scale")
        scaleAnimation.values = [1.0, 1.2, 0.9, 1.0]
        scaleAnimation.keyTimes = [0, 0.3, 0.6, 1.0]
        scaleAnimation.duration = 0.3
        
        // 旋转动画
        let rotationAnimation = CAKeyframeAnimation(keyPath: "transform.rotation.z")
        rotationAnimation.values = [0, 0.1, -0.1, 0]
        rotationAnimation.keyTimes = [0, 0.3, 0.6, 1.0]
        rotationAnimation.duration = 0.3
        
        // 组合动画
        let animationGroup = CAAnimationGroup()
        animationGroup.animations = [scaleAnimation, rotationAnimation]
        animationGroup.duration = 0.3
        animationGroup.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        
        // 应用动画
        tabBarButton.layer.add(animationGroup, forKey: "tabBarButtonAnimation")
        
        // 触感反馈
        if #available(iOS 10.0, *) {
            let generator = UISelectionFeedbackGenerator()
            generator.prepare()
            generator.selectionChanged()
        }
    }
    
    /// 获取TabBar中的指定item视图
    /// - Parameters:
    ///   - tabBar: TabBar实例
    ///   - index: 按钮索引
    /// - Returns: 对应的按钮视图，如果不存在则返回nil
    private func getTabBarButton(tabBar: UITabBar, at index: Int) -> UIView? {
        let tabBarButtons = tabBar.subviews.filter { subview in
            return subview.isKind(of: NSClassFromString("UITabBarButton")!)
        }
        
       // 确保索引有效
        guard index >= 0, index < tabBarButtons.count else { return nil }
        return tabBarButtons[index]
    }
} 
