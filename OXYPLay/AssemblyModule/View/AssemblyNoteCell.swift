import SnapKit
import UIKit

/// 集结笔记单元格代理协议
protocol AssemblyNoteCellDelegate: AnyObject {
    /// 点赞按钮点击事件
    /// - Parameter cell: 点击事件发生的单元格
    func assemblyNoteCellDidTapLike(_ cell: AssemblyNoteCell)

    /// 评论按钮点击事件
    /// - Parameter cell: 点击事件发生的单元格
    func assemblyNoteCellDidTapComment(_ cell: AssemblyNoteCell)
}

/// 集结笔记列表单元格
class AssemblyNoteCell: UITableViewCell {
    // MARK: - 属性

    /// 重用标识符
    static let identifier = "AssemblyNoteCell"

    /// 代理
    weak var delegate: AssemblyNoteCellDelegate?

    /// 笔记模型（保存引用以便在代理方法中使用）
    private var noteModel: RecommendModel?

    // MARK: - UI组件

    /// 标签视图 - "找人"
    private lazy var tagLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textAlignment = .center
        label.layer.cornerRadius = 10
        label.layer.masksToBounds = true
        return label
    }()

    /// 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor.black
        label.numberOfLines = 1
        return label
    }()

    /// 内容标签
    private lazy var contentLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor.darkGray
        label.numberOfLines = 0
        return label
    }()

    /// 图片容器视图
    private lazy var imagesContainerView: UIView = {
        let view = UIView()
        return view
    }()
    private lazy var locationButton = BaseButton().then {
        $0.backgroundColor = UIColor(hexString: "3D3E40", transparency: 0.06)
        $0.setTitleColor(UIColor(hexString: "3D3E40", transparency: 0.48), for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 12, weight: .regular)
        $0.isRounded = true
        $0.setImage(UIImage(named: "assembly_location")?.withTintColor(UIColor(hexString: "3D3E40")!), for: .normal)
        $0.spacing = 5
    }

    /// 收藏按钮
    private lazy var favoriteButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "assembly_collection"), for: .normal)
        button.setImage(UIImage(named: "assembly_collection_select"), for: .selected)
        button.setTitleColor(UIColor(hexString: "3D3E40", transparency: 0.8), for: .normal)
        button.addTarget(self, action: #selector(handleLikeTap), for: .touchUpInside)
        button.titleLabel?.font = .systemFont(ofSize: 13, weight: .regular)
        return button
    }()

    /// 评论按钮
    private lazy var commentButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "assembly_comment"), for: .normal)
        button.setTitleColor(UIColor(hexString: "3D3E40", transparency: 0.8), for: .normal)
        button.addTarget(self, action: #selector(handleCommentTap), for: .touchUpInside)
        button.titleLabel?.font = .systemFont(ofSize: 13, weight: .regular)
        return button
    }()

    /// 用户头像
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 16
        imageView.layer.masksToBounds = true
        imageView.layer.borderWidth = 0.5
        imageView.layer.borderColor = UIColor(white: 0.9, alpha: 1.0).cgColor
        imageView.backgroundColor = UIColor(white: 0.95, alpha: 1.0)
        return imageView
    }()

    /// 用户名标签
    private lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.textColor = UIColor.black
        return label
    }()

    /// 时间标签
    private lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor.lightGray
        label.textAlignment = .right
        return label
    }()

    /// 时间标签
    private lazy var topView: UIView = {
        let label = UIView()
        label.backgroundColor = .white
        label.layer.cornerRadius = 12
        label.layer.masksToBounds = true
        return label
    }()

    // MARK: - 初始化方法

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func layoutSubviews() {
        super.layoutSubviews()
    }

    // MARK: - UI设置

    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear
        contentView.addSubview(topView)

        // 添加子视图
        topView.addSubview(tagLabel)
        topView.addSubview(titleLabel)
        topView.addSubview(contentLabel)
        topView.addSubview(imagesContainerView)

        topView.addSubview(locationButton)
        topView.addSubview(favoriteButton)
        topView.addSubview(commentButton)
        topView.addSubview(avatarImageView)
        topView.addSubview(usernameLabel)
        topView.addSubview(timeLabel)

        // 设置约束
        setupConstraints()
    }

    private func setupConstraints() {
        topView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.equalTo(0)
            make.bottom.equalTo(-12)
        }
        // 找人标签
        tagLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalToSuperview().offset(12)
            make.height.equalTo(20)
            make.width.equalTo(tagLabel.intrinsicContentSize.width + 10)
        }

        // 标题
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(tagLabel.snp.right).offset(8)
            make.centerY.equalTo(tagLabel)
            make.right.lessThanOrEqualTo(-12)
        }
        // 用户头像和用户名
        avatarImageView.snp.makeConstraints { make in
            make.left.equalTo(tagLabel)
            make.top.equalTo(tagLabel.snp.bottom).offset(10)
            make.width.height.equalTo(32)
        }

        usernameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.centerY.equalTo(avatarImageView)
        }

        // 时间标签
        timeLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalTo(usernameLabel)
        }
        // 内容
        contentLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalTo(avatarImageView.snp.bottom).offset(10)
            make.right.equalToSuperview().offset(-12)
        }

        // 图片容器
        imagesContainerView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalTo(contentLabel.snp.bottom).offset(10)
            make.right.equalToSuperview().offset(-12)
        }

        // 位置相关
        locationButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalTo(imagesContainerView.snp.bottom).offset(10)
            make.width.equalTo(locationButton.intrinsicContentSize.width + 10)
            make.bottom.equalTo(-16).priority(.low)
            make.height.equalTo(19)
        }

        // 收藏和评论按钮
        commentButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalTo(locationButton)
        }

        favoriteButton.snp.makeConstraints { make in
            make.right.equalTo(commentButton.snp.left).offset(-16)
            make.centerY.equalTo(commentButton)
        }
    }

    // MARK: - 事件处理

    @objc private func handleLikeTap() {
        delegate?.assemblyNoteCellDidTapLike(self)
    }

    @objc private func handleCommentTap() {
        delegate?.assemblyNoteCellDidTapComment(self)
    }


    // MARK: - 公共方法

    /// 配置单元格（基于模型）
    /// - Parameter note: 集结笔记模型
    func configure(with note: RecommendModel) {
        // 保存笔记模型引用
        noteModel = note

        // 设置标签类型
        tagLabel.text = note.recommendType.displayName
        tagLabel.textColor = note.recommendType.displayTextColor
        tagLabel.backgroundColor = note.recommendType.displayBackColor

        // 设置标题和内容
        titleLabel.text = note.title
        contentLabel.text = note.description

        // 设置位置
        locationButton.setTitle(note.location, for: .normal)
        
        imagesContainerView.subviews.forEach{$0.removeFromSuperview()}
        let imageArray = Array(note.img_urls.prefix(2))
        if imageArray.isEmpty{
            imagesContainerView.isHidden = true
        }else{
            imagesContainerView.isHidden = false
            imageArray.forEach { url in
                let imageView = UIImageView()
                imageView.backgroundColor = .random
                imageView.layer.cornerRadius = 5
                imageView.masksToBounds = true
                imagesContainerView.addSubview(imageView)
            }
            imagesContainerView.subviews.snp.distributeSudokuViews(fixedItemWidth: 100, fixedItemHeight: 100, warpCount: 3)
        }
        // 设置收藏和评论数量
        favoriteButton.setTitle(" \(note.favorite_count)", for: .normal)
        commentButton.setTitle(" \(note.comment_count)", for: .normal)
        favoriteButton.isSelected = note.is_favorited
        // 设置用户信息
        usernameLabel.text = note.user?.nickname
        timeLabel.text = note.created_at.formatTimeAgo
        // 设置头像
        if let avatarUrl = note.user?.avatar, let url = URL(string: avatarUrl) {
            avatarImageView.kf.setImage(with: url, placeholder: UIImage(systemName: "person.circle.fill"))
        }
        // 设置头像
        
        tagLabel.snp.updateConstraints { make in
            make.width.equalTo(tagLabel.intrinsicContentSize.width + 10)
        }
        locationButton.snp.updateConstraints { make in
            make.width.equalTo(locationButton.intrinsicContentSize.width + 10)
        }
    }

    /// 获取当前单元格的笔记模型
    /// - Returns: 笔记模型
    func getNote() -> RecommendModel? {
        return noteModel
    }
}
