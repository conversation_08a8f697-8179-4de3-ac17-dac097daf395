import SnapKit
import UIKit
import Then

/// 筛选视图代理协议
protocol AssemblyFilterViewDelegate: AnyObject {
    /// 笔记类型选择事件
    /// - Parameters:
    ///   - filterView: 触发事件的筛选视图
    ///   - type: 选择的笔记类型
    func assemblyFilterView(_ filterView: BaseView, didSelectNoteType type: NoteType)

    /// 地区选择事件
    /// - Parameters:
    ///   - filterView: 触发事件的筛选视图
    ///   - region: 选择的地区
    func assemblyFilterView(_ filterView: BaseView, didSelectRegion region: RegionVenueItemModel)

    /// 场地选择事件
    /// - Parameters:
    ///   - filterView: 触发事件的筛选视图
    ///   - venue: 选择的场地
    func assemblyFilterView(_ filterView: BaseView, didSelectVenue venue: VenueItemModel)
}

/// 筛选视图组件
class AssemblyFilterView: BaseView {
    // MARK: - 属性

    /// 代理
    weak var delegate: AssemblyFilterViewDelegate?

    private var regions: [RegionVenueItemModel] = []
    private var venues: [VenueItemModel] = []
    private var selectedRegionIndex: Int = 0
    private var selectedVenueIndex: Int = 0

    // MARK: - UI组件

    // 笔记类型区域
    private lazy var noteTypeGradientView = UIView().then{
        $0.backgroundColor = color_00000004
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
    }
    private lazy var blueGradientView = UIView().then{
        $0.backgroundColor = color_blue
    }
    private lazy var noteTypeTitleView = NoteTypeTitleView()
    private lazy var noteTypeFilterView = FilterScrollView(type: .noteType)
    
    // 地区和场地区域
    private lazy var locationInfoView = LocationInfoView()
    private lazy var regionFilterView = FilterScrollView(type: .region)
    private lazy var venueFilterView = FilterScrollView(type: .venue)
    
    private lazy var regionTitle = UILabel().then {
        $0.text = "选择地区"
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = color_3D3E40
    }

    private lazy var venueTitle = UILabel().then {
        $0.text = "选择场地"
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = color_3D3E40
    }
    private let titlebottomImageView = UIImageView().then {
        $0.image = UIImage(named: "assembly_list_title")
    }
    
    private lazy var whiteBackgroundView = UIView().then {
        $0.backgroundColor = color_F6F8F9
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = false
        $0.layer.maskedCorners = .layerMaxXMinYCorner

    }
    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        loadData()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func layoutSubviews() {
        super.layoutSubviews()
    }

    // MARK: - 配置方法

    override func configUI() {
        self.backgroundColor = color_F6F8F9
        self.addSubview(blueGradientView)
        self.addSubview(noteTypeTitleView)
        self.addSubview(noteTypeGradientView)
        noteTypeGradientView.addSubview(noteTypeFilterView)
        self.addSubview(whiteBackgroundView)
        whiteBackgroundView.addSubview(regionTitle)
        whiteBackgroundView.addSubview(venueTitle)
        whiteBackgroundView.addSubview(locationInfoView)
        whiteBackgroundView.addSubview(regionFilterView)
        whiteBackgroundView.addSubview(venueFilterView)
        self.addSubview(titlebottomImageView)
    }
    override func configLayout() {
        blueGradientView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(whiteBackgroundView.snp.top).offset(50)
        }
        noteTypeTitleView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(0)
        }
        noteTypeGradientView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(noteTypeTitleView.snp.bottom).offset(12)
            make.height.equalTo(80)
        }
        noteTypeFilterView.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.right.equalTo(0)
            make.top.equalTo(0)
            make.height.equalTo(66)
        }
        whiteBackgroundView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(noteTypeGradientView).offset(66)
        }
        regionTitle.snp.makeConstraints { make in
            make.left.top.equalTo(12)
        }
        locationInfoView.snp.makeConstraints { make in
            make.centerY.equalTo(regionTitle)
            make.right.equalTo(-12)
        }
        regionFilterView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.equalTo(regionTitle.snp.bottom).offset(12)
            make.height.equalTo(24)
        }
        venueTitle.snp.makeConstraints { make in
            make.top.equalTo(regionFilterView.snp.bottom).offset(12)
            make.left.equalTo(12)
        }
        venueFilterView.snp.makeConstraints { make in
            make.top.equalTo(venueTitle.snp.bottom).offset(12)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(24)
            make.bottom.equalTo(-12)
        }
        titlebottomImageView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(whiteBackgroundView.snp.bottom).offset(36)
            make.bottom.equalTo(-12)
        }
    }
    

    // MARK: - 数据加载

    /// 加载数据
    private func loadData() {
        setupNoteTypeFilter()
        setupRegionFilter()
        setupVenueFilter()
    }

    /// 设置笔记类型筛选器
    private func setupNoteTypeFilter() {
        let noteTypes = AssemblyDataProvider.getNoteTypes()

        for (index, type) in noteTypes.enumerated() {
            noteTypeFilterView.addButton(
                title: type.title,
                isSelected: index == 0,
                isFirst: index == 0,
                isLast: index == noteTypes.count - 1,
                iconName: type.title,
                delegate: self
            )
        }
    }

    /// 设置地区筛选器
    private func setupRegionFilter() {
        // 地区数据将通过updateRegions方法从外部设置
    }

    /// 设置场地筛选器
    private func setupVenueFilter() {
        // 场地数据将通过updateVenues方法从外部设置
    }

    // MARK: - 公开数据更新方法

    /// 更新地区数据
    /// - Parameter newRegions: 新的地区列表
    func updateRegions(_ newRegions: [RegionVenueItemModel]) {
        regions = newRegions
        regionFilterView.clearButtons()

        for (index, region) in regions.enumerated() {
            regionFilterView.addButton(
                title: region.name,
                isSelected: index == 0, // 默认选中第一个（全部）
                delegate: self
            )
        }

        // 重置选中索引
        selectedRegionIndex = 0

        // 更新位置信息显示
        updateLocationInfo()
    }

    /// 更新场地数据
    /// - Parameter newVenues: 新的场地列表
    func updateVenues(_ newVenues: [VenueItemModel]) {
        venues = newVenues
        venueFilterView.clearButtons()

        for (index, venue) in venues.enumerated() {
            venueFilterView.addButton(
                title: venue.name,
                isSelected: index == 0, // 默认选中第一个（全部）
                delegate: self
            )
        }

        // 重置选中索引
        selectedVenueIndex = 0

        // 更新位置信息显示
        updateLocationInfo()
    }

    // MARK: - 选择处理

    private func selectNoteType(index: Int) {
        noteTypeFilterView.selectButton(at: index)
    }

    private func selectRegion(index: Int) {
        regionFilterView.selectButton(at: index)
        selectedRegionIndex = index
    }

    private func selectVenue(index: Int) {
        venueFilterView.selectButton(at: index)
        selectedVenueIndex = index
    }

    // MARK: - 公开方法

    /// 获取当前选中的地区
    func getSelectedRegion() -> RegionVenueItemModel {
        return regions[selectedRegionIndex]
    }

    /// 获取当前选中的场地
    func getSelectedVenue() -> VenueItemModel {
        return venues[selectedVenueIndex]
    }
    
    /// 设置选中的笔记类型
    func setSelectedNoteType(index: Int) {
        selectNoteType(index: index)
        if index == 0{
            whiteBackgroundView.layer.maskedCorners = .layerMaxXMinYCorner
        }else if index == noteTypeFilterView.getButtonCount() - 1 {
            whiteBackgroundView.layer.maskedCorners = .layerMinXMinYCorner
        }else{
            whiteBackgroundView.layer.maskedCorners = [.layerMinXMinYCorner,.layerMaxXMinYCorner]
        }
    }
    
    /// 设置选中的地区
    func setSelectedRegion(index: Int) {
        selectRegion(index: index)
    }
    
    /// 设置选中的场地
    func setSelectedVenue(index: Int) {
        selectVenue(index: index)
    }
    
    /// 获取当前选中的笔记类型索引
    func getSelectedNoteTypeIndex() -> Int {
        return noteTypeFilterView.getSelectedIndex()
    }
    
    /// 获取当前选中的地区索引
    func getSelectedRegionIndex() -> Int {
        return selectedRegionIndex
    }
    
    /// 获取当前选中的场地索引
    func getSelectedVenueIndex() -> Int {
        return selectedVenueIndex
    }
}

// MARK: - FilterButtonCellDelegate

extension AssemblyFilterView: FilterButtonCellDelegate {
    func filterButtonCellDidTap(_ buttonCell: FilterButtonCell) {
        handleButtonTap(buttonCell)
        if buttonCell.getButtonStyle() == .noteType{
            if buttonCell.isFirst{
                whiteBackgroundView.layer.maskedCorners = .layerMaxXMinYCorner
            }else if buttonCell.isLast {
                whiteBackgroundView.layer.maskedCorners = .layerMinXMinYCorner
            }else{
                whiteBackgroundView.layer.maskedCorners = [.layerMinXMinYCorner,.layerMaxXMinYCorner]
            }
        }
    }
    
    private func handleButtonTap(_ buttonCell: FilterButtonCell) {
        if buttonCell.isNoteType() {
            handleNoteTypeButtonTap(buttonCell)
        } else if let index = regionFilterView.getButtons().firstIndex(of: buttonCell) {
            handleRegionButtonTap(index)
        } else if let index = venueFilterView.getButtons().firstIndex(of: buttonCell) {
            handleVenueButtonTap(index)
        }
    }
    
    private func handleNoteTypeButtonTap(_ buttonCell: FilterButtonCell) {
        if let index = noteTypeFilterView.getButtons().firstIndex(of: buttonCell) {
            selectNoteType(index: index)
            let noteType = AssemblyDataProvider.getNoteTypes()[index]
            delegate?.assemblyFilterView(self, didSelectNoteType: noteType)
        }
    }
    
    private func handleRegionButtonTap(_ index: Int) {
        selectRegion(index: index)
        let selectedRegion = regions[index]

        // 更新locationInfoView的城市显示
        updateLocationInfo()

        delegate?.assemblyFilterView(self, didSelectRegion: selectedRegion)
    }

    private func handleVenueButtonTap(_ index: Int) {
        selectVenue(index: index)
        let selectedVenue = venues[index]

        // 更新locationInfoView的场地显示
        updateLocationInfo()

        delegate?.assemblyFilterView(self, didSelectVenue: selectedVenue)
    }

    /// 更新位置信息显示
    private func updateLocationInfo() {
        let cityName = selectedRegionIndex < regions.count ? regions[selectedRegionIndex].name : "全部"
        let venueName = selectedVenueIndex < venues.count ? venues[selectedVenueIndex].name : "全部"

        locationInfoView.updateLocation(city: cityName, venue: venueName)
    }
}
