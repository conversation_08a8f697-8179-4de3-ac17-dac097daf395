import UIKit
import SnapKit
import Then
import Combine
import CombineCocoa

/// 搜索历史视图代理协议
protocol SearchHistoryViewDelegate: AnyObject {
    /// 点击历史搜索项
    func searchHistoryView(_ view: SearchHistoryView, didSelectItem keyword: String)
    /// 删除历史搜索项
    func searchHistoryView(_ view: SearchHistoryView, didDeleteItem keyword: String)
}

/// 搜索历史视图
class SearchHistoryView: BaseView {
    
    // MARK: - Properties
    
    /// 代理对象
    weak var delegate: SearchHistoryViewDelegate? 
    
    /// 搜索历史数据
    private var searchHistory: [String] = [] {
        didSet {
            DispatchQueue.main.async { [weak self] in
                self?.collectionView.reloadData()
                self?.collectionView.collectionViewLayout.invalidateLayout()
                self?.updateVisibility()
            }
        }
    }
    
    // MARK: - UI Components
    
    /// 标题标签
    private lazy var titleLabel = UILabel().then {
        $0.text = "近期搜索"
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textColor = color_3D3E40
    }
    
    /// 集合视图
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(SearchHistoryCell.self, forCellWithReuseIdentifier: SearchHistoryCell.identifier)
        collectionView.showsVerticalScrollIndicator = false
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.bounces = false // 禁用弹性滚动
        collectionView.alwaysBounceVertical = false // 禁用垂直弹性滚动
        collectionView.alwaysBounceHorizontal = false // 禁用水平弹性滚动
        return collectionView
    }()
    
    // MARK: - UI Configuration
    
    /// UI配置
    override func configUI() {
        backgroundColor = .clear
        
        // 添加子视图
        addSubview(titleLabel)
        addSubview(collectionView)
    }
    
    /// UI布局
    override func configLayout() {
        // 标题标签约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        // 集合视图约束
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview()
        }
    }
    
    // MARK: - Public Methods
    
    /// 更新搜索历史数据
    /// - Parameter history: 搜索历史数组
    func updateSearchHistory(_ history: [String]) {
        searchHistory = history
    }
    
    /// 删除指定的搜索历史项
    /// - Parameter keyword: 要删除的关键词
    func removeHistoryItem(_ keyword: String) {
        searchHistory.removeAll { $0 == keyword }
        collectionView.reloadData()
        updateVisibility()
    }
    
    // MARK: - Private Methods
    
    /// 更新视图可见性
    private func updateVisibility() {
        let hasHistory = !searchHistory.isEmpty
        titleLabel.isHidden = !hasHistory
        collectionView.isHidden = !hasHistory
        isHidden = !hasHistory
    }
}

// MARK: - UICollectionViewDataSource

extension SearchHistoryView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return searchHistory.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: SearchHistoryCell.identifier, for: indexPath) as! SearchHistoryCell
        let keyword = searchHistory[indexPath.item]
        cell.configure(with: keyword)
        cell.delegate = self
        return cell
    }
}

// MARK: - UICollectionViewDelegate

extension SearchHistoryView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let keyword = searchHistory[indexPath.item]
        delegate?.searchHistoryView(self, didSelectItem: keyword)
    }
}

// MARK: - UICollectionViewDelegateFlowLayout

extension SearchHistoryView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let keyword = searchHistory[indexPath.item]

        // 使用临时label来精确计算文本宽度
        let tempLabel = UILabel()
        tempLabel.font = UIFont.systemFont(ofSize: 14)
        tempLabel.text = keyword
        tempLabel.sizeToFit()

        // 添加内边距和删除按钮的宽度
        // 左内边距12 + 文本宽度 + 文本与删除按钮间距8 + 删除按钮16 + 右内边距8
        let width = ceil(12 + tempLabel.frame.width + 8 + 16 + 8)
        let height: CGFloat = 32

        // 设置最小宽度和最大宽度
        let minWidth: CGFloat = 60
        let maxWidth: CGFloat = collectionView.frame.width - 32 // 减去左右边距
        let finalWidth = min(max(width, minWidth), maxWidth)

        return CGSize(width: finalWidth, height: height)
    }
}

// MARK: - SearchHistoryCellDelegate

extension SearchHistoryView: SearchHistoryCellDelegate {
    func searchHistoryCellDidTapDelete(_ cell: SearchHistoryCell, keyword: String) {
        delegate?.searchHistoryView(self, didDeleteItem: keyword)
        removeHistoryItem(keyword)
    }
}

// MARK: - SearchHistoryCell

/// 搜索历史单元格代理协议
protocol SearchHistoryCellDelegate: AnyObject {
    /// 点击删除按钮
    func searchHistoryCellDidTapDelete(_ cell: SearchHistoryCell, keyword: String)
}

/// 搜索历史单元格
class SearchHistoryCell: UICollectionViewCell {
    
    // MARK: - Properties
    
    static let identifier = "SearchHistoryCell"
    
    /// 代理对象
    weak var delegate: SearchHistoryCellDelegate?
    
    /// 当前关键词
    private var currentKeyword: String = ""
    
    // MARK: - UI Components
    
    /// 关键词标签
    private lazy var keywordLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14)
        $0.textColor = color_3D3E40
        $0.textAlignment = .left
    }
    
    /// 删除按钮
    private lazy var deleteButton = UIButton(type: .system).then {
        $0.setImage(UIImage(systemName: "xmark"), for: .normal)
        $0.tintColor = color_999999
        $0.contentMode = .scaleAspectFit
    }
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupBindings()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Configuration
    
    private func setupUI() {
        // 设置背景
        backgroundColor =  color_2B2C2F04
        layer.cornerRadius = 16
        layer.masksToBounds = true
        
        // 添加子视图
        contentView.addSubview(keywordLabel)
        contentView.addSubview(deleteButton)
        
        // 设置约束
        keywordLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.right.equalTo(deleteButton.snp.left).offset(-8)
        }
        
        deleteButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
    }
    
    private func setupBindings() {
        deleteButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.searchHistoryCellDidTapDelete(self, keyword: self.currentKeyword)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 配置单元格
    /// - Parameter keyword: 搜索关键词
    func configure(with keyword: String) {
        currentKeyword = keyword
        keywordLabel.text = keyword
    }
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
}
