import SnapKit
import UIKit
import Then

/// 顶部搜索栏视图代理协议
protocol AssemblySearchViewDelegate: AnyObject {
    /// 搜索事件
    /// - Parameters:
    ///   - searchView: 触发事件的搜索视图
    ///   - searchText: 搜索文本
    func assemblySearchView(_ searchView: AssemblySearchView, didSearchWithText searchText: String)

    /// 菜单按钮点击事件
    /// - Parameter searchView: 触发事件的搜索视图
    func assemblySearchViewDidTapMenu(_ searchView: AssemblySearchView)

    /// 扫描按钮点击事件
    /// - Parameter searchView: 触发事件的搜索视图
    func assemblySearchViewDidTapScan(_ searchView: AssemblySearchView)
}

/// 顶部搜索栏视图
class AssemblySearchView: BaseView {
    // MARK: - 属性
    
    /// 代理
    weak var delegate: AssemblySearchViewDelegate?
    
    // MARK: - UI组件
    
    /// 搜索容器视图
    private lazy var searchContainerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 18  // 高度的一半，将在布局时确保高度为40
        $0.layer.masksToBounds = true
    }
    
    /// 搜索图标
    private lazy var searchIconImageView = UIImageView().then {
        $0.image = UIImage(named: "assembly_search")
        $0.contentMode = .scaleAspectFit
    }
    
    /// 搜索文本框
    private lazy var searchTextView = UITextField().then {
        $0.font = UIFont.systemFont(ofSize: 14)
        $0.textColor = color_686A6D80
        $0.text = "在这里搜索内容"
        $0.isUserInteractionEnabled = false // 禁用编辑
    }
    
    /// 搜索按钮
    private lazy var searchButton = UIButton(type: .system).then {
        $0.backgroundColor = color_blue
        $0.setTitle("搜索", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.layer.cornerRadius = 15  // 按钮的高度将是30，所以圆角是15
    }
    
    /// 菜单按钮
    private lazy var menuButton = UIButton(type: .system).then {
        $0.setImage(UIImage(named: "home_leftmenu"), for: .normal)
        $0.tintColor = .white
    }
    
    /// 扫描按钮
    private lazy var scanButton = UIButton(type: .system).then {
        $0.setImage(UIImage(named: "assembly_qrcode"), for: .normal)
        $0.tintColor = .white
    }
    
    // MARK: - 配置方法
    
    override func configUI() {
        super.configUI()
        backgroundColor = color_blue
        // 添加子视图
        addSubview(menuButton)
        addSubview(searchContainerView)
        addSubview(scanButton)
        
        // 搜索容器内的子视图
        searchContainerView.addSubview(searchIconImageView)
        searchContainerView.addSubview(searchTextView)
        searchContainerView.addSubview(searchButton)
        
        // 添加按钮事件
        menuButton.addTarget(self, action: #selector(menuButtonTapped), for: .touchUpInside)
        scanButton.addTarget(self, action: #selector(scanButtonTapped), for: .touchUpInside)
        searchButton.addTarget(self, action: #selector(searchButtonTapped), for: .touchUpInside)

        // 添加搜索容器点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(searchContainerTapped))
        searchContainerView.addGestureRecognizer(tapGesture)
    }
    
    override func configLayout() {
        super.configLayout()
        
        // 主按钮约束
        menuButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalTo(searchContainerView)
            make.width.height.equalTo(24)
        }
        
        searchContainerView.snp.makeConstraints { make in
            make.left.equalTo(menuButton.snp.right).offset(24)
            make.bottom.equalToSuperview().offset(-8)
            make.height.equalTo(36)
            make.right.equalTo(scanButton.snp.left).offset(-24)
        }
        
        scanButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalTo(searchContainerView)
            make.width.height.equalTo(24)
        }
        
        // 搜索容器内部约束
        searchIconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
        searchTextView.snp.makeConstraints { make in
            make.left.equalTo(searchIconImageView.snp.right).offset(8)
            make.top.bottom.equalToSuperview()
            make.right.equalTo(searchButton.snp.left).offset(-8)
        }
        
        searchButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-5)
            make.centerY.equalToSuperview()
            make.width.equalTo(60)
            make.height.equalTo(30)
        }
    }
    func configViewStyle(isLight:Bool){
        menuButton.tintColor = isLight ? .white:.black
        scanButton.tintColor = isLight ? .white:.black
        searchContainerView.backgroundColor = isLight ? .white:color_F6F8F9
        self.backgroundColor = isLight ? color_blue:.white

    }
    
    // MARK: - 事件处理
    
    @objc private func menuButtonTapped() {
        delegate?.assemblySearchViewDidTapMenu(self)
    }
    
    @objc private func scanButtonTapped() {
        delegate?.assemblySearchViewDidTapScan(self)
    }
    
    @objc private func searchButtonTapped() {
        // 点击搜索按钮时，触发搜索事件（跳转到搜索页面）
        delegate?.assemblySearchView(self, didSearchWithText: "")
    }

    @objc private func searchContainerTapped() {
        // 点击搜索容器时，触发搜索事件（跳转到搜索页面）
        delegate?.assemblySearchView(self, didSearchWithText: "")
    }
}
