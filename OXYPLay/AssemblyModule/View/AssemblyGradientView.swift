//
//  AssemblyGradientView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/6.
//

import UIKit
import SnapKit
import Then

class AssemblyGradientView: BaseView {
    
    // MARK: - Properties
    
    /// 渐变图层
    private lazy var gradientLayer = CAGradientLayer().then {
        $0.colors = [
            UIColor(red: 50 / 255.0, green: 120 / 255.0, blue: 250 / 255.0, alpha: 1.0).cgColor,
            UIColor(red: 80 / 255.0, green: 150 / 255.0, blue: 255 / 255.0, alpha: 0.95).cgColor,
            UIColor(red: 80 / 255.0, green: 150 / 255.0, blue: 255 / 255.0, alpha: 0.5).cgColor,
        ]
        $0.locations = [0.0, 0.7, 1.0]
        $0.startPoint = CGPoint(x: 0.5, y: 0.0)
        $0.endPoint = CGPoint(x: 0.5, y: 1.0)
    }
    
    // MARK: - Lifecycle
    
    override func configUI() {
        super.configUI()
        layer.addSublayer(gradientLayer)
    }
    
    override func configLayout() {
        super.configLayout()
        // 这里不需要设置约束，因为渐变层会在layoutSubviews中更新
    }
    
    // MARK: - Layout
    
    override func layoutSubviews() {
        super.layoutSubviews()
        // 更新渐变层的frame以匹配视图大小
        gradientLayer.frame = bounds
    }
    
    // MARK: - Public Methods
    
    /// 设置渐变颜色
    /// - Parameters:
    ///   - colors: 渐变颜色数组
    ///   - locations: 渐变位置数组，值范围0.0-1.0
    ///   - startPoint: 渐变开始点，默认为(0.5, 0)，即顶部中心
    ///   - endPoint: 渐变结束点，默认为(0.5, 1)，即底部中心
    func setGradient(colors: [UIColor], 
                     locations: [NSNumber]? = [0.0, 0.7, 1.0],
                     startPoint: CGPoint = CGPoint(x: 0.5, y: 0.0),
                     endPoint: CGPoint = CGPoint(x: 0.5, y: 1.0)) {
        gradientLayer.colors = colors.map { $0.cgColor }
        gradientLayer.locations = locations
        gradientLayer.startPoint = startPoint
        gradientLayer.endPoint = endPoint
        setNeedsLayout()
    }
    
    /// 设置渐变方向
    /// - Parameters:
    ///   - startPoint: 渐变开始点
    ///   - endPoint: 渐变结束点
    func setGradientDirection(startPoint: CGPoint, endPoint: CGPoint) {
        gradientLayer.startPoint = startPoint
        gradientLayer.endPoint = endPoint
        setNeedsLayout()
    }
}
