import UIKit
import Then

/// 笔记类型标题视图
class NoteTypeTitleView: BaseView {
    // MARK: - UI组件
    
    lazy var titleImageView = UIImageView().then {
        $0.image = UIImage(named: "assembly_list_typetitle")
    }
    
    lazy var subtitleLabel = UILabel().then {
        $0.text = "选择您想看的类型"
        $0.font = UIFont.systemFont(ofSize: 10,weight: .light)
        $0.textColor = .white
    }
    
    // MARK: - 配置方法
    
    override func configUI() {
        super.configUI()
        
        addSubview(titleImageView)
        addSubview(subtitleLabel)
    }
    
    override func configLayout() {
        super.configLayout()
        
        titleImageView.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(titleImageView.snp.right).offset(8)
        }
    }
} 
