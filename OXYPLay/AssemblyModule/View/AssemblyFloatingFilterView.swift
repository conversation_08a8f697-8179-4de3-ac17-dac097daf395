//
//  AssemblyFloatingFilterView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/6.
//

import UIKit
import SnapKit
import Then

/// 筛选视图组件
class AssemblyFloatingFilterView: BaseView {
    // MARK: - 属性

    /// 代理
    weak var delegate: AssemblyFilterViewDelegate?

    private var regions: [RegionVenueItemModel] = []
    private var venues: [VenueItemModel] = []
    private var selectedRegionIndex: Int = 0
    private var selectedVenueIndex: Int = 0
    
    // MARK: - UI组件
    
    // 筛选视图
    private lazy var noteTypeFilterView = FilterScrollView(type: .noteTypeOnlyTitle)
    private lazy var regionFilterView = FilterScrollView(type: .region)
    private lazy var venueFilterView = FilterScrollView(type: .venue)
    
    // 标题标签
    private lazy var regionTitle = UILabel().then {
        $0.text = "选择地区"
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = color_3D3E40
    }

    private lazy var venueTitle = UILabel().then {
        $0.text = "选择场地"
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = color_3D3E40
    }

    // 布局容器
    private lazy var mainStackView = UIStackView().then {
        $0.axis = .vertical
        $0.spacing = 8
        $0.distribution = .fill
        $0.alignment = .fill
        $0.translatesAutoresizingMaskIntoConstraints = false
        $0.isUserInteractionEnabled = true
    }
    
    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        loadData()
        setupInitialState()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func layoutSubviews() {
        super.layoutSubviews()
    }

    // MARK: - 配置方法

    override func configUI() {
        super.configUI()
        
        setupViewAppearance()
        setupLayout()
    }
    
    private func setupInitialState() {
        // 初始时隐藏
        self.isHidden = true
        self.alpha = 0
    }
    
    private func setupViewAppearance() {
        // 确保视图可以接收用户交互
        self.isUserInteractionEnabled = true
        
        // 设置视图样式
        self.backgroundColor = .white
        self.layer.cornerRadius = 16
        self.layer.masksToBounds = false
        self.layer.maskedCorners = [.layerMaxXMaxYCorner, .layerMinXMaxYCorner]
    }
    
    private func setupLayout() {
        // 添加主容器
        addSubview(mainStackView)
        
        // 添加筛选组件
        mainStackView.addArrangedSubview(noteTypeFilterView)
        mainStackView.addArrangedSubview(regionTitle)
        mainStackView.addArrangedSubview(regionFilterView)
        mainStackView.addArrangedSubview(venueTitle)
        mainStackView.addArrangedSubview(venueFilterView)
        
        // 设置约束
        mainStackView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview().offset(-12)
        }
        
        // 设置筛选视图高度
        noteTypeFilterView.snp.makeConstraints { make in
            make.height.equalTo(24)
        }
        
        regionFilterView.snp.makeConstraints { make in
            make.height.equalTo(24)
        }
        
        venueFilterView.snp.makeConstraints { make in
            make.height.equalTo(24)
        }
    }

    // MARK: - 数据加载

    /// 加载数据
    private func loadData() {
        setupNoteTypeFilter()
        setupRegionFilter()
        setupVenueFilter()
    }

    /// 设置笔记类型筛选器
    private func setupNoteTypeFilter() {
        let noteTypes = AssemblyDataProvider.getNoteTypes()

        for (index, type) in noteTypes.enumerated() {
            noteTypeFilterView.addButton(
                title: type.title,
                isSelected: index == 0,
                delegate: self
            )
        }
    }

    /// 设置地区筛选器
    private func setupRegionFilter() {
        // 地区数据将通过updateRegions方法从外部设置
    }

    /// 设置场地筛选器
    private func setupVenueFilter() {
        // 场地数据将通过updateVenues方法从外部设置
    }

    // MARK: - 公开数据更新方法

    /// 更新地区数据
    /// - Parameter newRegions: 新的地区列表
    func updateRegions(_ newRegions: [RegionVenueItemModel]) {
        regions = newRegions
        regionFilterView.clearButtons()

        for (index, region) in regions.enumerated() {
            regionFilterView.addButton(
                title: region.name,
                isSelected: index == 0, // 默认选中第一个（全部）
                delegate: self
            )
        }

        // 重置选中索引
        selectedRegionIndex = 0
    }

    /// 更新场地数据
    /// - Parameter newVenues: 新的场地列表
    func updateVenues(_ newVenues: [VenueItemModel]) {
        venues = newVenues
        venueFilterView.clearButtons()

        for (index, venue) in venues.enumerated() {
            venueFilterView.addButton(
                title: venue.name,
                isSelected: index == 0, // 默认选中第一个（全部）
                delegate: self
            )
        }

        // 重置选中索引
        selectedVenueIndex = 0
    }

    // MARK: - 选择处理

    private func selectNoteType(index: Int) {
        noteTypeFilterView.selectButton(at: index)
    }

    private func selectRegion(index: Int) {
        regionFilterView.selectButton(at: index)
        selectedRegionIndex = index
    }

    private func selectVenue(index: Int) {
        venueFilterView.selectButton(at: index)
        selectedVenueIndex = index
    }

    // MARK: - 公开方法

    /// 获取当前选中的地区
    func getSelectedRegion() -> RegionVenueItemModel {
        return regions[selectedRegionIndex]
    }

    /// 获取当前选中的场地
    func getSelectedVenue() -> VenueItemModel {
        return venues[selectedVenueIndex]
    }
    
    /// 设置选中的笔记类型
    func setSelectedNoteType(index: Int) {
        selectNoteType(index: index)
    }
    
    /// 设置选中的地区
    func setSelectedRegion(index: Int) {
        selectRegion(index: index)
    }
    
    /// 设置选中的场地
    func setSelectedVenue(index: Int) {
        selectVenue(index: index)
    }
    
    /// 获取当前选中的笔记类型索引
    func getSelectedNoteTypeIndex() -> Int {
        return noteTypeFilterView.getSelectedIndex()
    }
    
    /// 获取当前选中的地区索引
    func getSelectedRegionIndex() -> Int {
        return selectedRegionIndex
    }
    
    /// 获取当前选中的场地索引
    func getSelectedVenueIndex() -> Int {
        return selectedVenueIndex
    }
}

// MARK: - FilterButtonCellDelegate

extension AssemblyFloatingFilterView: FilterButtonCellDelegate {
    func filterButtonCellDidTap(_ buttonCell: FilterButtonCell) {
        handleButtonTap(buttonCell)
    }
    
    private func handleButtonTap(_ buttonCell: FilterButtonCell) {
        if buttonCell.isNoteType() {
            handleNoteTypeButtonTap(buttonCell)
        } else if let index = regionFilterView.getButtons().firstIndex(of: buttonCell) {
            handleRegionButtonTap(index)
        } else if let index = venueFilterView.getButtons().firstIndex(of: buttonCell) {
            handleVenueButtonTap(index)
        }
    }
    
    private func handleNoteTypeButtonTap(_ buttonCell: FilterButtonCell) {
        if let index = noteTypeFilterView.getButtons().firstIndex(of: buttonCell) {
            selectNoteType(index: index)
            let noteType = AssemblyDataProvider.getNoteTypes()[index]
            delegate?.assemblyFilterView(self, didSelectNoteType: noteType)
        }
    }
    
    private func handleRegionButtonTap(_ index: Int) {
        selectRegion(index: index)
        delegate?.assemblyFilterView(self, didSelectRegion: regions[index])
    }
    
    private func handleVenueButtonTap(_ index: Int) {
        selectVenue(index: index)
        delegate?.assemblyFilterView(self, didSelectVenue: venues[index])
    }
}
