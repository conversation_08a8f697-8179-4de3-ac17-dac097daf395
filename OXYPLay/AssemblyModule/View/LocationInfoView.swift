import UIKit
import SnapKit
import Then

/// 位置信息视图
class LocationInfoView: BaseView {
    // MARK: - UI组件
    
    lazy var locationIcon = UIImageView().then {
        $0.tintColor = .darkGray
        $0.contentMode = .scaleAspectFit
        $0.image = UIImage(named: "assembly_location")
    }
    
    lazy var cityLabel = UILabel().then {
        $0.text = "北京"
        $0.font = UIFont.systemFont(ofSize: 10,weight: .regular)
        $0.textColor = color_3D3E40
    }
    
    lazy var venueLabel = UILabel().then {
        $0.text = "翠云山"
        $0.font = UIFont.systemFont(ofSize: 10,weight: .regular)
        $0.textColor = color_3D3E40
    }
    lazy var line = UIView().then {
        $0.backgroundColor = color_3D3E4012
    }
    // MARK: - 配置方法
    
    override func configUI() {
        super.configUI()
        
        addSubview(locationIcon)
        addSubview(cityLabel)
        addSubview(venueLabel)
        addSubview(line)
    }
    
    override func configLayout() {
        super.configLayout()
        
        locationIcon.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
        cityLabel.snp.makeConstraints { make in
            make.left.equalTo(locationIcon.snp.right).offset(4)
            make.centerY.equalToSuperview()
        }
        
        venueLabel.snp.makeConstraints { make in
            make.left.equalTo(line.snp.right).offset(8)
            make.centerY.equalToSuperview()
            make.right.equalToSuperview()
        }
        line.snp.makeConstraints { make in
            make.left.equalTo(cityLabel.snp.right).offset(8)
            make.width.equalTo(1)
            make.height.equalTo(12)
            make.centerY.equalTo(cityLabel)
           
        }
        
    }
    
    // MARK: - 公开方法
    
    /// 更新位置信息
    /// - Parameters:
    ///   - city: 城市名称
    ///   - venue: 场地名称
    func updateLocation(city: String, venue: String) {
        cityLabel.text = city
        venueLabel.text = venue
    }
} 
