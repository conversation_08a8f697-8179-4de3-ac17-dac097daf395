import UIKit
import SnapKit
import Then

/// 筛选滚动视图类型
enum FilterScrollViewType {
    case noteType           // 带图标的笔记类型
    case noteTypeOnlyTitle  // 纯文字的笔记类型
    case region             // 地区
    case venue              // 场地
}

/// 筛选滚动视图
class FilterScrollView: BaseView {
    // MARK: - 属性
    
    private var type: FilterScrollViewType
    private var buttons: [FilterButtonCell] = []
    private var stackView: UIStackView!
    
    // MARK: - UI组件
    
    lazy var scrollView = UIScrollView().then {
        $0.showsHorizontalScrollIndicator = false
        $0.isUserInteractionEnabled = true
    }
    
    // MARK: - 初始化
    
    init(type: FilterScrollViewType) {
        self.type = type
        super.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 配置方法
    
    override func configUI() {
        super.configUI()
        setupScrollView()
        setupContentView()
    }
    
    private func setupScrollView() {
        addSubview(scrollView)
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setupContentView() {
        // 创建内容视图
        let contentView = UIView()
        contentView.isUserInteractionEnabled = true
        scrollView.addSubview(contentView)
        
        // 创建堆栈视图
        stackView = UIStackView().then {
            $0.axis = .horizontal
            $0.spacing = 12 // 统一间距
            $0.distribution = .fillProportionally
            $0.alignment = .fill
            $0.isUserInteractionEnabled = true
        }
        
        contentView.addSubview(stackView)
        
        // 设置约束
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(scrollView)
        }
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalToSuperview()
        }
    }
    
    // MARK: - 公开方法
    
    /// 添加按钮
    /// - Parameters:
    ///   - title: 按钮标题
    ///   - isSelected: 是否选中
    ///   - iconName: 图标名称（仅用于笔记类型按钮）
    ///   - delegate: 按钮代理
    func addButton(title: String, isSelected: Bool,isFirst: Bool = false,isLast: Bool = false, iconName: String? = nil, delegate: FilterButtonCellDelegate?) {
        let buttonStyle = getButtonStyleForType()
        
        let button = FilterButtonCell(
            title: title,
            isSelected: isSelected,
            isFirst: isFirst,
            isLast: isLast,
            style: buttonStyle,
            iconName: iconName
        )
        
        stackView.addArrangedSubview(button)
        buttons.append(button)
        
        // 设置代理
        button.delegate = delegate
    }
    
    /// 根据视图类型获取按钮样式
    private func getButtonStyleForType() -> FilterButtonStyle {
        switch type {
        case .noteType:
            return .noteType
        case .noteTypeOnlyTitle:
            return .plainText
        case .region, .venue:
            return .normal
        }
    }
    
    /// 选择按钮
    /// - Parameter index: 按钮索引
    func selectButton(at index: Int) {
        guard index >= 0 && index < buttons.count else { return }
        
        for (i, button) in buttons.enumerated() {
            button.setSelected(i == index)
        }
    }
    
    /// 获取所有按钮
    /// - Returns: 按钮数组
    func getButtons() -> [FilterButtonCell] {
        return buttons
    }
    
    /// 获取按钮数量
    /// - Returns: 按钮数量
    func getButtonCount() -> Int {
        return buttons.count
    }
    
    /// 获取当前选中按钮的索引
    /// - Returns: 选中按钮的索引，如果没有选中按钮则返回0
    func getSelectedIndex() -> Int {
        for (index, button) in buttons.enumerated() {
            if button.getSelected() {
                return index
            }
        }
        return 0
    }

    /// 清空所有按钮
    func clearButtons() {
        // 从stackView中移除所有按钮
        buttons.forEach { button in
            stackView.removeArrangedSubview(button)
            button.removeFromSuperview()
        }

        // 清空按钮数组
        buttons.removeAll()
    }
}
