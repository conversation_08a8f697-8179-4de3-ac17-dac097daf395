import SnapKit
import UIKit

/// 筛选按钮样式类型
enum FilterButtonStyle {
    case normal       // 普通筛选按钮
    case noteType     // 笔记类型按钮（带图标）
    case plainText    // 纯文字样式按钮
}

/// 筛选按钮单元格代理协议
protocol FilterButtonCellDelegate: AnyObject {
    /// 按钮点击事件
    /// - Parameter buttonCell: 被点击的按钮单元格
    func filterButtonCellDidTap(_ buttonCell: FilterButtonCell)
  
}

/// 筛选按钮样式组件
class FilterButtonCell: UIView {
    // MARK: - 属性

    private var isSelected: Bool = false
    var isFirst: Bool = false
    var isLast: Bool = false
    private var textLabel: UILabel!
    private var textLabelImageView: UIImageView!
    private var iconImageView: UIImageView?
    private var backImageView: UIImageView!

    private var buttonStyle: FilterButtonStyle = .normal

    /// 代理
    weak var delegate: FilterButtonCellDelegate?

    /// 按钮标题
    private var buttonTitle: String = ""

    /// 图标名称（未选中状态）
    private var normalIconName: String?

    /// 选中状态图标名称
    private var selectedIconName: String?

    // MARK: - 初始化

    init(title: String, isSelected: Bool = false,isFirst: Bool = false,isLast: Bool = false, style: FilterButtonStyle = .normal, iconName: String? = nil) {
        super.init(frame: .zero)
        translatesAutoresizingMaskIntoConstraints = false
        self.isSelected = isSelected
        self.isFirst = isFirst
        self.isLast = isLast
        self.buttonStyle = style
        buttonTitle = title
        self.normalIconName = iconName
        self.selectedIconName = (iconName ?? "") + "_select"
        setupView(title: title, iconName: iconName)
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 视图设置

    private func setupView(title: String, iconName: String?) {
        // 圆角设置
        layer.cornerRadius = buttonStyle == .noteType ? 0 : 12
        // 根据按钮样式设置UI
        setupButtonUI(title: title, iconName: iconName)
        // 设置初始状态
        updateStyle()
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(buttonTapped))
        addGestureRecognizer(tapGesture)
    }
    
    private func setupButtonUI(title: String, iconName: String?) {
        switch buttonStyle {
        case .noteType:
            setupNoteTypeButton(title: title, iconName: iconName)
        case .plainText, .normal:
            setupTextButton(title: title)
        }
    }
    
    private func setupNoteTypeButton(title: String, iconName: String?) {
        // 为笔记类型按钮设置固定宽度
        snp.makeConstraints { make in
            make.width.equalTo((ScreenInfo.width-12*6)/7)
        }
        backImageView = UIImageView()
        addSubview(backImageView)
        backImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }

        // 图标
        if let iconName = iconName {
            let iconView = UIImageView()
            iconView.translatesAutoresizingMaskIntoConstraints = false
            iconView.contentMode = .scaleAspectFit
            // 初始设置图标，后续会在updateStyle中根据状态更新
            iconView.image = UIImage(named: iconName) ?? UIImage(systemName: iconName)
            // 如果没有提供不同状态的图标，则使用tintColor
            if selectedIconName == nil {
                iconView.tintColor = .white
            }
            addSubview(iconView)
            iconImageView = iconView

            iconView.snp.makeConstraints { make in
                make.centerX.equalToSuperview()
                make.top.equalToSuperview().offset(8)
                make.width.height.equalTo(24)
            }
        }
        textLabelImageView = UIImageView()
        textLabelImageView.image = UIImage(named: "assembly_list_typetitle_bottom")
        addSubview(textLabelImageView)
       
        
        // 文本标签 - 垂直排列在图标下方
        textLabel = UILabel()
        textLabel.translatesAutoresizingMaskIntoConstraints = false
        textLabel.text = title
        textLabel.textAlignment = .center
        textLabel.font = .systemFont(ofSize: 12, weight: .regular)
        textLabel.numberOfLines = 1
        
        addSubview(textLabel)

        // 文本约束
        textLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(4)
            make.bottom.equalTo(-8)
        }
        textLabelImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(textLabel.snp.centerY).offset(5)
        }
       
    }
    
    private func setupTextButton(title: String) {
        // 常规按钮或纯文字按钮只有文本
        textLabel = UILabel()
        textLabel.translatesAutoresizingMaskIntoConstraints = false
        textLabel.text = title
        textLabel.textAlignment = .center
        textLabel.font = .systemFont(ofSize: 12, weight: buttonStyle == .plainText ? .medium : .light)
        addSubview(textLabel)

        // 约束设置
        textLabel.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(15)
            make.right.equalTo(-15)
        }
    }

    // MARK: - 事件处理

    @objc private func buttonTapped() {
        delegate?.filterButtonCellDidTap(self)
    }

    // MARK: - 公开方法

    /// 设置选中状态
    func setSelected(_ selected: Bool) {
        isSelected = selected
        updateStyle()
    }

    /// 获取选中状态
    func getSelected() -> Bool {
        return isSelected
    }

    /// 获取按钮标题
    func getTitle() -> String {
        return buttonTitle
    }

    /// 判断是否为笔记类型按钮（包括纯文字类型）
    func isNoteType() -> Bool {
        return buttonStyle == .noteType || buttonStyle == .plainText
    }
    
    /// 获取按钮样式
    func getButtonStyle() -> FilterButtonStyle {
        return buttonStyle
    }

    // MARK: - 样式更新

    private func updateStyle() {
        switch buttonStyle {
        case .noteType:
            updateNoteTypeStyle()
        case .plainText:
            updatePlainTextStyle()
        case .normal:
            updateNormalStyle()
        }
    }
    
    private func updateNoteTypeStyle() {
        if isFirst {
            backImageView.image = UIImage(named: "assembly_list_type_backcenter")
        }else if isLast{
            backImageView.image = UIImage(named: "assembly_list_type_backcenter")
        }else{
            backImageView.image = UIImage(named: "assembly_list_type_backcenter")
        }
        textLabelImageView.isHidden = !isSelected
        backImageView.isHidden = !isSelected

        // 更新图标和文字颜色
        if isSelected {
            textLabel.textColor = .black
            // 如果有选中状态的图标，使用选中图标，否则使用tintColor
            if let selectedIcon = selectedIconName {
                iconImageView?.image = UIImage(named: selectedIcon) ?? UIImage(systemName: selectedIcon)
                iconImageView?.tintColor = nil // 清除tintColor，使用原始图片颜色
            } else {
                iconImageView?.tintColor = .black
            }
        } else {
            textLabel.textColor = .white
            // 使用未选中状态的图标
            if let normalIcon = normalIconName {
                iconImageView?.image = UIImage(named: normalIcon) ?? UIImage(systemName: normalIcon)
                iconImageView?.tintColor = nil // 清除tintColor，使用原始图片颜色
            } else {
                iconImageView?.tintColor = .white
            }
        }
    }
    
    private func updatePlainTextStyle() {
        backgroundColor = .clear
        if isSelected {
            textLabel.textColor = .black
            textLabel.font = .systemFont(ofSize: 12, weight: .medium)
        } else {
            textLabel.textColor = color_686A6D80
            textLabel.font = .systemFont(ofSize: 12, weight: .regular)
        }
    }
    
    private func updateNormalStyle() {
        layer.masksToBounds = true
        if isSelected {
            backgroundColor = color_blue
            textLabel.textColor = .white
        } else {
            backgroundColor = color_2B2C2F04
            textLabel.textColor = color_686A6D80
        }
    }
}
