//
//  AssemblyViewController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/22.
//

import SnapKit
import UIKit
import Combine

class AssemblyViewController: BaseViewController {
    // MARK: - 属性
    private lazy var searchView = AssemblySearchView()
    private var filterView = AssemblyFilterView()
    private lazy var floatingFilterView = AssemblyFloatingFilterView()
    private lazy var tableView = UITableView(frame: .zero, style:.grouped).then{
        $0.backgroundColor = color_F6F8F9
        $0.separatorStyle = .none
        $0.showsVerticalScrollIndicator = false
        $0.register(AssemblyNoteCell.self, forCellReuseIdentifier: AssemblyNoteCell.identifier)
        $0.delegate = self
        $0.dataSource = self
        $0.estimatedRowHeight = 350
        $0.rowHeight = UITableView.automaticDimension
        $0.sectionHeaderHeight = UITableView.automaticDimension
        $0.estimatedSectionHeaderHeight = 250
        $0.tableHeaderView = UIView(frame: CGRect(x: 0, y: 0, width: 0, height: 0.01))
    }
    private lazy var assemblyTitleLabel = UILabel().then{
        $0.text = "集结笔记"
        $0.font = UIFont.boldSystemFont(ofSize: 20)
        $0.textColor = .black
    }
    
    // 视图模型
    private let viewModel = AssemblyViewModel()
        
    // 用于记录filterView的原始位置
    private var filterViewOriginY: CGFloat = 0

    // MARK: - 生命周期方法

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
        setupDelegates()
        setupBindings()
        setupRefresh()
        loadData()
        
        // 初始化floatingFilterView的状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 同步初始选择状态
            let noteTypeIndex = self.filterView.getSelectedNoteTypeIndex()
            let regionIndex = self.filterView.getSelectedRegionIndex()
            let venueIndex = self.filterView.getSelectedVenueIndex()
            
            self.floatingFilterView.setSelectedNoteType(index: noteTypeIndex)
            self.floatingFilterView.setSelectedRegion(index: regionIndex)
            self.floatingFilterView.setSelectedVenue(index: venueIndex)
        }
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 记录filterView的初始位置
        if filterViewOriginY == 0 {
            // 在第一次布局完成后获取filterView的位置
            let headerRect = tableView.rect(forSection: 0)
            if headerRect.origin.y != 0 {
                filterViewOriginY = headerRect.origin.y
            }
        }
    }

    // MARK: - 设置视图

    private func setupViews() {
        fd_prefersNavigationBarHidden = true
        view.addSubview(searchView)
        view.addSubview(tableView)
        view.addSubview(floatingFilterView)
        searchView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalNavBarHeight)
        }
  
        tableView.snp.makeConstraints { make in
            make.top.equalTo(searchView.snp.bottom)
            make.bottom.equalToSuperview()
            make.left.equalTo(0)
            make.right.equalTo(0)
        }
        
        // 设置悬浮的filterView
        floatingFilterView.snp.makeConstraints { make in
            make.top.equalTo(searchView.snp.bottom)
            make.left.right.equalToSuperview()
        }
        
        // 默认隐藏悬浮筛选视图
        floatingFilterView.isHidden = true
        floatingFilterView.alpha = 0
    }
  
    // MARK: - 设置代理

    private func setupDelegates() {
        // 设置各个视图的代理
        searchView.delegate = self
        filterView.delegate = self
        floatingFilterView.delegate = self
    }
    
    // MARK: - 设置数据绑定
    
   override func setupBindings() {
        // 监听笔记数据变化
        viewModel.$notes
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)

        // 监听地区数据变化
        viewModel.$regions
            .receive(on: DispatchQueue.main)
            .sink { [weak self] regions in
                self?.filterView.updateRegions(regions)
                self?.floatingFilterView.updateRegions(regions)
            }
            .store(in: &cancellables)

        // 监听场地数据变化
        viewModel.$venues
            .receive(on: DispatchQueue.main)
            .sink { [weak self] venues in
                self?.filterView.updateVenues(venues)
                self?.floatingFilterView.updateVenues(venues)
            }
            .store(in: &cancellables)

        // 监听空数据状态变化
        viewModel.$emptyDataSetType
            .receive(on: DispatchQueue.main)
            .sink { [weak self] emptyType in
                guard let self = self else { return }
                if let type = emptyType {
                    self.setEmptyDataSet(for: self.tableView, type: type)
                } else {
                    self.hideEmptyDataSet(for: self.tableView)
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 设置刷新控件

    private func setupRefresh() {
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: tableView, with: viewModel)
    }
    
   
    // MARK: - 数据加载

    private func loadData() {
        // 加载网络数据
        viewModel.refreshData()
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource

extension AssemblyViewController: UITableViewDelegate, UITableViewDataSource {
    // 设置分区头视图
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        return filterView
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return UITableView.automaticDimension
    }
    
    func tableView(_: UITableView, numberOfRowsInSection _: Int) -> Int {
        return viewModel.notes.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: AssemblyNoteCell.identifier, for: indexPath) as? AssemblyNoteCell else {
            return UITableViewCell()
        }

        let note = viewModel.notes[indexPath.row]
        cell.configure(with: note)

        // 设置代理
        cell.delegate = self

        return cell
    }

    func tableView(_: UITableView, didSelectRowAt indexPath: IndexPath) {
        let note = viewModel.notes[indexPath.row]
        let vc = HomeDetailController(postId: note.id, pageType: note.recommendType.rawValue)
        pushVcHiddenTabBar(vc, animated: true)
       
    }
}

// MARK: - UIScrollViewDelegate

extension AssemblyViewController {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let offsetY = scrollView.contentOffset.y
        
        // 当滚动到100的位置时，显示悬停的filterView
        if offsetY >= 100 {
            if floatingFilterView.isHidden {
                floatingFilterView.isHidden = false
                UIView.animate(withDuration: 0.3) {
                    self.floatingFilterView.alpha = 1
                    self.searchView.configViewStyle(isLight: false)
                }
            }
        } else {
            if !floatingFilterView.isHidden {
                UIView.animate(withDuration: 0.3, animations: {
                    self.floatingFilterView.alpha = 0
                    self.searchView.configViewStyle(isLight: true)


                }) { _ in
                    self.floatingFilterView.isHidden = true
                }
            }
        }
    }
}

// MARK: - AssemblyNoteCellDelegate

extension AssemblyViewController: AssemblyNoteCellDelegate {
    func assemblyNoteCellDidTapLike(_ cell: AssemblyNoteCell) {
        guard let note = cell.getNote() else { return }
        print("点赞笔记: \(note.id)")
        // 实现点赞逻辑
    }

    func assemblyNoteCellDidTapComment(_ cell: AssemblyNoteCell) {
        guard let note = cell.getNote() else { return }
        print("评论笔记: \(note.id)")
        // 实现评论逻辑
    }
}

// MARK: - AssemblySearchViewDelegate

extension AssemblyViewController: AssemblySearchViewDelegate {
    func assemblySearchView(_ searchView: AssemblySearchView, didSearchWithText searchText: String) {
        // 跳转到搜索页面
        let searchController = AssemblySearchController()
        searchController.noteType = viewModel.currentNoteType
        navigationController?.pushViewController(searchController, animated: true)
    }

    func assemblySearchViewDidTapMenu(_ searchView: AssemblySearchView) {
        // 处理菜单按钮点击事件
        showSlideMenu()
    }

    func assemblySearchViewDidTapScan(_ searchView: AssemblySearchView) {
        // 处理扫描按钮点击事件
        showQrCode()
    }
}

// MARK: - AssemblyFilterViewDelegate

extension AssemblyViewController: AssemblyFilterViewDelegate {
    func assemblyFilterView(_ filterView: BaseView, didSelectNoteType type: NoteType) {
        // 更新ViewModel中的笔记类型
        viewModel.updateNoteType(type)
        
        // 同步筛选视图选择状态
        syncFilterSelections(sourceView: filterView, selectionType: .noteType)
    }

    func assemblyFilterView(_ filterView: BaseView, didSelectRegion region: RegionVenueItemModel) {
        // 更新ViewModel中的地区
        viewModel.updateRegion(region)

        // 同步筛选视图选择状态
        syncFilterSelections(sourceView: filterView, selectionType: .region)
    }

    func assemblyFilterView(_ filterView: BaseView, didSelectVenue venue: VenueItemModel) {
        // 更新ViewModel中的场地
        viewModel.updateVenue(venue)

        // 同步筛选视图选择状态
        syncFilterSelections(sourceView: filterView, selectionType: .venue)
    }
    
    // MARK: - 筛选视图联动
    
    /// 筛选类型
    private enum FilterSelectionType {
        case noteType
        case region
        case venue
    }
    /// 同步筛选视图选择状态
    private func syncFilterSelections(sourceView: BaseView, selectionType: FilterSelectionType) {
        // 根据事件源视图和选择类型同步筛选状态
        if sourceView === self.filterView {
            // 原始筛选视图触发事件，同步到悬浮筛选视图
            syncFromOriginalToFloating(selectionType: selectionType)
        } else if sourceView === floatingFilterView {
            // 悬浮筛选视图触发事件，同步到原始筛选视图
            syncFromFloatingToOriginal(selectionType: selectionType)
        }
    }
    
    /// 从原始筛选视图同步到悬浮筛选视图
    private func syncFromOriginalToFloating(selectionType: FilterSelectionType) {
        switch selectionType {
        case .noteType:
            let selectedIndex = self.filterView.getSelectedNoteTypeIndex()
            floatingFilterView.setSelectedNoteType(index: selectedIndex)
        case .region:
            let selectedIndex = self.filterView.getSelectedRegionIndex()
            floatingFilterView.setSelectedRegion(index: selectedIndex)
        case .venue:
            let selectedIndex = self.filterView.getSelectedVenueIndex()
            floatingFilterView.setSelectedVenue(index: selectedIndex)
        }
    }
    
    /// 从悬浮筛选视图同步到原始筛选视图
    private func syncFromFloatingToOriginal(selectionType: FilterSelectionType) {
        switch selectionType {
        case .noteType:
            let selectedIndex = floatingFilterView.getSelectedNoteTypeIndex()
            self.filterView.setSelectedNoteType(index: selectedIndex)
        case .region:
            let selectedIndex = floatingFilterView.getSelectedRegionIndex()
            self.filterView.setSelectedRegion(index: selectedIndex)
        case .venue:
            let selectedIndex = floatingFilterView.getSelectedVenueIndex()
            self.filterView.setSelectedVenue(index: selectedIndex)
        }
    }
}

