import Foundation

/// 笔记类型枚举
enum NoteType: Int {
    
    case all = 1
    case findPerson = 2
    case photoService = 3
    case findHouse = 4
    case findMisc = 5
    case findCar = 6
    case chatting = 7
    case casual = 8

    /// 标签标题
    var title: String {
        switch self {
        case .all: return "全部"
        case .findPerson: return "找人"
        case .photoService: return "摄影"
        case .findHouse: return "找房"
        case .findMisc: return "找闲置"
        case .findCar: return "找车"
        case .chatting: return "有话说"
        case .casual: return "随便问"
        }
    }
    
    /// 根据接口类型获取NoteType
    static func fromServiceType(_ serviceType: ServiceType) -> NoteType {
        switch serviceType {
        case .all: return .all
        case .people: return .findPerson
        case .photo: return .photoService
        case .rent: return .findHouse
        case .misc: return .findMisc
        case .car: return .findCar
        case .talk: return .chatting
        case .question: return .casual
        }
    }
    
    /// 转换为ServiceType
    var toServiceType: ServiceType {
        switch self {
        case .all: return .all
        case .findPerson: return .people
        case .photoService: return .photo
        case .findHouse: return .rent
        case .findMisc: return .misc
        case .findCar: return .car
        case .chatting: return .talk
        case .casual: return .question
        }
    }
}

/// 地区模型
struct RegionModel {
    let id: String
    let name: String
    var isSelected: Bool = false
}

/// 场地模型
struct VenueModel {
    let id: String
    let name: String
    var isSelected: Bool = false
}

/// 模拟数据提供（保留笔记类型数据）
class AssemblyDataProvider {
    /// 获取笔记类型列表
    static func getNoteTypes() -> [NoteType] {
        return [.all, .findPerson, .findMisc, .chatting, .findCar, .casual, .findHouse]
    }
}
