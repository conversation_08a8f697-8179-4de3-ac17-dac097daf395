//
//  RegionVenueModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/29.
//

import Foundation
import SmartCodable

/// 滑雪城市与雪场列表响应模型
struct RegionVenueListModel: SmartCodable {
    /// 城市列表
    var regions: [RegionVenueItemModel] = []
}

/// 城市与雪场项模型
struct RegionVenueItemModel: SmartCodable {
    /// 城市名称（展示用）
    var name: String = ""
    
    /// 城市值（提交用）
    var value: String = ""
    
    /// 雪场列表
    var venues: [VenueItemModel] = []
}

/// 雪场项模型
struct VenueItemModel: SmartCodable {
    /// 雪场名称（展示用）
    var name: String = ""
    
    /// 雪场值（提交用）
    var value: String = ""
}

/// 扩展RegionVenueItemModel以兼容现有的RegionModel
extension RegionVenueItemModel {
    /// 转换为RegionModel
    func toRegionModel(isSelected: Bool = false) -> RegionModel {
        return RegionModel(id: value, name: name, isSelected: isSelected)
    }
    
    /// 获取所有雪场的RegionModel列表（用于场地筛选）
    func getVenueModels() -> [VenueModel] {
        return venues.map { venue in
            VenueModel(id: venue.value, name: venue.name, isSelected: false)
        }
    }
}

/// 扩展VenueItemModel以兼容现有的VenueModel
extension VenueItemModel {
    /// 转换为VenueModel
    func toVenueModel(isSelected: Bool = false) -> VenueModel {
        return VenueModel(id: value, name: name, isSelected: isSelected)
    }
}
