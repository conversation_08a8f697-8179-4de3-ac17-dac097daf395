import Foundation
import Combine

/// 集结搜索页面ViewModel
class AssemblySearchViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    /// 搜索结果列表
    @Published var notes: [RecommendModel] = []
    
    /// 搜索关键词
    @Published var searchKeyword: String = ""
    
    /// 笔记类型（从AssemblyViewController传入）
    var noteType: NoteType = .all
    
    // MARK: - 初始化
    
    override init() {
        super.init()
    }
    
    // MARK: - 网络请求方法
    
    /// 搜索笔记数据
    /// - Parameter isRefresh: 是否为刷新操作
    func searchNotes(isRefresh: Bool = true) {
        guard !searchKeyword.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines).isEmpty else {
            // 如果搜索关键词为空，清空结果
            notes = []
            setEmptyDataSet(type: .noSearchResult)
            return
        }
        
        updateRefreshState(isRefresh: isRefresh)
        
        // 如果是刷新操作且当前没有数据，显示加载状态
        if isRefresh && notes.isEmpty {
            setEmptyDataSet(type: .loading)
        }
        
        // 构建请求参数
        let params = buildSearchRequestParams()
        
        // 根据当前选择的帖子类型获取对应的服务类型
        let service = AssemblyService.listServiceBy(type: noteType.toServiceType, params: params)
        
        // 使用便捷的分页数据请求方法
        requestPageData(service, type: RecommendModel.self, isRefresh: isRefresh) { [weak self] newData, isRefresh in
            guard let self = self else { return }
            
            if isRefresh {
                self.notes = newData
            } else {
                self.notes.append(contentsOf: newData)
            }
            
            // 根据数据数量更新空数据状态
            self.updateEmptyDataSetForSearchResults(count: self.notes.count)
        }
        .sink(
            receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.handleError(error, operation: "搜索\(self?.noteType.title ?? "")内容")
                    // 如果是首次搜索失败且没有数据，显示错误状态
                    if self?.notes.isEmpty == true {
                        self?.setEmptyDataSet(type: .error)
                    }
                }
            },
            receiveValue: { [weak self] _ in
                guard let self = self else { return }
                print("搜索\(self.noteType.title)成功，关键词：\(self.searchKeyword)，共\(self.notes.count)条结果")
            }
        )
        .store(in: &cancellables)
    }
    
    // MARK: - 重写BaseViewModel方法
    
    /// 下拉刷新回调
    override func refreshData() {
        searchNotes(isRefresh: true)
    }
    
    /// 上拉加载回调
    override func loadMoreData() {
        searchNotes(isRefresh: false)
    }
    
    // MARK: - 私有辅助方法
    
    /// 更新刷新状态
    /// - Parameter isRefresh: 是否为刷新操作
    private func updateRefreshState(isRefresh: Bool) {
        if isRefresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }
    }
    
    /// 构建搜索请求参数
    private func buildSearchRequestParams() -> [String: Any] {
        let params: [String: Any] = [
            "page": currentPage,
            "limit": pageSize,
            "keyword": searchKeyword.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
        ]
        
        return params
    }
    
    /// 更新搜索结果的空数据状态
    /// - Parameter count: 数据数量
    private func updateEmptyDataSetForSearchResults(count: Int) {
        if count == 0 {
            setEmptyDataSet(type: .noSearchResult)
        } else {
            setEmptyDataSet(type: .custom(EmptyDataSetConfig(
                image: nil,
                title: nil,
                description: nil,
                buttonTitle: nil,
                buttonAction: nil
            )))
        }
    }
    
    /// 统一的错误处理方法
    /// - Parameters:
    ///   - error: 网络错误
    ///   - operation: 操作描述
    override func handleError(_ error: NetworkError, operation: String) {
        // 调用父类方法处理空数据状态
        super.handleError(error, operation: operation)
        
        // 添加特定的错误处理逻辑
        switch error {
        case .networkError(let response):
            print("\(operation)失败: \(response.message)")
        case .decodingError(let message):
            print("\(operation)数据解析错误: \(message)")
        case .noConnection:
            print("\(operation)失败: 网络连接失败")
        case .tokenExpired:
            print("\(operation)失败: Token已过期")
        case .tokenError:
            print("\(operation)失败: Token错误")
        }
    }
}
