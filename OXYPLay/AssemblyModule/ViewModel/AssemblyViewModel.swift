//
//  AssemblyViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import Foundation
import Combine

/// 集结页面ViewModel - 负责管理不同类型的服务帖子列表和筛选条件
class AssemblyViewModel: BaseViewModel {

    // MARK: - Published Properties

    /// 帖子列表数据
    @Published var notes: [RecommendModel] = []

    /// 当前选择的帖子类型
    @Published var currentNoteType: NoteType = .all

    /// 当前选择的地区
    @Published var currentRegion: RegionVenueItemModel?

    /// 当前选择的场地
    @Published var currentVenue: VenueItemModel?

    /// 地区列表数据
    @Published var regions: [RegionVenueItemModel] = []

    /// 场地列表数据
    @Published var venues: [VenueItemModel] = []

    /// 原始的地区场地数据（用于根据地区筛选场地）
    private var regionVenueData: [RegionVenueItemModel] = []

    // MARK: - 初始化

    override init() {
        super.init()
        setupDefaultFilters()
        fetchRegionVenueList()
    }

    /// 设置默认筛选条件
    private func setupDefaultFilters() {
        // 初始化默认地区和场地（使用"全部"选项）
        let allRegion = RegionVenueItemModel(name: "全部", value: "0", venues: [])
        let allVenue = VenueItemModel(name: "全部", value: "0")

        currentRegion = allRegion
        currentVenue = allVenue

        // 初始化列表数据
        regions = [allRegion]
        venues = [allVenue]
    }

    // MARK: - 数据加载方法

    /// 重写刷新数据方法
    override func refreshData() {
        fetchNotes(isRefresh: true)
    }

    /// 重写加载更多数据方法
    override func loadMoreData() {
        fetchNotes(isRefresh: false)
    }

    /// 获取滑雪城市与雪场列表
    func fetchRegionVenueList() {
        requestModel(AssemblyService.regionVenueList, type: RegionVenueListModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.handleError(error, operation: "获取地区场地列表")
                        // 如果接口失败，使用默认数据
                        self?.setupFallbackData()
                    }
                },
                receiveValue: { [weak self] regionVenueList in
                    self?.processRegionVenueData(regionVenueList)
                }
            )
            .store(in: &cancellables)
    }

    /// 处理地区场地数据
    private func processRegionVenueData(_ data: RegionVenueListModel) {
        // 保存原始数据
        regionVenueData = data.regions

        // 构建地区列表（添加"全部"选项）
        var regionList = [RegionVenueItemModel(name: "全部", value: "0", venues: [])]
        regionList.append(contentsOf: data.regions)
        regions = regionList

        // 构建场地列表（添加"全部"选项，包含所有场地）
        var venueList = [VenueItemModel(name: "全部", value: "0")]
        for region in data.regions {
            venueList.append(contentsOf: region.venues)
        }
        venues = venueList

        print("成功加载地区场地数据：\(data.regions.count)个地区")
    }

    /// 设置备用数据（当接口失败时使用）
    private func setupFallbackData() {
        // 创建备用数据，模拟接口返回的数据结构
        let fallbackRegions = [
            RegionVenueItemModel(name: "全部", value: "0", venues: []),
            RegionVenueItemModel(name: "崇礼", value: "崇礼", venues: [
                VenueItemModel(name: "富龙", value: "富龙"),
                VenueItemModel(name: "云顶", value: "云顶"),
                VenueItemModel(name: "万龙", value: "万龙")
            ]),
            RegionVenueItemModel(name: "新疆", value: "新疆", venues: [
                VenueItemModel(name: "丝绸之路", value: "丝绸之路"),
                VenueItemModel(name: "将军山", value: "将军山")
            ])
        ]

        regions = fallbackRegions

        // 构建场地列表
        var venueList = [VenueItemModel(name: "全部", value: "0")]
        for region in fallbackRegions where region.value != "0" {
            venueList.append(contentsOf: region.venues)
        }
        venues = venueList

        print("使用备用地区场地数据")
    }

    /// 加载帖子列表数据
    /// - Parameter isRefresh: 是否为刷新操作（true: 下拉刷新, false: 上拉加载更多）
    func fetchNotes(isRefresh: Bool = true) {
        updateRefreshState(isRefresh: isRefresh)

        // 如果是刷新操作且当前没有数据，显示加载状态
        if isRefresh && notes.isEmpty {
            setEmptyDataSet(type: .loading)
        }

        // 构建请求参数
        let params = buildRequestParams()

        // 根据当前选择的帖子类型获取对应的服务类型
        let service = AssemblyService.listServiceBy(type: currentNoteType.toServiceType, params: params)

        // 使用便捷的分页数据请求方法
        requestPageData(service, type: RecommendModel.self, isRefresh: isRefresh) { [weak self] newData, isRefresh in
            guard let self = self else { return }

            if isRefresh {
                self.notes = newData
            } else {
                self.notes.append(contentsOf: newData)
            }

            // 根据数据数量更新空数据状态
            self.updateEmptyDataSetForData(count: self.notes.count)
        }
        .sink(
            receiveCompletion: { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.handleError(error, operation: "获取\(self?.currentNoteType.title ?? "")列表")
                    // 如果是首次加载失败且没有数据，显示错误状态
                    if self?.notes.isEmpty == true {
                        self?.setEmptyDataSet(type: .error)
                    }
                }
            },
            receiveValue: { [weak self] _ in
                guard let self = self else { return }
                print("\(self.currentNoteType.title)列表加载成功，共\(self.notes.count)条数据")
            }
        )
        .store(in: &cancellables)
    }


    // MARK: - 筛选条件更新方法

    /// 更新筛选条件 - 帖子类型
    /// - Parameter noteType: 新的帖子类型
    func updateNoteType(_ noteType: NoteType) {
        if currentNoteType != noteType {
            currentNoteType = noteType
            print("切换到\(noteType.title)类型")
            refreshData()
        }
    }

    /// 更新筛选条件 - 地区
    /// - Parameter region: 新的地区
    func updateRegion(_ region: RegionVenueItemModel) {
        if currentRegion?.value != region.value {
            currentRegion = region
            print("切换到地区: \(region.name)")

            // 根据选择的地区更新场地列表
            updateVenuesForRegion(region)

            refreshData()
        }
    }

    /// 根据地区更新场地列表
    /// - Parameter region: 选择的地区
    private func updateVenuesForRegion(_ region: RegionVenueItemModel) {
        var venueList = [VenueItemModel(name: "全部", value: "0")]

        if region.value == "0" {
            // 选择"全部"地区时，显示所有场地
            for regionData in regionVenueData {
                venueList.append(contentsOf: regionData.venues)
            }
        } else {
            // 选择特定地区时，只显示该地区的场地
            if let selectedRegionData = regionVenueData.first(where: { $0.value == region.value }) {
                venueList.append(contentsOf: selectedRegionData.venues)
            }
        }

        venues = venueList

        // 重置场地选择为"全部"
        currentVenue = venueList.first

        print("更新场地列表：\(venueList.count - 1)个场地")
    }

    /// 更新筛选条件 - 场地
    /// - Parameter venue: 新的场地
    func updateVenue(_ venue: VenueItemModel) {
        if currentVenue?.value != venue.value {
            currentVenue = venue
            print("切换到场地: \(venue.name)")
            refreshData()
        }
    }

    /// 重置所有筛选条件
    func resetFilters() {
        currentNoteType = .all
        currentRegion = regions.first
        currentVenue = venues.first
        print("重置所有筛选条件")
        refreshData()
    }

    // MARK: - 私有辅助方法

    /// 更新刷新状态
    /// - Parameter isRefresh: 是否为刷新操作
    private func updateRefreshState(isRefresh: Bool) {
        if isRefresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }
    }

    /// 构建请求参数
    private func buildRequestParams() -> [String: Any] {
        var params: [String: Any] = [
            "page": currentPage,
            "limit": pageSize
        ]

        // 添加地区筛选（排除"全部"选项）
        if let region = currentRegion, region.value != "0" {
            params["location"] = region.name
        }

        // 添加场地筛选（如果需要的话）
        if let venue = currentVenue, venue.value != "0" {
            params["venue"] = venue.name
        }

        return params
    }

    /// 统一的错误处理方法
    /// - Parameters:
    ///   - error: 网络错误
    ///   - operation: 操作描述
    override func handleError(_ error: NetworkError, operation: String) {
        // 调用父类方法处理空数据状态
        super.handleError(error, operation: operation)

        // 添加特定的错误处理逻辑
        switch error {
        case .networkError(let response):
            print("\(operation)失败: \(response.message)")
        case .decodingError(let message):
            print("\(operation)数据解析错误: \(message)")
        case .noConnection:
            print("\(operation)失败: 网络连接失败")
        case .tokenExpired:
            print("\(operation)失败: Token已过期")
        case .tokenError:
            print("\(operation)失败: Token错误")
        }
    }
}
