//
//  Macro_Color.swift
//  OXYPLay
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/21.
//
let color_F3F6F7 = UIColor(hexString: "F3F6F7", transparency: 1)! // 控制器背景
let color_2B2C2F = UIColor(hexString: "2B2C2F", transparency: 1)!// 主文本色1
let color_2B2C2F80 = UIColor(hexString: "2B2C2F", transparency: 0.8)!// 主文本色2
let color_2B2C2F64 = UIColor(hexString: "2B2C2F", transparency: 0.64)!// 主文本色3
let color_2B2C2F48 = UIColor(hexString: "2B2C2F", transparency: 0.48)!// 主文本色4
let color_2B2C2F40 = UIColor(hexString: "2B2C2F", transparency: 0.4)!// 主文本色5

let color_red =  UIColor(hexString: "FF0000", transparency: 1)!//红色
let color_blue = UIColor(hexString: "2A72FF", transparency: 1.0)!//蓝色
let color_FF8C00 = UIColor(hexString: "FF8C00", transparency: 1)! // 橙色
let color_52C41A = UIColor(hexString: "52C41A", transparency: 1)! // 绿色

let color_FFFFFF80 = UIColor(hexString: "FFFFFF", transparency: 0.8)!
let color_FFFFFF20 = UIColor(hexString: "FFFFFF", transparency: 0.2)!
let color_00000004 = UIColor(hexString: "000000", transparency: 0.04)!
let color_2B2C2F72 = UIColor(hexString: "2B2C2F", transparency: 0.72)!
let color_48A1FF = UIColor(hexString: "48A1FF", transparency: 1.0)!
let color_3D3E40 = UIColor(hexString: "3D3E40", transparency: 1.0)!
let color_3D3E4012 = UIColor(hexString: "3D3E40", transparency: 0.12)!
let color_2B2C2F24 = UIColor(hexString: "2B2C2F", transparency: 0.24)!
let color_F6F8F9 = UIColor(hexString: "F6F8F9", transparency: 1.0)!
let color_2B2C2F04 = UIColor(hexString: "2B2C2F", transparency: 0.04)!
let color_686A6D80 = UIColor(hexString: "686A6D", transparency: 0.8)!
let color_8D9096 = UIColor(hexString: "8D9096", transparency: 1)!
let color_999DA1 = UIColor(hexString: "999DA1", transparency: 1)!
let color_FFFFFF72 = UIColor(hexString: "FFFFFF", transparency: 0.72)!
let color_999999 = UIColor(hexString: "999999", transparency: 1)!
let color_666666 = UIColor(hexString: "666666", transparency: 1)!
let color_E5E5E5 = UIColor(hexString: "E5E5E5", transparency: 1)!
let color_FF5D48 = UIColor(hexString: "FF5D48", transparency: 1)!
let color_FF3434 = UIColor(hexString: "FF3434", transparency: 1)!
let color_FFFFFF = UIColor(hexString: "FFFFFF", transparency: 1)!
let color_F5F5F5 = UIColor(hexString: "F5F5F5", transparency: 1)!

// 进度条专用颜色
let color_progress_blue_light = UIColor(hexString: "2A72FF", transparency: 0.2)! // 已完成连接线
let color_progress_blue_dark = UIColor(hexString: "2A72FF", transparency: 1)! // 当前进度连接线
let color_progress_gray = UIColor(hexString: "788092", transparency: 0.08)! // 未完成连接线




