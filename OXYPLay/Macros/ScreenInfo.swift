//
//  ScreenInfo.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/23.
//

import UIKit
/// 提供屏幕尺寸、安全区域、导航栏和状态栏高度等关键信息
public enum ScreenInfo {
    
    // MARK: - 屏幕基本信息
    
    /// 屏幕尺寸
    public static var size: CGSize { UIScreen.main.bounds.size }
    
    /// 屏幕宽度
    public static var width: CGFloat { size.width }
    
    /// 屏幕高度
    public static var height: CGFloat { size.height }
    
    /// 屏幕比例
    public static var scale: CGFloat { UIScreen.main.scale }
    
    // MARK: - 安全区域
    
    /// 获取当前活动窗口
    public static var activeWindow: UIWindow? {
        if #available(iOS 15.0, *) {
            return UIApplication.shared
                .connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .first(where: { $0.activationState == .foregroundActive })?
                .windows
                .first(where: \.isKeyWindow)
        } else if #available(iOS 13.0, *) {
            return UIApplication.shared.windows.first(where: \.isKeyWindow)
        } else {
            return UIApplication.shared.keyWindow
        }
    }
    
    
    // MARK: - 导航栏和状态栏高度
    
    /// 标准导航栏高度（不含状态栏）
    public static let standardNavBarHeight: CGFloat = 44.0
    
    /// 状态栏高度（取固定值，适配不同设备）
    public static var statusBarHeight: CGFloat {
        if iphoneX {
            return 44.0 // 刘海屏设备状态栏高度
        } else {
            return 20.0 // 传统设备状态栏高度
        }
    }
    
    /// 导航栏和状态栏总高度（固定值）
    public static var totalNavBarHeight: CGFloat {
        return statusBarHeight + standardNavBarHeight
    }
    
    // MARK: - TabBar高度
    
    /// 标准TabBar高度（不含底部安全区域）
    public static let standardTabBarHeight: CGFloat = 49.0
    
    /// TabBar总高度（包含底部安全区域）
    public static var totalTabBarHeight: CGFloat {
        return standardTabBarHeight + (iphoneX ? 34.0 : 0.0)
    }
}
