//
//  Macro.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/26.
//

@_exported import King<PERSON><PERSON>
@_exported import Moya
@_exported import Combine
@_exported import SnapKit
@_exported import SwifterSwift
@_exported import SwiftyJSON
@_exported import Then
@_exported import SmartCodable
@_exported import CombineCocoa
@_exported import SwiftDate

import UIKit

// MARK: - 屏幕尺寸
let kScreenWidth = UIScreen.main.bounds.size.width
let kScreenHeight = UIScreen.main.bounds.size.height



let iphoneX = ((statusBarHeight != 20) ? true : false)
//返回状态栏高度
#if __IPHONE_13_0
let statusBarHeight = UIApplication.shared.windows.first?.windowScene?.statusBarManager?.statusBarFrame.size.height
#else
let statusBarHeight = UIApplication.shared.statusBarFrame.size.height
#endif
