//
//  AppNotifications.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/25.
//
import Foundation

struct AppNotifications {
    static let NotificationUserDidLogin = Notification.Name("UserDidLoginNotification")
    static let NotificationUserDidLogout = Notification.Name("UserDidLogoutNotification")
    static let userTokenExpired = Notification.Name("UserTokenExpiredNotification")
    /// 用户关注数和粉丝数更新通知
    static let userFollowCountUpdated = Notification.Name("UserFollowCountUpdatedNotification")
}
