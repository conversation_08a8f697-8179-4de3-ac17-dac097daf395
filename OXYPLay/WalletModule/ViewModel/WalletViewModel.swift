//
//  WalletViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/24.
//

import Foundation
import Combine

/// 钱包模块ViewModel
class WalletViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    /// 钱包概览数据
    @Published var walletSummary: WalletSummaryModel?
    
    /// 账单明细数据
    @Published var transactionList: WalletTransactionListModel?
    
    /// 提现明细数据
    @Published var withdrawList: WalletWithdrawListModel?
    
    /// 提现申请结果
    @Published var withdrawRequestResult: String?

    /// 充值结果
    @Published var rechargeResult: String?

    // MARK: - Private Properties
    
  
    // MARK: - Public Methods
    
    /// 获取钱包概览（余额 + 优惠券）
    func fetchWalletSummary() {
        let request = WalletSummaryRequest()
        
        requestModel(WalletService.summary(params: request), type: WalletSummaryModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error)
                    }
                },
                receiveValue: { [weak self] summary in
                    self?.walletSummary = summary
                }
            )
            .store(in: &cancellables)
    }
    
    /// 获取钱包账单明细
    /// - Parameters:
    ///   - month: 查询月份，格式为 YYYY-MM，不填则为当前月
    ///   - type: 账单类型
    func fetchTransactionList(month: String? = nil, type: TransactionType? = nil) {
        let request = WalletTransactionListRequest(
            month: month,
            type: type?.rawValue
        )
        
        requestModel(WalletService.transactionList(params: request), type: WalletTransactionListModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error)
                    }
                },
                receiveValue: { [weak self] transactionList in
                    self?.transactionList = transactionList
                }
            )
            .store(in: &cancellables)
    }
    
    /// 获取提现明细
    /// - Parameters:
    ///   - month: 指定月份，格式为 YYYY-MM，不传则默认返回最近三个月
    ///   - page: 分页页码，默认 1
    ///   - limit: 每页数量，默认 20
    func fetchWithdrawList(month: String? = nil, page: Int = 1, limit: Int = 20) {
        let request = WalletWithdrawListRequest(
            month: month,
            page: page,
            limit: limit
        )
        
        requestModel(WalletService.withdrawList(params: request), type: WalletWithdrawListModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error)
                    }
                },
                receiveValue: { [weak self] withdrawList in
                    self?.withdrawList = withdrawList
                }
            )
            .store(in: &cancellables)
    }
    
    /// 提现申请
    /// - Parameter amount: 提现金额
    func submitWithdrawRequest(amount: Float) {
        let withdrawRequest = WalletWithdrawRequest(amount: amount)

        request(WalletService.withdrawRequest(params: withdrawRequest))
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error)
                    }
                },
                receiveValue: { [weak self] response in
                    self?.withdrawRequestResult = response.message
                    // 提现申请成功后，刷新钱包概览和提现明细
                    self?.fetchWalletSummary()
                    self?.fetchWithdrawList()
                }
            )
            .store(in: &cancellables)
    }

    /// 提交充值申请
    /// - Parameters:
    ///   - amount: 充值金额
    ///   - paymentMethod: 支付方式
    func submitRechargeRequest(amount: Float, paymentMethod: String) {
        let rechargeRequest = WalletRechargeRequest(amount: amount, paymentMethod: paymentMethod)

        request(WalletService.rechargeRequest(params: rechargeRequest))
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error)
                    }
                },
                receiveValue: { [weak self] response in
                    self?.rechargeResult = response.message
                    // 充值申请成功后，刷新钱包概览
                    self?.fetchWalletSummary()
                }
            )
            .store(in: &cancellables)
    }

    // MARK: - Private Methods
    
    /// 处理网络错误
    override func handleError(_ error: NetworkError, operation: String = "请求") {
        // 调用父类方法处理空数据状态
        super.handleError(error, operation: operation)

        // 添加特定的错误处理逻辑
        switch error {
        case .networkError(let response):
            // 可以在这里处理特定的错误逻辑
            print("钱包接口错误: \(response.message)")
        case .decodingError(let message):
            print("数据解析错误: \(message)")
        case .noConnection:
            print("网络连接失败")
        case .tokenExpired:
            print("Token已过期")
        case .tokenError:
            print("Token错误")
        }
    }
}

// MARK: - 便利方法扩展

extension WalletViewModel {
    
    /// 获取当前月份的账单明细
    func fetchCurrentMonthTransactions() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM"
        let currentMonth = formatter.string(from: Date())
        fetchTransactionList(month: currentMonth)
    }
    
    /// 获取指定类型的账单明细
    func fetchTransactionsByType(_ type: TransactionType) {
        fetchTransactionList(type: type)
    }
    
    /// 刷新所有钱包数据
    func refreshAllData() {
        fetchWalletSummary()
        fetchCurrentMonthTransactions()
        fetchWithdrawList()
    }
}
