//
//  WalletModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/24.
//

import Foundation
import SmartCodable

// MARK: - 钱包概览模型

/// 钱包概览响应模型
struct WalletSummaryModel: SmartCodable {
    /// 钱包总余额（含冻结部分）
    var balance_amount: Float = 0.0
    
    /// 可用余额（余额 - 冻结金额）
    var available_amount: Float = 0.0
    
    /// 冻结金额（如提现中资金）
    var frozen_amount: Float = 0.0
    
    /// 优惠券列表
    var couponList: [WalletCouponModel] = []
    
    /// 格式化的总余额显示
    var formattedBalanceAmount: String {
        return String(format: "%.2f", balance_amount)
    }
    
    /// 格式化的可用余额显示
    var formattedAvailableAmount: String {
        return String(format: "%.2f", available_amount)
    }
    
    /// 格式化的冻结金额显示
    var formattedFrozenAmount: String {
        return String(format: "%.2f", frozen_amount)
    }
}

/// 钱包优惠券模型
struct WalletCouponModel: SmartCodable {
    /// 优惠券 ID
    var id: Int = 0
    
    /// 优惠券标题
    var title: String = ""
    
    /// 优惠金额
    var amount: Float = 0.0
    
    /// 最低使用门槛金额
    var min_spend: Float = 0.0
    
    /// 过期时间（格式：Y-m-d H:i:s）
    var expire_at: String = ""
    
    /// 是否已使用（0否 1是）
    var is_used: Int = 0
    
    /// 状态说明（可用/已用/已过期）
    var status_text: String = ""
    
    /// 是否可用
    var isAvailable: Bool {
        return is_used == 0 && status_text == "可用"
    }
    
    /// 格式化的优惠金额显示
    var formattedAmount: String {
        return String(format: "%.1f", amount)
    }
    
    /// 格式化的最低消费显示
    var formattedMinSpend: String {
        return String(format: "%.1f", min_spend)
    }
}

// MARK: - 账单明细模型

/// 钱包账单明细响应模型
struct WalletTransactionListModel: SmartCodable {
    /// 当前返回的月份
    var month: String = ""
    
    /// 本月总收入（已过滤解冻记录）
    var income_total: String = ""
    
    /// 本月总支出（包括退款、提现）
    var expense_total: String = ""
    
    /// 明细记录数组，每条为一笔账单交易
    var records: [TransactionRecord] = []
}

/// 交易记录模型
struct TransactionRecord: SmartCodable {
    /// 交易备注，例如"卖出商品"
    var remark: String = ""
    
    /// 交易时间，格式为 YYYY.MM.DD HH:mm:ss
    var display_date: String = ""
    
    /// 显示金额，支出为负号，收入为正号
    var display_amount: String = ""
    
    /// 交易类型文本说明
    var typeText: String = ""
    
    /// 是否为收入
    var isIncome: Bool {
        return display_amount.hasPrefix("+")
    }
    
    /// 是否为支出
    var isExpense: Bool {
        return display_amount.hasPrefix("-")
    }
}

// MARK: - 提现明细模型

/// 提现明细响应模型
struct WalletWithdrawListModel: SmartCodable {
    /// 月份分组的提现记录数组
    var list: [WithdrawMonthGroup] = []
    
    /// 当前页码
    var page: Int = 1
    
    /// 每页记录数
    var limit: Int = 20
}

/// 提现月份分组模型
struct WithdrawMonthGroup: SmartCodable {
    /// 月份，格式如 2025年07月
    var month: String = ""
    
    /// 当月的提现记录数量
    var total_count: Int = 0
    
    /// 当月的提现记录列表
    var items: [WithdrawRecord] = []
}

/// 提现记录模型
struct WithdrawRecord: SmartCodable {
    /// 提现记录ID
    var id: Int = 0
    
    /// 提现金额（单位：元）
    var amount: String = ""
    
    /// 状态码：0待审核，1已打款，2已拒绝
    var status: Int = 0
    
    /// 状态文本描述，如"待审核"
    var status_text: String = ""
    
    /// 状态颜色，用于 UI 显示
    var status_color: String = ""
    
    /// 时间标签说明，如"打款时间"
    var time_label: String = ""
    
    /// 对应时间字段，如 2025-07-17 12:00:00
    var time: String = ""
    
    /// 审批内容
    var audit_remark: String = ""
    
    /// 提现状态枚举
    var withdrawStatus: WithdrawStatus {
        return WithdrawStatus(rawValue: status) ?? .pending
    }
}

/// 提现状态枚举
enum WithdrawStatus: Int, CaseIterable {
    case pending = 0    // 待审核
    case completed = 1  // 已打款
    case rejected = 2   // 已拒绝
    
    var displayText: String {
        switch self {
        case .pending:
            return "待审核"
        case .completed:
            return "已打款"
        case .rejected:
            return "已拒绝"
        }
    }
    
    var color: String {
        switch self {
        case .pending:
            return "#faad14"
        case .completed:
            return "#52c41a"
        case .rejected:
            return "#ff4d4f"
        }
    }
}

// MARK: - 提现响应模型

/// 提现申请响应模型
struct WalletWithdrawResponseModel: Codable {
    /// 响应消息
    let message: String

    /// 提现申请ID（可选）
    let withdrawId: String?

    enum CodingKeys: String, CodingKey {
        case message
        case withdrawId = "withdraw_id"
    }
}

// MARK: - 充值响应模型

/// 充值申请响应模型
struct WalletRechargeResponseModel: Codable {
    /// 响应消息
    let message: String

    /// 充值订单ID（可选）
    let orderId: String?

    /// 支付链接（可选）
    let paymentUrl: String?

    enum CodingKeys: String, CodingKey {
        case message
        case orderId = "order_id"
        case paymentUrl = "payment_url"
    }
}
