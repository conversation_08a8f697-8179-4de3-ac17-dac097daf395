//
//  WalletRequestModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/24.
//

import Foundation

// MARK: - 钱包API请求参数模型

/// 钱包概览请求参数（无参数，仅需登录状态）
struct WalletSummaryRequest: RequestParametersConvertible {
    func asParameters() -> [String: Any] {
        return [:]
    }
}

/// 钱包账单明细请求参数
struct WalletTransactionListRequest: RequestParametersConvertible {
    /// 查询月份，格式为 YYYY-MM，不填则为当前月
    var month: String?
    
    /// 账单类型，取值：recharge（充值）、income（收入）、refund（退款）、withdraw（提现）
    var type: String?
    
    init(month: String? = nil, type: String? = nil) {
        self.month = month
        self.type = type
    }
    
    func asParameters() -> [String: Any] {
        var params: [String: Any] = [:]
        
        if let month = month {
            params["month"] = month
        }
        
        if let type = type {
            params["type"] = type
        }
        
        return params
    }
}

/// 提现明细请求参数
struct WalletWithdrawListRequest: RequestParametersConvertible {
    /// 指定月份，格式为 YYYY-MM，不传则默认返回最近三个月
    var month: String?
    
    /// 分页页码，默认 1
    var page: Int
    
    /// 每页数量，默认 20
    var limit: Int
    
    init(month: String? = nil, page: Int = 1, limit: Int = 20) {
        self.month = month
        self.page = page
        self.limit = limit
    }
    
    func asParameters() -> [String: Any] {
        var params: [String: Any] = [
            "page": page,
            "limit": limit
        ]
        
        if let month = month {
            params["month"] = month
        }
        
        return params
    }
}



/// 提现申请请求参数
struct WalletWithdrawRequest: RequestParametersConvertible {
    /// 提现金额（单位：元）
    var amount: Float

    init(amount: Float) {
        self.amount = amount
    }

    func asParameters() -> [String: Any] {
        return [
            "amount": amount
        ]
    }
}

/// 充值申请请求参数
struct WalletRechargeRequest: RequestParametersConvertible {
    /// 充值金额（单位：元）
    var amount: Float

    /// 支付方式
    var paymentMethod: String

    init(amount: Float, paymentMethod: String) {
        self.amount = amount
        self.paymentMethod = paymentMethod
    }

    func asParameters() -> [String: Any] {
        return [
            "amount": amount,
            "payment_method": paymentMethod
        ]
    }
}

// MARK: - 账单类型枚举

/// 账单类型枚举
enum TransactionType: String, CaseIterable {
    case recharge = "recharge"  // 充值
    case income = "income"      // 收入
    case refund = "refund"      // 退款
    case withdraw = "withdraw"  // 提现
    
    var displayText: String {
        switch self {
        case .recharge:
            return "充值"
        case .income:
            return "收入"
        case .refund:
            return "退款"
        case .withdraw:
            return "提现"
        }
    }
}

// MARK: - 便利方法

extension WalletTransactionListRequest {
    /// 创建查询指定月份的请求
    static func forMonth(_ month: String) -> WalletTransactionListRequest {
        return WalletTransactionListRequest(month: month)
    }
    
    /// 创建查询指定类型的请求
    static func forType(_ type: TransactionType) -> WalletTransactionListRequest {
        return WalletTransactionListRequest(type: type.rawValue)
    }
    
    /// 创建查询指定月份和类型的请求
    static func forMonthAndType(_ month: String, type: TransactionType) -> WalletTransactionListRequest {
        return WalletTransactionListRequest(month: month, type: type.rawValue)
    }
}

extension WalletWithdrawListRequest {
    /// 创建查询指定月份的请求
    static func forMonth(_ month: String, page: Int = 1, limit: Int = 20) -> WalletWithdrawListRequest {
        return WalletWithdrawListRequest(month: month, page: page, limit: limit)
    }
    
    /// 创建分页请求
    static func forPage(_ page: Int, limit: Int = 20) -> WalletWithdrawListRequest {
        return WalletWithdrawListRequest(page: page, limit: limit)
    }
}
