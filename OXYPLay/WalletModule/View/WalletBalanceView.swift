//
//  WalletBalanceView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine
import SnapKit

/// 钱包余额视图代理协议
protocol WalletBalanceViewDelegate: AnyObject {
    /// 充值按钮点击事件
    func walletBalanceViewDidTapRecharge(_ view: WalletBalanceView)
    /// 提现按钮点击事件
    func walletBalanceViewDidTapWithdraw(_ view: WalletBalanceView)
    /// 待释放余额信息按钮点击事件
    func walletBalanceViewDidTapFrozenInfo(_ view: WalletBalanceView)
    /// 账单明细点击事件
    func walletBalanceViewDidTapTransaction(_ view: WalletBalanceView)
}

/// 钱包余额视图
class WalletBalanceView: BaseView {
    
    // MARK: - Properties
    
    /// 代理对象
    weak var delegate: WalletBalanceViewDelegate?
    
    // MARK: - UI Components
    
    /// 余额明细容器
    private lazy var balanceContainer = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
    }
    
    /// 钱包余额标题
    private lazy var balanceTitleLabel = UILabel().then {
        $0.text = "钱包余额"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 14, weight: .regular)
    }

    /// 总余额数值标签
    private lazy var totalBalanceValueLabel = UILabel().then {
        $0.text = "¥2333.00"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 28, weight: .medium)
    }

    /// 可用余额容器
    private lazy var availableBalanceContainer = UIView().then {
        $0.backgroundColor = UIColor(hexString: "F6F8F9", transparency: 1)
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
    }

    /// 可用余额标题
    private lazy var availableBalanceTitleLabel = UILabel().then {
        $0.text = "可用余额"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textAlignment = .center
    }

    /// 可用余额数值
    private lazy var availableBalanceValueLabel = UILabel().then {
        $0.text = "¥2333.00"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textAlignment = .center
    }

    /// 待释放余额容器
    private lazy var frozenBalanceContainer = UIView().then {
        $0.backgroundColor = UIColor(hexString: "F6F8F9", transparency: 1)
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
    }

    /// 待释放余额标题
    private lazy var frozenBalanceTitleLabel = UILabel().then {
        $0.text = "待释放余额"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textAlignment = .center
    }

    /// 待释放余额数值
    private lazy var frozenBalanceValueLabel = UILabel().then {
        $0.text = "¥2333.00"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textAlignment = .center
    }

    /// 待释放余额问号图标
    private lazy var frozenInfoButton = UIButton(type: .system).then {
        $0.setImage(UIImage(systemName: "questionmark.circle"), for: .normal)
        $0.tintColor = color_2B2C2F
    }

    /// 功能按钮容器
    private lazy var actionButtonsContainer = UIView()

    /// 充值按钮
    private lazy var rechargeButton = BaseButton().then {
        $0.setTitle("充值", for: .normal)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.backgroundColor = UIColor(hexString: "F6F8F9", transparency: 1)
        $0.isRounded = true
    }

    /// 去提现按钮
    private lazy var withdrawButton = BaseButton().then {
        $0.setTitle("去提现", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.backgroundColor = color_blue
        $0.isRounded = true
    }

    /// 账单明细容器
    private lazy var transactionContainer = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
    }

    /// 账单明细图标
    private lazy var transactionIconView = UIImageView().then {
        $0.image = UIImage(systemName: "doc.text")
        $0.tintColor = color_2B2C2F
        $0.contentMode = .scaleAspectFit
    }

    /// 账单明细标题
    private lazy var transactionTitleLabel = UILabel().then {
        $0.text = "账单明细"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 14, weight: .regular)
    }

    /// 账单明细箭头
    private lazy var transactionArrowView = UIImageView().then {
        $0.image = UIImage(named: "prodcut_order_right_arrow")
        $0.contentMode = .scaleAspectFit
    }

    /// 优惠券标题
    private lazy var couponTitleLabel = UILabel().then {
        $0.text = "优惠券"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
    }
    
    // MARK: - Initialization

    // MARK: - UI Configuration

    override func configUI() {
        backgroundColor = .clear
        // 添加余额容器及其子视图
        addSubview(balanceContainer)
        balanceContainer.addSubview(balanceTitleLabel)
        balanceContainer.addSubview(totalBalanceValueLabel)

        // 添加余额详情容器
        balanceContainer.addSubview(availableBalanceContainer)
        balanceContainer.addSubview(frozenBalanceContainer)

        // 可用余额子视图
        availableBalanceContainer.addSubview(availableBalanceTitleLabel)
        availableBalanceContainer.addSubview(availableBalanceValueLabel)

        // 待释放余额子视图
        frozenBalanceContainer.addSubview(frozenBalanceTitleLabel)
        frozenBalanceContainer.addSubview(frozenBalanceValueLabel)
        frozenBalanceContainer.addSubview(frozenInfoButton)

        // 功能按钮容器
        balanceContainer.addSubview(actionButtonsContainer)
        actionButtonsContainer.addSubview(rechargeButton)
        actionButtonsContainer.addSubview(withdrawButton)

        // 账单明细容器
        addSubview(transactionContainer)
        transactionContainer.addSubview(transactionIconView)
        transactionContainer.addSubview(transactionTitleLabel)
        transactionContainer.addSubview(transactionArrowView)

        // 优惠券标题
        addSubview(couponTitleLabel)
        setupBindings()
    }

 
    /// 设置约束布局
    override func configLayout() {
        // 余额容器布局
        balanceContainer.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
        }

        // 钱包余额标题布局
        balanceTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(12)
        }

        // 总余额数值布局
        totalBalanceValueLabel.snp.makeConstraints { make in
            make.top.equalTo(balanceTitleLabel.snp.bottom).offset(20)
            make.left.equalTo(balanceTitleLabel)
        }

        // 可用余额容器布局
        availableBalanceContainer.snp.makeConstraints { make in
            make.left.equalTo(balanceTitleLabel)
            make.top.equalTo(totalBalanceValueLabel.snp.bottom).offset(12)
            make.right.equalTo(balanceContainer.snp.centerX).offset(-5.5)
        }

        // 待释放余额容器布局
        frozenBalanceContainer.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.top.equalTo(totalBalanceValueLabel.snp.bottom).offset(12)
            make.left.equalTo(balanceContainer.snp.centerX).offset(5.5)
        }

        // 可用余额标题和数值布局
        availableBalanceTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.centerX.equalToSuperview()
        }

        availableBalanceValueLabel.snp.makeConstraints { make in
            make.top.equalTo(availableBalanceTitleLabel.snp.bottom).offset(10)
            make.centerX.equalToSuperview()
            make.bottom.equalTo(-12)
        }

        // 待释放余额标题和数值布局
        frozenBalanceTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.centerX.equalToSuperview()
        }

        frozenInfoButton.snp.makeConstraints { make in
            make.centerY.equalTo(frozenBalanceTitleLabel)
            make.left.equalTo(frozenBalanceTitleLabel.snp.right).offset(5)
            make.width.height.equalTo(11)
        }

        frozenBalanceValueLabel.snp.makeConstraints { make in
            make.top.equalTo(frozenBalanceTitleLabel.snp.bottom).offset(10)
            make.centerX.equalToSuperview()
            make.bottom.equalTo(-12)
        }

        // 功能按钮容器布局
        actionButtonsContainer.snp.makeConstraints { make in
            make.top.equalTo(availableBalanceContainer.snp.bottom)
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(52)
            make.bottom.equalToSuperview()
        }

        // 提现按钮布局
        withdrawButton.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()
            make.width.equalTo(65)
            make.height.equalTo(28)
        }

        // 充值按钮布局
        rechargeButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalTo(withdrawButton.snp.left).offset(-8)
            make.width.equalTo(65)
            make.height.equalTo(28)
        }

        // 账单明细容器布局
        transactionContainer.snp.makeConstraints { make in
            make.top.equalTo(balanceContainer.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.height.equalTo(43)
        }

        // 账单明细图标布局
        transactionIconView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(12)
            make.width.height.equalTo(24)
        }

        // 账单明细标题布局
        transactionTitleLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(transactionIconView.snp.right).offset(8)
        }

        // 账单明细箭头布局
        transactionArrowView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-12)
        }

        // 优惠券标题布局
        couponTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(transactionContainer.snp.bottom).offset(24)
            make.left.equalToSuperview()
            make.bottom.equalToSuperview()
        }
       
    }
    
    /// 设置事件绑定
    override func setupBindings() {
        // 充值按钮点击事件
        rechargeButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.walletBalanceViewDidTapRecharge(self)
            }
            .store(in: &cancellables)
        
        // 提现按钮点击事件
        withdrawButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.walletBalanceViewDidTapWithdraw(self)
            }
            .store(in: &cancellables)
        
        // 待释放余额信息按钮点击事件
        frozenInfoButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.walletBalanceViewDidTapFrozenInfo(self)
            }
            .store(in: &cancellables)
        
        // 账单明细容器点击事件
        let transactionTapGesture = UITapGestureRecognizer()
        transactionContainer.addGestureRecognizer(transactionTapGesture)
        transactionTapGesture.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.walletBalanceViewDidTapTransaction(self)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 更新余额数据
    /// - Parameters:
    ///   - totalBalance: 总余额
    ///   - availableBalance: 可用余额
    ///   - frozenBalance: 待释放余额
    func updateBalanceData(totalBalance: String, availableBalance: String, frozenBalance: String) {
        totalBalanceValueLabel.text = totalBalance
        availableBalanceValueLabel.text = availableBalance
        frozenBalanceValueLabel.text = frozenBalance
    }
}
