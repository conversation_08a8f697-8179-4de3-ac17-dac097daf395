//
//  CouponItemView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine
import SnapKit

/// 优惠券项视图代理协议
protocol CouponItemViewDelegate: AnyObject {
    /// 优惠券点击事件
    func couponItemViewDidTap(_ view: CouponItemView, coupon: Any)
}

/// 优惠券项视图
class CouponItemView: BaseView {
    
    // MARK: - Properties
    
    /// 代理对象
    weak var delegate: CouponItemViewDelegate?
    
    /// 优惠券数据
    private var couponData: Any?
    
    /// 是否可用状态
    private var isAvailable: Bool = true {
        didSet {
            updateAvailableState()
        }
    }
    
    // MARK: - UI Components
    
    /// 主容器视图
    private lazy var containerView = UIView()
    
    /// 左侧金额区域
    private lazy var amountBackgroundView = UIView().then {
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
        $0.backgroundColor = .white
    }
    
    /// 金额标签
    private lazy var amountLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        $0.textColor = UIColor(hexString: "F12D24", transparency: 1)
        $0.textAlignment = .center
    }
    
    /// 货币符号标签
    private lazy var currencyLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = UIColor(hexString: "F12D24", transparency: 1)
        $0.text = "¥"
    }
    
    /// 使用条件标签
    private lazy var conditionLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.textColor = UIColor(hexString: "F12D24", transparency: 1)
        $0.numberOfLines = 1
    }
    
    /// 右侧信息区域
    private lazy var rightBackgroundView = UIView().then {
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
        $0.backgroundColor = .white
    }
    
    /// 标题标签
    private lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 1)
        $0.numberOfLines = 1
    }
    
    /// 过期时间标签
    private lazy var expireLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.textColor = UIColor(hexString: "#F12D24")
        $0.backgroundColor = UIColor(hexString: "#F12D24", transparency: 0.08)
        $0.numberOfLines = 1
        $0.setPadding(5)
        $0.layer.cornerRadius = 4
        $0.layer.masksToBounds = true
    }
    
    /// 选择按钮
    private lazy var selectButton = UIButton(type: .custom).then {
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
        $0.isUserInteractionEnabled = false
    }
    
    /// 不可用遮罩
    private lazy var unavailableMaskView = UIView().then {
        $0.backgroundColor = UIColor.white.withAlphaComponent(0.7)
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
        $0.isHidden = true
    }
    
    // MARK: - UI Configuration
    
    /// UI配置 - BaseView会在init时自动调用
    override func configUI() {
        backgroundColor = .clear
        
        // 添加子视图
        addSubview(containerView)
        containerView.addSubview(amountBackgroundView)
        containerView.addSubview(rightBackgroundView)
        containerView.addSubview(unavailableMaskView)
        
        rightBackgroundView.addSubview(titleLabel)
        rightBackgroundView.addSubview(expireLabel)
        rightBackgroundView.addSubview(selectButton)
        amountBackgroundView.addSubview(conditionLabel)
        amountBackgroundView.addSubview(currencyLabel)
        amountBackgroundView.addSubview(amountLabel)
        setupBindings()
    }
    
    /// UI布局 - BaseView会在init时自动调用
    override func configLayout() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(87)
        }
        
        amountBackgroundView.snp.makeConstraints { make in
            make.left.top.equalToSuperview()
            make.bottom.equalTo(-12)
            make.width.equalTo(90)
        }
        
        rightBackgroundView.snp.makeConstraints { make in
            make.top.bottom.equalTo(amountBackgroundView)
            make.right.equalTo(0)
            make.left.equalTo(amountBackgroundView.snp.right)
        }
        
        amountLabel.snp.makeConstraints { make in
            make.top.equalTo(20)
            make.centerX.equalToSuperview()
        }
        
        currencyLabel.snp.makeConstraints { make in
            make.right.equalTo(amountLabel.snp.left).offset(2)
            make.centerY.equalTo(amountLabel)
        }
        
        conditionLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(amountLabel.snp.bottom).offset(6)
        }
        
        selectButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.top.equalToSuperview().offset(20)
        }
        
        expireLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.height.equalTo(20)
        }
        
        unavailableMaskView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(-12)
        }
    }
    
    /// 设置事件绑定 - 需要手动调用
    override func setupBindings() {
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer()
        containerView.addGestureRecognizer(tapGesture)
        
        tapGesture.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let couponData = self.couponData else { return }
                self.delegate?.couponItemViewDidTap(self, coupon: couponData)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 配置CouponItemModel数据
    /// - Parameters:
    ///   - coupon: CouponItemModel数据
    ///   - available: 是否可用
    func configure(with coupon: CouponItemModel, available: Bool = true) {
        self.couponData = coupon
        self.isAvailable = available
        
        // 设置金额
        amountLabel.text = coupon.amount
        
        // 设置标题
        titleLabel.text = coupon.title
        
        // 设置使用条件
        if !coupon.min_spend.isEmpty && coupon.min_spend != "0" {
            conditionLabel.text = "满¥\(coupon.min_spend)可用"
        } else {
            conditionLabel.text = "无门槛"
        }
        
        // 设置过期时间
        expireLabel.text = formatExpireTime(coupon.expire_at)
    }
    
    /// 配置WalletCouponModel数据
    /// - Parameter coupon: WalletCouponModel数据
    func configure(with coupon: WalletCouponModel) {
        self.couponData = coupon
        self.isAvailable = coupon.isAvailable
        
        // 设置金额
        amountLabel.text = coupon.formattedAmount
        
        // 设置标题
        titleLabel.text = coupon.title
        
        // 设置使用条件
        if coupon.min_spend > 0 {
            conditionLabel.text = "满¥\(coupon.formattedMinSpend)可用"
        } else {
            conditionLabel.text = "无门槛"
        }
        
        // 设置过期时间
        expireLabel.text = formatExpireTime(coupon.expire_at)
    }
    
    /// 设置选中状态
    /// - Parameter selected: 是否选中
    func setSelected(_ selected: Bool) {
        selectButton.isSelected = selected
    }
    
    // MARK: - Private Methods
    
    /// 更新可用状态
    private func updateAvailableState() {
        unavailableMaskView.isHidden = isAvailable
        selectButton.isHidden = !isAvailable
    }
    
    /// 格式化过期时间
    /// - Parameter timeString: 时间字符串
    /// - Returns: 格式化后的时间字符串
    private func formatExpireTime(_ timeString: String) -> String {
        if timeString.isEmpty {
            return ""
        }
        
        // 简单处理，实际项目中可能需要更复杂的时间格式化
        if timeString.contains("今日到期") {
            return "今日到期"
        } else {
            return timeString + "到期"
        }
    }
}
