//
//  WalletDrawMonthHeaderView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine

/// 提现明细月份分组头部视图代理
protocol WalletDrawMonthHeaderViewDelegate: AnyObject {
    /// 月份按钮点击事件
    func drawMonthHeaderView(_ view: WalletDrawMonthHeaderView, didTapMonthButton month: String)
}

/// 提现明细月份分组头部视图
class WalletDrawMonthHeaderView: UITableViewHeaderFooterView {
    
    // MARK: - Properties
    
    static let identifier = "WalletDrawMonthHeaderView"
    
    /// 代理对象
    weak var delegate: WalletDrawMonthHeaderViewDelegate?
    
    /// 当前月份数据
    private var monthData: WithdrawMonthGroup?
    
    /// 订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - UI Components
    
    /// 主容器
    private lazy var containerView = UIView().then {
        $0.backgroundColor = color_F6F8F9
    }
    
    /// 月份按钮
    private lazy var monthButton = BaseButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.imagePosition = .right
        $0.setImage(UIImage(named: "prodcut_order_bottomarrow"), for: .normal)
    }
    
    /// 提现次数标签
    private lazy var countLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .light)
        $0.textColor = color_2B2C2F80
        $0.text = "提现次数"
    }
    
    /// 提现次数数值标签
    private lazy var countValueLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .light)
        $0.textColor = color_2B2C2F80
    }
    
    // MARK: - 初始化
    
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupUI()
        setupBindings()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        contentView.addSubview(containerView)
        containerView.addSubview(monthButton)
        containerView.addSubview(countLabel)
        containerView.addSubview(countValueLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        monthButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalTo(12)
            make.bottom.equalTo(-12)
            make.width.greaterThanOrEqualTo(80)
        }
        
        countValueLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
        }
        
        countLabel.snp.makeConstraints { make in
            make.right.equalTo(countValueLabel.snp.left).offset(-4)
            make.centerY.equalToSuperview()
        }
    }
    
    // MARK: - Bindings
    
    private func setupBindings() {
        // 月份按钮点击事件
        monthButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let monthData = self.monthData else { return }
                self.delegate?.drawMonthHeaderView(self, didTapMonthButton: monthData.month)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 配置头部视图数据
    /// - Parameter monthData: 月份数据
    func configure(with monthData: WithdrawMonthGroup) {
        self.monthData = monthData

        monthButton.setTitle(monthData.month, for: .normal)
        countValueLabel.text = "\(monthData.total_count)次"
    }
}
