//
//  WalletPaymentMethodView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine
import SnapKit

/// 支付方式类型
enum WalletPaymentMethodType: Int, CaseIterable {
    case alipay = 0
    case wechat = 1
    
    var title: String {
        switch self {
        case .alipay:
            return "支付宝支付"
        case .wechat:
            return "微信支付"
        }
    }
    
    var icon: UIImage? {
        switch self {
        case .alipay:
            return UIImage(systemName: "creditcard.circle.fill")?.withTintColor(UIColor(hexString: "1677FF")!, renderingMode: .alwaysOriginal)
        case .wechat:
            return UIImage(systemName: "message.circle.fill")?.withTintColor(UIColor(hexString: "07C160")!, renderingMode: .alwaysOriginal)
        }
    }
}

/// 支付方式选择视图代理协议
protocol WalletPaymentMethodViewDelegate: AnyObject {
    /// 支付方式选择改变
    func walletPaymentMethodView(_ view: WalletPaymentMethodView, didSelectMethod method: WalletPaymentMethodType)
}

/// 支付方式选择视图
class WalletPaymentMethodView: BaseView {
    
    // MARK: - Properties
    
    /// 代理对象
    weak var delegate: WalletPaymentMethodViewDelegate?
    
    /// 当前选中的支付方式
    private var selectedMethod: WalletPaymentMethodType = .alipay {
        didSet {
            updateSelection()
        }
    }
    
    /// 标题文本
    var titleText: String = "选择提现到" {
        didSet {
            titleLabel.text = titleText
        }
    }
    
    // MARK: - UI Components
    
    /// 标题标签
    private lazy var titleLabel = UILabel().then {
        $0.text = titleText
        $0.textColor = color_2B2C2F80
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
    }
    
    /// 支付方式容器
    private lazy var methodsContainer = UIView().then{
        $0.backgroundColor = .white
        $0.layer.masksToBounds = true
        $0.layer.cornerRadius = 16
    }
    
    /// 支付宝选项
    private lazy var alipayOptionView = createPaymentOptionView(for: .alipay)
    
    /// 微信选项
    private lazy var wechatOptionView = createPaymentOptionView(for: .wechat)
    
    // MARK: - UI Configuration
    
    /// UI配置 - BaseView会在init时自动调用
    override func configUI() {
        backgroundColor = .clear
        
        // 添加子视图
        addSubview(titleLabel)
        addSubview(methodsContainer)
        
        methodsContainer.addSubview(alipayOptionView)
        methodsContainer.addSubview(wechatOptionView)
    }
    
    /// UI布局 - BaseView会在init时自动调用
    override func configLayout() {
        // 标题标签布局
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.left.equalTo(12)
            make.right.equalToSuperview().offset(-12)
        }
        
        // 支付方式容器布局
        methodsContainer.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(12)
            make.bottom.equalToSuperview().offset(-12)
        }
        
        // 支付宝选项布局
        alipayOptionView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(45)
        }
        
        // 微信选项布局
        wechatOptionView.snp.makeConstraints { make in
            make.top.equalTo(alipayOptionView.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(45)
            make.bottom.equalToSuperview()
        }
    }
    
    /// 设置事件绑定 - 需要手动调用
    override func setupBindings() {
        // 支付宝选项点击事件
        let alipayTapGesture = UITapGestureRecognizer()
        alipayOptionView.addGestureRecognizer(alipayTapGesture)
        alipayTapGesture.tapPublisher
            .sink { [weak self] _ in
                self?.selectMethod(.alipay)
            }
            .store(in: &cancellables)
        
        // 微信选项点击事件
        let wechatTapGesture = UITapGestureRecognizer()
        wechatOptionView.addGestureRecognizer(wechatTapGesture)
        wechatTapGesture.tapPublisher
            .sink { [weak self] _ in
                self?.selectMethod(.wechat)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 设置选中的支付方式
    /// - Parameter method: 支付方式类型
    func setSelectedMethod(_ method: WalletPaymentMethodType) {
        selectedMethod = method
    }
    
    /// 获取当前选中的支付方式
    /// - Returns: 当前选中的支付方式
    func getSelectedMethod() -> WalletPaymentMethodType {
        return selectedMethod
    }
    
    // MARK: - Private Methods
    
    /// 创建支付选项视图
    /// - Parameter method: 支付方式类型
    /// - Returns: 支付选项视图
    private func createPaymentOptionView(for method: WalletPaymentMethodType) -> UIView {
        let containerView = UIView()
        let iconImageView = UIImageView().then {
            $0.image = method.icon
            $0.contentMode = .scaleAspectFit
        }
        
        let titleLabel = UILabel().then {
            $0.text = method.title
            $0.textColor = color_2B2C2F
            $0.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        }
        
        let radioButton = UIButton(type: .custom).then {
            $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
            $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
            $0.isUserInteractionEnabled = false
        }
        
        containerView.addSubview(iconImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(radioButton)
        
        // 设置约束
        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(12)
            make.centerY.equalToSuperview()
            make.right.lessThanOrEqualTo(radioButton.snp.left).offset(-12)
        }
        
        radioButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(14)
        }
        
        // 存储radioButton引用，用于后续更新状态
        containerView.tag = method.rawValue
        
        return containerView
    }
    
    /// 选择支付方式
    /// - Parameter method: 支付方式类型
    private func selectMethod(_ method: WalletPaymentMethodType) {
        selectedMethod = method
        delegate?.walletPaymentMethodView(self, didSelectMethod: method)
    }
    
    /// 更新选择状态
    private func updateSelection() {
        // 更新支付宝选项状态
        if let radioButton = alipayOptionView.subviews.last as? UIButton {
            radioButton.isSelected = (selectedMethod == .alipay)
        }
        
        // 更新微信选项状态
        if let radioButton = wechatOptionView.subviews.last as? UIButton {
            radioButton.isSelected = (selectedMethod == .wechat)
        }
    }
}
