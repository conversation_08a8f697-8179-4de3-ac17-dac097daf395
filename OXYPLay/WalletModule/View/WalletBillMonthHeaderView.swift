//
//  WalletBillMonthHeaderView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine

/// 月份分组头部视图代理
protocol WalletBillMonthHeaderViewDelegate: AnyObject {
    /// 月份按钮点击事件
    func monthHeaderView(_ view: WalletBillMonthHeaderView, didTapMonthButton month: String)
}

/// 月份分组头部视图
class WalletBillMonthHeaderView: UITableViewHeaderFooterView {
    
    // MARK: - Properties
    
    static let identifier = "WalletBillMonthHeaderView"
    
    /// 代理对象
    weak var delegate: WalletBillMonthHeaderViewDelegate?
    
    /// 当前月份数据
    private var monthData: MonthlyBillSection?
    
    /// 订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - UI Components
    
    /// 主容器
    private lazy var containerView = UIView().then {
        $0.backgroundColor = color_F6F8F9
    }
    
    /// 月份按钮
    private lazy var monthButton = BaseButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.imagePosition = .right
        $0.setImage(UIImage(named: "prodcut_order_bottomarrow"), for: .normal)
    }
    
    /// 收入标签
    private lazy var incomeLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .light)
        $0.textColor = color_2B2C2F80
        $0.text = "收入"
    }
    
    /// 收入金额标签
    private lazy var incomeAmountLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .light)
        $0.textColor = color_2B2C2F80
    }
    
    /// 支出标签
    private lazy var expenseLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .light)
        $0.textColor = color_2B2C2F80
        $0.text = "支出"
    }
    
    /// 支出金额标签
    private lazy var expenseAmountLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .light)
        $0.textColor = color_2B2C2F80
    }
    
    // MARK: - 初始化
    
    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupUI()
        setupBindings()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        contentView.addSubview(containerView)
        containerView.addSubview(monthButton)
        containerView.addSubview(incomeLabel)
        containerView.addSubview(incomeAmountLabel)
        containerView.addSubview(expenseLabel)
        containerView.addSubview(expenseAmountLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        monthButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalTo(12)
            make.bottom.equalTo(-12)
            make.width.greaterThanOrEqualTo(80)
        }
        
        expenseAmountLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
        }
        
        expenseLabel.snp.makeConstraints { make in
            make.right.equalTo(expenseAmountLabel.snp.left).offset(-4)
            make.centerY.equalToSuperview()
        }
        
        incomeAmountLabel.snp.makeConstraints { make in
            make.right.equalTo(expenseLabel.snp.left).offset(-12)
            make.centerY.equalToSuperview()
        }
        
        incomeLabel.snp.makeConstraints { make in
            make.right.equalTo(incomeAmountLabel.snp.left).offset(-4)
            make.centerY.equalToSuperview()
        }
    }
    
    // MARK: - Bindings
    
    private func setupBindings() {
        // 月份按钮点击事件
        monthButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let monthData = self.monthData else { return }
                self.delegate?.monthHeaderView(self, didTapMonthButton: monthData.month)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 配置头部视图数据
    /// - Parameter monthData: 月份数据
    func configure(with monthData: MonthlyBillSection) {
        self.monthData = monthData

        monthButton.setTitle(monthData.displayMonth, for: .normal)
        incomeAmountLabel.text = "¥\(monthData.incomeTotal)"
        expenseAmountLabel.text = "¥\(monthData.expenseTotal)"
    }
}
