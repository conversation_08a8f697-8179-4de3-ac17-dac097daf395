//
//  WalletDrawDetailCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit

/// 提现明细Cell
class WalletDrawDetailCell: UITableViewCell {
    
    // MARK: - Properties
    
    static let identifier = "WalletDrawDetailCell"
    
    // MARK: - UI Components
    
    /// 主容器
    private lazy var containerView = UIView().then {
        $0.backgroundColor = color_FFFFFF
    }
    
    /// 提现类型标签（固定显示"谁谁谁"）
    private lazy var typeLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textColor = color_2B2C2F
        $0.text = "谁谁谁"
    }
    
    /// 提现ID标签
    private lazy var idLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .light)
        $0.textColor = color_2B2C2F80
    }
    
    /// 状态标签
    private lazy var statusLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 10, weight: .regular)
        $0.layer.cornerRadius = 10
        $0.layer.masksToBounds = true
        $0.textAlignment = .center
        $0.setHorizontalPadding(4)
    }
    
    /// 时间描述标签（如"申请时间"、"打款时间"等）
    private lazy var timeDescLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .light)
        $0.textColor = color_2B2C2F80
    }
    
    /// 时间标签
    private lazy var timeLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .light)
        $0.textColor = color_2B2C2F80
    }
    
    /// 金额标签
    private lazy var amountLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textAlignment = .right
        $0.textColor = color_2B2C2F
    }

    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.addSubview(typeLabel)
        containerView.addSubview(idLabel)
        containerView.addSubview(statusLabel)
        containerView.addSubview(timeDescLabel)
        containerView.addSubview(timeLabel)
        containerView.addSubview(amountLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        typeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(12)
        }
        
        idLabel.snp.makeConstraints { make in
            make.top.equalTo(typeLabel)
            make.left.equalTo(typeLabel.snp.right).offset(8)
        }
        
        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(typeLabel)
            make.right.lessThanOrEqualTo(amountLabel.snp.left).offset(-12)
            make.left.equalTo(idLabel.snp.right).offset(12)
            make.height.equalTo(20)
        }
        
        timeDescLabel.snp.makeConstraints { make in
            make.top.equalTo(typeLabel.snp.bottom).offset(8)
            make.left.equalTo(typeLabel)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(timeDescLabel)
            make.left.equalTo(timeDescLabel.snp.right).offset(4)
            make.right.lessThanOrEqualTo(amountLabel.snp.left).offset(-12)
            make.bottom.equalToSuperview().offset(-0)
        }
        
        amountLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-12)
            make.width.greaterThanOrEqualTo(80)
        }
    }
    
    // MARK: - Public Methods
    
    /// 配置Cell数据
    /// - Parameter record: 提现记录
    func configure(with record: WithdrawRecord) {
        idLabel.text = "ID:\(record.id)"
        timeDescLabel.text = record.time_label
        timeLabel.text = record.time
        amountLabel.text = "¥\(record.amount)"
        
        // 设置状态标签
        statusLabel.text = record.status_text
        statusLabel.backgroundColor = UIColor(hexString: record.status_color)?.withAlphaComponent(0.1)
        statusLabel.textColor = UIColor(hexString: record.status_color)
    }
}
