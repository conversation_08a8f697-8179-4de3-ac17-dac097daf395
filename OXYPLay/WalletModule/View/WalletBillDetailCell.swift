//
//  WalletBillDetailCell.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/26.
//

import UIKit

/// 账单明细Cell
class WalletBillDetailCell: UITableViewCell {
    
    // MARK: - Properties
    
    static let identifier = "WalletBillDetailCell"
    
    // MARK: - UI Components
    
    /// 主容器
    private lazy var containerView = UIView().then {
        $0.backgroundColor = color_FFFFFF
    }
    
    /// 交易类型标签
    private lazy var typeLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textColor = color_2B2C2F
    }
    
    /// 交易时间标签
    private lazy var timeLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .light)
        $0.textColor = color_2B2C2F80
    }
    
    /// 金额标签
    private lazy var amountLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textAlignment = .right

    }

    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.addSubview(typeLabel)
        containerView.addSubview(timeLabel)
        containerView.addSubview(amountLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        typeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(12)
            make.right.lessThanOrEqualTo(amountLabel.snp.left).offset(-12)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(typeLabel.snp.bottom).offset(8)
            make.left.equalTo(typeLabel)
            make.right.lessThanOrEqualTo(amountLabel.snp.left).offset(-12)
            make.bottom.equalToSuperview().offset(0)
        }
        
        amountLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-12)
            make.width.greaterThanOrEqualTo(80)
        }
    }
    
    // MARK: - Public Methods
    
    /// 配置Cell数据
    /// - Parameter record: 交易记录
    func configure(with record: TransactionRecord) {
        typeLabel.text = record.remark
        timeLabel.text = record.display_date
        amountLabel.text = record.display_amount
        
        // 设置金额颜色
        if record.isIncome {
            amountLabel.textColor = color_FF8C00 // 收入用橙色
        } else {
            amountLabel.textColor = color_2B2C2F // 支出用默认色
        }
    }
}
