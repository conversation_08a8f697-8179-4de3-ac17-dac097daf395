//
//  WalletAmountInputView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine
import SnapKit

/// 金额输入视图代理协议
protocol WalletAmountInputViewDelegate: AnyObject {
    /// 金额输入改变
    func walletAmountInputView(_ view: WalletAmountInputView, didChangeAmount amount: String)
    /// 全部提现按钮点击
    func walletAmountInputViewDidTapAllAmount(_ view: WalletAmountInputView)
}

/// 金额输入视图
class WalletAmountInputView: BaseView {
    
    // MARK: - Properties
    
    /// 代理对象
    weak var delegate: WalletAmountInputViewDelegate?
    
    /// 标题文本
    var titleText: String = "提现金额" {
        didSet {
            titleLabel.text = titleText
        }
    }
    
    /// 可用余额文本
    var availableAmountText: String = "" {
        didSet {
            availableAmountLabel.text = availableAmountText
        }
    }
    
    /// 是否显示全部提现按钮
    var showAllAmountButton: Bool = true {
        didSet {
            allAmountButton.isHidden = !showAllAmountButton
        }
    }
    
    // MARK: - UI Components
    
    /// 标题标签
    private lazy var titleLabel = UILabel().then {
        $0.text = titleText
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
    }
    
    /// 金额输入容器
    private lazy var amountContainer = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
    }
    
    /// 货币符号标签
    private lazy var currencyLabel = UILabel().then {
        $0.text = "¥"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 24, weight: .regular)
    }
    
    /// 金额输入框
    private lazy var amountTextField = UITextField().then {
        $0.placeholder = "0.00"
        $0.textColor = color_2B2C2F
        $0.font = UIFont.systemFont(ofSize: 24, weight: .regular)
        $0.keyboardType = .decimalPad
        $0.borderStyle = .none
        $0.textAlignment = .left
    }
    
    /// 可用余额标签
    private lazy var availableAmountLabel = UILabel().then {
        $0.textColor = color_2B2C2F48
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
    }
    
    /// 全部提现按钮
    private lazy var allAmountButton = UIButton(type: .system).then {
        $0.setTitle("全部提现", for: .normal)
        $0.setTitleColor(color_blue, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
    }
    
    // MARK: - UI Configuration
    
    /// UI配置 - BaseView会在init时自动调用
    override func configUI() {
        backgroundColor = .clear
        
        // 添加子视图
        addSubview(titleLabel)
        addSubview(amountContainer)
        addSubview(availableAmountLabel)
        addSubview(allAmountButton)
        
        amountContainer.addSubview(currencyLabel)
        amountContainer.addSubview(amountTextField)
    }
    
    /// UI布局 - BaseView会在init时自动调用
    override func configLayout() {
        // 标题标签布局
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.top.equalTo(0)
        }
        
        // 金额输入容器布局
        amountContainer.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(80)
        }
        
        // 货币符号标签布局
        currencyLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
        }
        
        // 金额输入框布局
        amountTextField.snp.makeConstraints { make in
            make.left.equalTo(currencyLabel.snp.right).offset(8)
            make.right.lessThanOrEqualTo(-20)
            make.centerY.equalToSuperview()
            make.height.equalTo(40)
        }
        
        // 可用余额标签布局
        availableAmountLabel.snp.makeConstraints { make in
            make.top.equalTo(amountContainer.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(12)
        }
        
        // 全部提现按钮布局
        allAmountButton.snp.makeConstraints { make in
            make.centerY.equalTo(availableAmountLabel)
            make.left.equalTo(availableAmountLabel.snp.right).offset(12)
            make.bottom.equalToSuperview().offset(-12)
        }
    }
    
    /// 设置事件绑定 - 需要手动调用
    override func setupBindings() {
        // 金额输入框文本改变事件
        amountTextField.textPublisher
            .sink { [weak self] text in
                guard let self = self else { return }
                self.delegate?.walletAmountInputView(self, didChangeAmount: text ?? "")
            }
            .store(in: &cancellables)
        
        // 全部提现按钮点击事件
        allAmountButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.delegate?.walletAmountInputViewDidTapAllAmount(self)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 设置金额
    /// - Parameter amount: 金额字符串
    func setAmount(_ amount: String) {
        amountTextField.text = amount
    }
    
    /// 获取当前输入的金额
    /// - Returns: 金额字符串
    func getAmount() -> String {
        return amountTextField.text ?? ""
    }
    
    /// 清空金额输入
    func clearAmount() {
        amountTextField.text = ""
    }
    
    /// 设置输入框为第一响应者
    func becomeFirstResponder() {
        amountTextField.becomeFirstResponder()
    }
    
    /// 取消第一响应者状态
    func resignFirstResponder() {
        amountTextField.resignFirstResponder()
    }
    
    /// 设置可用余额文本和全部提现按钮显示状态
    /// - Parameters:
    ///   - availableAmount: 可用余额文本
    ///   - showAllButton: 是否显示全部提现按钮
    func configureAvailableAmount(_ availableAmount: String, showAllButton: Bool = true) {
        availableAmountText = availableAmount
        showAllAmountButton = showAllButton
    }
}
