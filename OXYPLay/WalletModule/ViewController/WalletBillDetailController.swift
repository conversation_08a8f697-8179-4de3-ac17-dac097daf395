//
//  WalletBillDetailController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine

/// 账单明细控制器
class WalletBillDetailController: BaseViewController {
    
    // MARK: - Properties
    
    /// ViewModel
    private let viewModel = WalletBillDetailViewModel()
    
    // MARK: - UI Components
    
    /// 顶部账单类型选择按钮
    private lazy var billTypeButton = BaseButton().then {
        $0.setTitle("全部账单", for: .normal)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.backgroundColor = color_FFFFFF
        $0.layer.cornerRadius = 18
        $0.imagePosition = .right
        $0.setImage(UIImage(named: "prodcut_order_bottomarrow_back"), for: .normal)
        $0.horizontalPadding = 8
    }
    
    /// 表格视图
    private lazy var tableView = UITableView(frame: .zero, style: .grouped).then {
        $0.backgroundColor = color_F6F8F9
        $0.separatorStyle = .none
        $0.showsVerticalScrollIndicator = false
        $0.delegate = self
        $0.dataSource = self
        
        // 注册Cell和HeaderView
        $0.register(WalletBillDetailCell.self, forCellReuseIdentifier: WalletBillDetailCell.identifier)
        $0.register(WalletBillMonthHeaderView.self, forHeaderFooterViewReuseIdentifier: WalletBillMonthHeaderView.identifier)
        
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configLayout()
        setupBindings()
        setupNavigationBar()
    }
    
    // MARK: - UI Configuration
    
    override func configUI() {
        view.backgroundColor = color_F6F8F9
        
        view.addSubview(billTypeButton)
        view.addSubview(tableView)
    }
    
    override func configLayout() {
        billTypeButton.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(16)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(36)
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(billTypeButton.snp.bottom).offset(12)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    /// 设置导航栏
    private func setupNavigationBar() {
        title = "账单明细"

        // 添加自定义提现明细按钮
        let detailButton = UIButton(type: .system).then {
            $0.setTitle("提现明细", for: .normal)
            $0.setTitleColor(color_2B2C2F, for: .normal)
            $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .regular)
            $0.sizeToFit()
        }

        detailButton.tapPublisher
            .sink { [weak self] _ in
                self?.showWithdrawDetail()
            }
            .store(in: &cancellables)

        navigationItem.rightBarButtonItem = UIBarButtonItem(customView: detailButton)
    }
    
    // MARK: - Bindings
    
    override func setupBindings() {
        // 设置刷新控件
        setupRefresh(for: tableView, with: viewModel)
        
        // 监听账单数据变化
        viewModel.$monthlyBillData
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)
        
        // 监听账单类型显示文本变化
        viewModel.$billTypeDisplayText
            .receive(on: DispatchQueue.main)
            .sink { [weak self] text in
                self?.billTypeButton.setTitle(text, for: .normal)
            }
            .store(in: &cancellables)
        
        // 账单类型选择按钮点击
        billTypeButton.tapPublisher
            .sink { [weak self] _ in
                self?.showBillTypeSelector()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Private Methods
    
    /// 显示账单类型选择器
    private func showBillTypeSelector() {
        let typeSelectController = WalletBillTypeSelectController()
        typeSelectController.setSelectedType(viewModel.selectedBillType)
        
        // 监听选择完成事件
        typeSelectController.selectionCompletedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] selectedType in
                self?.viewModel.updateBillType(selectedType)
            }
            .store(in: &cancellables)
        
        customPresent(typeSelectController, animated: true)
    }
    
    /// 显示提现明细
    private func showWithdrawDetail() {
        let drawDetailController = WalletDrawDetailController()
        pushVc(drawDetailController, animated: true)
    }
}

// MARK: - UITableViewDataSource

extension WalletBillDetailController: UITableViewDataSource {

    func numberOfSections(in tableView: UITableView) -> Int {
        return viewModel.monthlyBillData.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        let monthData = viewModel.monthlyBillData[section]
        return monthData.records.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: WalletBillDetailCell.identifier, for: indexPath) as! WalletBillDetailCell

        let monthData = viewModel.monthlyBillData[indexPath.section]
        let record = monthData.records[indexPath.row]
        cell.configure(with: record)

        return cell
    }
}

// MARK: - UITableViewDelegate

extension WalletBillDetailController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: WalletBillMonthHeaderView.identifier) as! WalletBillMonthHeaderView

        let monthData = viewModel.monthlyBillData[section]
        headerView.configure(with: monthData)
        headerView.delegate = self

        return headerView
    }

    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        let footerView = UIView()
        footerView.backgroundColor = .white
        return footerView
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 60
    }
    func tableView(_ tableView: UITableView, estimatedHeightForHeaderInSection section: Int) -> CGFloat {
        return 60
    }
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return UITableView.automaticDimension
    }
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 12
    }
}

// MARK: - WalletBillMonthHeaderViewDelegate

extension WalletBillDetailController: WalletBillMonthHeaderViewDelegate {

    func monthHeaderView(_ view: WalletBillMonthHeaderView, didTapMonthButton month: String) {
        // 显示日期选择器
        showDateSelector(currentMonth: month)
    }

    /// 显示日期选择器
    private func showDateSelector(currentMonth: String) {
        let dateSelectController = WalletDateSelectController(currentMonth: currentMonth)

        // 监听选择完成事件
        dateSelectController.selectionCompletedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] selectedMonth in
                self?.viewModel.jumpToMonth(selectedMonth)
            }
            .store(in: &cancellables)

        customPresent(dateSelectController, animated: true)
    }
}
