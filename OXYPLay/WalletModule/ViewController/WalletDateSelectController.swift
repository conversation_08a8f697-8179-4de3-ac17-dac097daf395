//
//  WalletDateSelectController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine
import SwiftDate

/// 日期选择控制器
class WalletDateSelectController: BasePresentController {

    // MARK: - Properties

    /// 选择完成回调
    let selectionCompletedPublisher = PassthroughSubject<String, Never>()

    /// 当前选中的日期
    private var selectedDate: Date

    // MARK: - UI Components

    /// 年月选择器
    private lazy var yearMonthPicker = UIPickerView().then {
        $0.delegate = self
        $0.dataSource = self
    }

    /// 可选择的年份列表
    private var availableYears: [Int] = []

    /// 可选择的月份列表
    private var availableMonths: [Int] = Array(1...12)

    /// 当前选中的年份
    private var selectedYear: Int = 0

    /// 当前选中的月份
    private var selectedMonth: Int = 0
    
    // MARK: - 初始化

    init(currentMonth: String) {
        // 使用SwiftDate解析当前月份
        if let date = currentMonth.toDate("yyyy-MM")?.date {
            self.selectedDate = date
            self.selectedYear = date.year
            self.selectedMonth = date.month
        } else {
            let now = Date()
            self.selectedDate = now
            self.selectedYear = now.year
            self.selectedMonth = now.month
        }
        super.init()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle

    override var presentationHeight: CGFloat {
        return 400
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupAvailableYears()
        configUI()
        setupBindings()
        setupInitialSelection()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "选择月份", bottomTitle: "确认")

        // 添加年月选择器
        contentView.addSubview(yearMonthPicker)
        yearMonthPicker.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(200)
        }
    }
    
    // MARK: - Private Methods

    /// 设置可选择的年份列表（最近24个月）
    private func setupAvailableYears() {
        let now = Date()
        let earliestDate = now - 24.months

        let currentYear = now.year
        let earliestYear = earliestDate.year

        availableYears = Array(earliestYear...currentYear)
    }

    /// 设置初始选择
    private func setupInitialSelection() {
        // 设置选择器的初始位置
        if let yearIndex = availableYears.firstIndex(of: selectedYear) {
            yearMonthPicker.selectRow(yearIndex, inComponent: 0, animated: false)
        }

        if selectedMonth >= 1 && selectedMonth <= 12 {
            yearMonthPicker.selectRow(selectedMonth - 1, inComponent: 1, animated: false)
        }
    }
    
    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        // 确认按钮
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.confirmSelection()
            }
            .store(in: &cancellables)
    }

    /// 确认选择
    private func confirmSelection() {
        // 构造选中的月份字符串
        let monthString = String(format: "%04d-%02d", selectedYear, selectedMonth)

        // 发送选择完成事件
        selectionCompletedPublisher.send(monthString)

        // 关闭弹窗
        dismiss(animated: true)
    }
}

// MARK: - UIPickerViewDataSource & UIPickerViewDelegate

extension WalletDateSelectController: UIPickerViewDataSource, UIPickerViewDelegate {

    func numberOfComponents(in pickerView: UIPickerView) -> Int {
        return 2 // 年份和月份两列
    }

    func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
        switch component {
        case 0: // 年份列
            return availableYears.count
        case 1: // 月份列
            return availableMonths.count
        default:
            return 0
        }
    }

    func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int) -> String? {
        switch component {
        case 0: // 年份列
            return "\(availableYears[row])年"
        case 1: // 月份列
            return "\(availableMonths[row])月"
        default:
            return nil
        }
    }

    func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
        switch component {
        case 0: // 年份列
            selectedYear = availableYears[row]
        case 1: // 月份列
            selectedMonth = availableMonths[row]
        default:
            break
        }

        // 检查选择的日期是否在有效范围内
        validateSelection()
    }

    /// 验证选择的日期是否在有效范围内
    private func validateSelection() {
        let now = Date()
        let earliestDate = now - 24.months

        // 构造选择的日期
        let selectedDateInRegion = DateInRegion(year: selectedYear, month: selectedMonth, day: 1)
        let selectedDate = selectedDateInRegion.date

        // 如果选择的日期超出范围，自动调整
        if selectedDate > now {
            // 如果选择的日期晚于当前日期，调整为当前月份
            selectedYear = now.year
            selectedMonth = now.month
            updatePickerSelection()
        } else if selectedDate < earliestDate {
            // 如果选择的日期早于最早日期，调整为最早月份
            selectedYear = earliestDate.year
            selectedMonth = earliestDate.month
            updatePickerSelection()
        }
    }

    /// 更新选择器的选择状态
    private func updatePickerSelection() {
        if let yearIndex = availableYears.firstIndex(of: selectedYear) {
            yearMonthPicker.selectRow(yearIndex, inComponent: 0, animated: true)
        }

        if selectedMonth >= 1 && selectedMonth <= 12 {
            yearMonthPicker.selectRow(selectedMonth - 1, inComponent: 1, animated: true)
        }
    }
}
