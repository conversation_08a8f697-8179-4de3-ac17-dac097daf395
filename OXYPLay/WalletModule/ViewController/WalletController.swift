//
//  WalletController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/24.
//

import UIKit
import Combine
import SnapKit

class WalletController: BaseViewController {

    // MARK: - Properties

    /// 钱包ViewModel
    private let viewModel = WalletViewModel()


    // MARK: - UI Components

    /// 主滚动视图
    private lazy var scrollView = UIScrollView().then {
        $0.backgroundColor = .clear
        $0.showsVerticalScrollIndicator = false
    }

    /// 内容容器视图
    private lazy var contentView = UIView()

    /// 钱包余额视图
    private lazy var walletBalanceView = WalletBalanceView().then {
        $0.delegate = self
    }

    /// 优惠券容器
    private lazy var couponContainer = UIStackView().then{
        $0.axis = .vertical
        $0.spacing = 0
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        title = "我的钱包"
        configUI()
        configLayout()
        bindViewModel()
        loadWalletData()
    }

    // MARK: - Private Methods

    /// 设置UI
    override func configUI() {

        // 添加主要视图
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        // 添加钱包余额视图
        contentView.addSubview(walletBalanceView)
        // 添加优惠卷
        contentView.addSubview(couponContainer)
    }

    /// 设置约束
    override func configLayout() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // 钱包余额视图约束
        walletBalanceView.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }

        // 优惠券容器约束
        couponContainer.snp.makeConstraints { make in
            make.top.equalTo(walletBalanceView.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(12)
            make.bottom.equalToSuperview().offset(-12)
        }
    }




    /// 绑定ViewModel
    private func bindViewModel() {
        // 监听钱包概览数据变化
        viewModel.$walletSummary
            .receive(on: DispatchQueue.main)
            .sink { [weak self] summary in
                self?.updateWalletSummaryUI(summary)
            }
            .store(in: &cancellables)

        // 监听提现申请结果
        viewModel.$withdrawRequestResult
            .compactMap { $0 }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] message in
                self?.showSuccessAlert(message)
            }
            .store(in: &cancellables)
    }

    /// 加载钱包数据
    private func loadWalletData() {
        viewModel.fetchWalletSummary()
    }

    /// 更新钱包概览UI
    private func updateWalletSummaryUI(_ summary: WalletSummaryModel?) {
        guard let summary = summary else { return }

        // 更新钱包余额视图数据
        walletBalanceView.updateBalanceData(
            totalBalance: "¥\(summary.formattedBalanceAmount)",
            availableBalance: "¥\(summary.formattedAvailableAmount)",
            frozenBalance: "¥\(summary.formattedFrozenAmount)"
        )

        // 更新优惠券显示
        updateCouponsUI(summary.couponList)
    }

    /// 更新优惠券UI
    private func updateCouponsUI(_ coupons: [WalletCouponModel]) {
        // 清除现有优惠券视图
        couponContainer.subviews.forEach { $0.removeFromSuperview() }

        guard !coupons.isEmpty else {
            // 如果没有优惠券，显示空状态
            let emptyLabel = UILabel().then {
                $0.text = "暂无可用优惠券"
                $0.textColor = color_2B2C2F
                $0.font = UIFont.systemFont(ofSize: 14)
                $0.textAlignment = .center
            }

            couponContainer.addSubview(emptyLabel)
            emptyLabel.snp.makeConstraints { make in
                make.center.equalToSuperview()
                make.height.equalTo(60)
                make.bottom.equalToSuperview()
            }
            return
        }


        for (index, coupon) in coupons.enumerated() {
            let couponView = CouponItemView().then {
                $0.delegate = self
            }
            couponView.configure(with: coupon)
            couponContainer.addArrangedSubview(couponView)
        }
    }

    /// 显示成功提示
    private func showSuccessAlert(_ message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            // 刷新钱包数据
            self?.loadWalletData()
        })
        present(alert, animated: true)
    }

    /// 显示提现申请弹窗
    private func showWithdrawRequestAlert() {
        let alert = UIAlertController(title: "申请提现", message: "请输入提现金额", preferredStyle: .alert)

        alert.addTextField { textField in
            textField.placeholder = "请输入金额"
            textField.keyboardType = .decimalPad
        }

        let confirmAction = UIAlertAction(title: "确认", style: .default) { [weak self] _ in
            guard let textField = alert.textFields?.first,
                  let amountText = textField.text,
                  let amount = Float(amountText),
                  amount > 0 else {
                self?.showErrorAlert("请输入有效的提现金额")
                return
            }

            self?.viewModel.submitWithdrawRequest(amount: amount)
        }

        let cancelAction = UIAlertAction(title: "取消", style: .cancel)

        alert.addAction(confirmAction)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }

    /// 显示错误提示
    private func showErrorAlert(_ message: String) {
        let alert = UIAlertController(title: "错误", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

}

// MARK: - WalletBalanceViewDelegate

extension WalletController: WalletBalanceViewDelegate {
    /// 充值按钮点击事件
    func walletBalanceViewDidTapRecharge(_ view: WalletBalanceView) {
        let rechargeController = WalletRechargeController()
        navigationController?.pushViewController(rechargeController, animated: true)
    }

    /// 提现按钮点击事件
    func walletBalanceViewDidTapWithdraw(_ view: WalletBalanceView) {
        let withdrawController = WalletWithdrawController()
        navigationController?.pushViewController(withdrawController, animated: true)
    }

    /// 待释放余额信息按钮点击事件
    func walletBalanceViewDidTapFrozenInfo(_ view: WalletBalanceView) {
        let alert = UIAlertController(title: "待释放余额说明",
                                    message: "待释放余额是指正在处理中的资金，包括提现申请中的金额等。",
                                    preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    /// 账单明细点击事件
    func walletBalanceViewDidTapTransaction(_ view: WalletBalanceView) {
        let vc = WalletBillDetailController()
        pushVc(vc, animated: true)
    }
}

// MARK: - CouponItemViewDelegate

extension WalletController: CouponItemViewDelegate {
    /// 优惠券点击事件
    func couponItemViewDidTap(_ view: CouponItemView, coupon: Any) {
        if let walletCoupon = coupon as? WalletCouponModel {
            // 处理钱包优惠券点击事件
            print("点击了优惠券: \(walletCoupon.title)")
            // TODO: 可以在这里添加优惠券详情展示或其他操作
        }
    }
}
