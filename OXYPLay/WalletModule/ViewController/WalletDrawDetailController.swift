//
//  WalletDrawDetailController.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/26.
//

import UIKit
import Combine

/// 提现明细控制器
class WalletDrawDetailController: BaseViewController {

    // MARK: - Properties

    /// ViewModel
    private let viewModel = WalletDrawDetailViewModel()

    // MARK: - UI Components

    /// 表格视图
    private lazy var tableView = UITableView(frame: .zero, style: .grouped).then {
        $0.backgroundColor = color_F6F8F9
        $0.separatorStyle = .none
        $0.showsVerticalScrollIndicator = false
        $0.delegate = self
        $0.dataSource = self

        // 注册Cell和HeaderView
        $0.register(WalletDrawDetailCell.self, forCellReuseIdentifier: WalletDrawDetailCell.identifier)
        $0.register(WalletDrawMonthHeaderView.self, forHeaderFooterViewReuseIdentifier: WalletDrawMonthHeaderView.identifier)
    }

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configLayout()
        setupBindings()
        setupNavigationBar()
    }

    // MARK: - UI Configuration

    override func configUI() {
        view.backgroundColor = color_F6F8F9

        view.addSubview(tableView)
    }

    override func configLayout() {
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }

    /// 设置导航栏
    private func setupNavigationBar() {
        title = "提现明细"
    }

    // MARK: - Bindings

    override func setupBindings() {
        // 设置刷新控件
        setupRefresh(for: tableView, with: viewModel)

        // 监听提现数据变化
        viewModel.$monthlyWithdrawData
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.tableView.reloadData()
            }
            .store(in: &cancellables)
    }
}

// MARK: - UITableViewDataSource

extension WalletDrawDetailController: UITableViewDataSource {

    func numberOfSections(in tableView: UITableView) -> Int {
        return viewModel.monthlyWithdrawData.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        let monthData = viewModel.monthlyWithdrawData[section]
        return monthData.items.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: WalletDrawDetailCell.identifier, for: indexPath) as! WalletDrawDetailCell

        let monthData = viewModel.monthlyWithdrawData[indexPath.section]
        let record = monthData.items[indexPath.row]
        cell.configure(with: record)

        return cell
    }
}

// MARK: - UITableViewDelegate

extension WalletDrawDetailController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: WalletDrawMonthHeaderView.identifier) as! WalletDrawMonthHeaderView

        let monthData = viewModel.monthlyWithdrawData[section]
        headerView.configure(with: monthData)
        headerView.delegate = self

        return headerView
    }

    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        let footerView = UIView()
        footerView.backgroundColor = .white
        return footerView
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 12
    }
}

// MARK: - WalletDrawMonthHeaderViewDelegate

extension WalletDrawDetailController: WalletDrawMonthHeaderViewDelegate {

    func drawMonthHeaderView(_ view: WalletDrawMonthHeaderView, didTapMonthButton month: String) {
        // 显示日期选择器
        showDateSelector(currentMonth: month)
    }

    /// 显示日期选择器
    private func showDateSelector(currentMonth: String) {
        let dateSelectController = WalletDateSelectController(currentMonth: currentMonth)

        // 监听选择完成事件
        dateSelectController.selectionCompletedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] selectedMonth in
                self?.viewModel.jumpToMonth(selectedMonth)
            }
            .store(in: &cancellables)

        customPresent(dateSelectController, animated: true)
    }
}
