//
//  WalletBillTypeSelectController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine

/// 账单类型选择控制器
class WalletBillTypeSelectController: BasePresentController {
    
    // MARK: - Properties
    
    /// 选择完成回调
    let selectionCompletedPublisher = PassthroughSubject<String?, Never>()
    
    /// 当前选中的类型
    private var selectedType: String?
    
    // MARK: - UI Components
    
    /// 列表视图
    private lazy var listView = BaseListView().then {
        $0.delegate = self
    }
    
    // MARK: - Lifecycle
    
    override var presentationHeight: CGFloat {
        return 400
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        setupListData()
    }
    
    // MARK: - UI Setup
    
    override func configUI() {
        configView(title: "选择账单类型", bottomTitle: "确认")
        
        // 添加列表视图
        contentView.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Private Methods
    
    /// 设置列表数据
    private func setupListData() {
        let configs = [
            ListItemConfig.singleSelect(
                identifier: "all",
                title: "全部账单",
                isSelected: selectedType == nil
            ),
            ListItemConfig.singleSelect(
                identifier: "recharge",
                title: "充值",
                isSelected: selectedType == "recharge"
            ),
            ListItemConfig.singleSelect(
                identifier: "income",
                title: "收入",
                isSelected: selectedType == "income"
            ),
            ListItemConfig.singleSelect(
                identifier: "refund",
                title: "退款",
                isSelected: selectedType == "refund"
            ),
            ListItemConfig.singleSelect(
                identifier: "withdraw",
                title: "提现",
                isSelected: selectedType == "withdraw"
            )
        ]

        // BaseListView需要二维数组，每个子数组代表一个分组
        listView.setItems([configs])
    }
    
    // MARK: - Bindings
    
    override func setupBindings() {
        super.setupBindings()
        
        // 确认按钮
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.confirmSelection()
            }
            .store(in: &cancellables)
    }
    
    /// 确认选择
    private func confirmSelection() {
        // 发送选择完成事件
        selectionCompletedPublisher.send(selectedType)

        // 关闭弹窗
        dismiss(animated: true)
    }
    
    // MARK: - Public Methods
    
    /// 设置当前选中的类型
    func setSelectedType(_ type: String?) {
        selectedType = type
        if isViewLoaded {
            setupListData()
        }
    }
}

// MARK: - BaseListViewDelegate

extension WalletBillTypeSelectController: BaseListViewDelegate {

    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        // 处理选择逻辑在BaseListView内部已经处理
    }

    func listViewUpdate(_ listView: BaseListView, with data: Any?) {
        // 更新选中的类型
        guard let identifier = data as? String else { return }

        switch identifier {
        case "all":
            selectedType = nil
        case "recharge":
            selectedType = "recharge"
        case "income":
            selectedType = "income"
        case "refund":
            selectedType = "refund"
        case "withdraw":
            selectedType = "withdraw"
        default:
            break
        }
    }
}
