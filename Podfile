# Uncomment the next line to define a global platform for your project
platform :ios, '15.0'
source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'
target 'OXYPLay' do
  use_frameworks!
  pod 'MJRefresh'
  pod 'SwiftyJSON'
  pod 'SwiftDate'
  pod 'JXSegmentedView'
  pod 'SnapKit'
  pod 'CombineCocoa'
  pod 'Kingfisher'
  pod 'Moya/Combine', '~> 15.0'
  pod 'SwifterSwift'
  pod 'MBProgressHUD'
  pod 'ActiveLabel'
  pod 'SmartCodable'
  pod 'Then'
  pod 'EFQRCode'
  pod 'ZLPhotoBrowser'
  pod 'DZNEmptyDataSet'
  #微信官方SDK
  pod 'WechatOpenSDK'
  #支付宝
  pod  'AlipaySDK-iOS'
  #融云
  pod 'RongCloudIM/IMKit'
  target 'OXYPLayTests' do
    inherit! :search_paths
    # Pods for testing
  end
  
  target 'OXYPLayUITests' do
    # Pods for testing
  end

end

# 修复部署目标版本问题
post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      # 设置最低部署目标为15.0，与项目保持一致
      if config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'].to_f < 15.0
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'
      end

      # 修复libarclite问题
      config.build_settings['CLANG_ENABLE_OBJC_ARC'] = 'YES'
      config.build_settings['ENABLE_STRICT_OBJC_MSGSEND'] = 'YES'
    end
  end
end
